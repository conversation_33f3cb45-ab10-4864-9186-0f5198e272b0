# 實踐要點格式轉換工具 - PowerShell版本
# 設定編碼為UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "實踐要點格式轉換工具" -ForegroundColor Cyan  
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 檢查Python是否可用
try {
    $pythonVersion = python --version 2>&1
    Write-Host "[資訊] 找到Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "[錯誤] 找不到Python，請確認已安裝Python 3.6+" -ForegroundColor Red
    Read-Host "按Enter鍵退出"
    exit 1
}

# 檢查輸入文件
$inputFile = "TW_AI_Policy_v5.md"
if (-not (Test-Path $inputFile)) {
    Write-Host "[錯誤] 找不到 $inputFile 文件" -ForegroundColor Red
    Write-Host "請確認文件存在於當前目錄中" -ForegroundColor Yellow
    Read-Host "按Enter鍵退出"
    exit 1
}

Write-Host "[資訊] 找到輸入文件: $inputFile" -ForegroundColor Green
Write-Host ""

# 創建備份
$backupFile = "TW_AI_Policy_v5_backup.md"
try {
    Copy-Item $inputFile $backupFile -Force
    Write-Host "[成功] 備份文件已創建: $backupFile" -ForegroundColor Green
} catch {
    Write-Host "[警告] 無法創建備份文件: $_" -ForegroundColor Yellow
}
Write-Host ""

# 執行轉換
Write-Host "[資訊] 開始轉換實踐要點格式..." -ForegroundColor Blue
try {
    python simple_convert.py
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "[成功] 轉換完成！" -ForegroundColor Green
        Write-Host ""
        Write-Host "文件說明:" -ForegroundColor Cyan
        Write-Host "- 原始文件: TW_AI_Policy_v5.md" -ForegroundColor White
        Write-Host "- 備份文件: TW_AI_Policy_v5_backup.md" -ForegroundColor White
        Write-Host "- 轉換結果: TW_AI_Policy_v5_converted.md" -ForegroundColor White
        Write-Host ""
        Write-Host "請檢查轉換結果，如果滿意可以替換原文件" -ForegroundColor Yellow
        
        # 詢問是否要查看轉換結果
        $response = Read-Host "是否要打開轉換後的文件查看結果？(y/n)"
        if ($response -eq "y" -or $response -eq "Y") {
            try {
                Start-Process "TW_AI_Policy_v5_converted.md"
            } catch {
                Write-Host "無法自動打開文件，請手動查看 TW_AI_Policy_v5_converted.md" -ForegroundColor Yellow
            }
        }
    } else {
        Write-Host ""
        Write-Host "[錯誤] 轉換失敗，請檢查錯誤信息" -ForegroundColor Red
    }
} catch {
    Write-Host "[錯誤] 執行轉換時發生錯誤: $_" -ForegroundColor Red
}

Write-Host ""
Read-Host "按Enter鍵退出"

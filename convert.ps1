# Practice Points Format Converter - PowerShell Version
# Set encoding to UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Practice Points Format Converter" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check if Python is available
try {
    $pythonVersion = python --version 2>&1
    Write-Host "[INFO] Found Python: $pythonVersion" -ForegroundColor Green
}
catch {
    Write-Host "[ERROR] Python not found, please install Python 3.6+" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Check input file
$inputFile = "AI_Policy_Draft\TW_AI_Policy_v5.md"
if (-not (Test-Path $inputFile)) {
    Write-Host "[ERROR] Cannot find file: $inputFile" -ForegroundColor Red
    Write-Host "Please ensure the file exists in current directory" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "[INFO] Found input file: $inputFile" -ForegroundColor Green
Write-Host ""

# Create backup
$backupFile = "TW_AI_Policy_v5_backup.md"
try {
    Copy-Item $inputFile $backupFile -Force
    Write-Host "[SUCCESS] Backup file created: $backupFile" -ForegroundColor Green
}
catch {
    Write-Host "[WARNING] Cannot create backup file: $_" -ForegroundColor Yellow
}
Write-Host ""

# Execute conversion
Write-Host "[INFO] Starting practice points format conversion..." -ForegroundColor Blue
try {
    python simple_convert.py

    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "[SUCCESS] Conversion completed!" -ForegroundColor Green
        Write-Host ""
        Write-Host "File descriptions:" -ForegroundColor Cyan
        Write-Host "- Original file: TW_AI_Policy_v5.md" -ForegroundColor White
        Write-Host "- Backup file: TW_AI_Policy_v5_backup.md" -ForegroundColor White
        Write-Host "- Converted file: TW_AI_Policy_v5_converted.md" -ForegroundColor White
        Write-Host ""
        Write-Host "Please check the conversion result. If satisfied, you can replace the original file." -ForegroundColor Yellow

        # Ask if user wants to view the result
        $response = Read-Host "Do you want to open the converted file to view the result? (y/n)"
        if ($response -eq "y" -or $response -eq "Y") {
            try {
                Start-Process "TW_AI_Policy_v5_converted.md"
            }
            catch {
                Write-Host "Cannot automatically open file, please manually check TW_AI_Policy_v5_converted.md" -ForegroundColor Yellow
            }
        }
    }
    else {
        Write-Host ""
        Write-Host "[ERROR] Conversion failed, please check error messages" -ForegroundColor Red
    }
}
catch {
    Write-Host "[ERROR] Error occurred during conversion: $_" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"

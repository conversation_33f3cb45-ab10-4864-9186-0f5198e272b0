勤業眾信聯合會計師事務所人工智慧政策 v5.1
一、目的
勤業眾信聯合會計師事務所(Deloitte & Touche)及其關係機構（以下簡稱本所），致力於遵循可信賴人工智慧（AI）的原則，建立AI系統治理架構提供政策指導與實務準則，確保AI系統在整個生命週期中，能夠依據負責任的商業實務及相關法規進行識別與管理，並提供合理保證水準。
二、範圍
本政策適用於AI系統在客戶服務及內部營運用途的使用，涵蓋本所全體人員，包括合夥人、專業服務人員及內部服務人員。政策範圍包含AI系統的設計、開發/採購、部署、維運、監控及退役等完整生命週期管理。
其他相關政策（如DPM 1570隱私政策、DPM 1630客戶機密資訊政策、DPM 1420獨立性政策、DPM 2060職業道德政策、PM40資訊安全政策）亦可能適用，應配合本政策一併遵循，確保AI應用的全面合規性。
三、名詞定義
3.1 AI系統
就本政策而言，「AI系統」係指基於機器學習的系統，能夠針對明確或隱含的目標，從接收的輸入資料中推斷如何產生預測、建議或決策等輸出結果，並可能影響實體或虛擬環境的系統。
3.2 AI風險主管
指派的高階主管層級人員，負責AI系統的整體問責管理，包括風險識別、評估、處理及監控等職責。
3.3 AI治理委員會
由相關專業領域代表組成的委員會，負責審核高風險AI專案、監督政策執行，並進行倫理與風險審查。
3.4 高風險AI系統
涉及人力資源決策、客戶信用評估、個人敏感資料處理，或可能對個人權益產生重大影響的AI系統。
3.5 生成式AI
能夠產生文字、圖像、音訊或其他內容的AI系統，包括但不限於大型語言模型、圖像生成模型等。
四、權責
4.1 AI風險主管責任
事務所應指派高階主管層級的AI風險主管（AI Risk Officer），負責AI系統的整體問責管理。AI風險主管職責可併入現有職務，但應明確定義適當的報告路徑與決策權限，確保AI風險管理具備充足的資源配置，包括人力、技術及預算資源。
4.2 高階管理層責任
確保充分的資源投入AI治理體系，建立AI治理文化，推動組織變革，監督政策執行成效，定期檢討改善。高階管理層應承擔AI治理的最終責任，確保政策的有效實施與持續改善。
4.3 專業服務單位責任
識別AI應用需求與機會，評估業務價值，配合執行風險評估，提供業務觀點，確保業務流程符合政策要求，落實執行。專業服務單位應積極參與AI治理活動，確保AI應用符合業務需求與風險控制要求。
4.4 技術團隊責任
負責AI系統的技術實作與維運，執行技術風險評估，確保技術可行性，確保系統安全與效能，建立監控機制。技術團隊應具備足夠的專業能力，確保AI系統的技術品質與安全性。
4.5 法務部門責任
提供法規遵循指導，解釋法律要求，審核合約與協議，確保法律保護，處理法律風險議題，提供專業建議。法務部門應密切關注AI相關法規發展，確保政策與實務的合規性。
4.6 全員合規義務
全體人員均有理解並遵循AI政策的責任，應積極參與相關教育訓練，遵守使用規範，及時回報異常情況。每位員工都是AI治理的重要參與者，應承擔相應的合規責任。
五、內容
5.1 通用治理政策
本節規範適用於全體人員的AI治理基本要求，包括組織架構、教育訓練要求、行為規範等通用性政策，確保所有人員都能理解並遵循AI治理的基本原則。
5.1.1 AI治理委員會
建立AI治理委員會作為AI治理的核心決策機構，負責制定AI策略方向、審核高風險專案、監督政策執行，確保AI應用符合本所價值觀與風險控制要求。
5.1.1.1 AI治理委員會組成
委員會成員應具備相關專業背景與決策權限，能夠從不同角度評估AI應用的風險與機會，確保決策的專業性與全面性。委員會應包含跨領域專業代表，具備充分授權進行AI治理決策，並對高風險AI使用案例實施強制性倫理與風險審查機制。
- 建立AI治理委員會作為核心決策機構
- 委員會成員包含：Risk Leaders、CCO、Privacy Leader、CISO、OGC等專業領域代表
- 針對高風險AI使用案例實施強制倫理與風險審查
- 確保委員會成員具備相關專業背景與決策權限
- 建立跨部門協調與溝通機制
5.1.1.2 倫理與風險審查程序
審查程序應包含技術評估、倫理評估、法規評估等多個面向，確保AI應用的全面合規性與社會責任。建立標準化的AI倫理審查流程與評估標準，定期檢視AI系統的倫理影響，確保AI應用符合 Deloitte 共同價值觀與職業道德標準。
5.1.1.3 委員會職責與權限
委員會應具備充分的決策權限，能夠有效指導AI治理活動，確保政策的有效執行與持續改善。委員會負責制定AI策略方向、審核高風險專案、監督政策執行，並處理重大AI相關事件與爭議，確保AI治理的全面性與有效性。
5.1.1.4 會議機制與決策程序
會議機制應確保決策的透明性與可追溯性，建立有效的溝通與協調機制，促進跨部門合作。建立定期會議制度與標準化決策程序，確保委員會活動的規範性與效率性，並建立完整的會議記錄與追蹤機制。
5.1.2 AI素養與教育訓練
實施AI素養與教育訓練計畫，提升全體人員AI知識，建立分級教育訓練制度，確保不同層級人員都能具備適當的AI知識與技能。
- 基礎訓練確保所有員工都具備基本的AI認知與合規意識，建立全員AI素養基礎。訓練內容涵蓋AI基本概念、政策要點、使用倫理與資料隱私保護意識，為本所AI治理文化奠定基礎。
- 專業訓練提升AI相關人員的專業能力，確保AI應用的技術品質與風險控制能力。深化AI技術知識、風險評估管理技能，並透過最佳實務案例分享，提升專業人員的實務應用能力。
- 管理訓練確保管理層具備適當的AI治理能力，能夠做出正確的決策。強化管理階層對AI治理策略的理解，提升風險管理決策能力，並建立危機處理與法律責任認知。
- 內部宣導營造良好的AI治理文化，促進知識分享與經驗交流。透過多元化的宣導活動，提升全員對AI治理的認知與參與度，建立積極正向的AI治理文化氛圍。
- 外部參與提升本所的AI治理水準，促進產業最佳實務的發展。積極參與產業AI治理倡議與國際交流，學習先進經驗並分享治理成果，提升本所在AI治理領域的影響力與專業聲譽。
5.1.3 紀律處分程序
建立處理AI政策違規行為的紀律程序，確保違規行為得到適當的處理，維護政策的權威性與有效性。
5.1.3.1 違規處理機制
違規處理機制應建立明確的程序與標準，確保處分的公正性與合理性。建立完善的AI政策違規行為紀律程序，定義違規行為分類與相應處分標準，確保處分程序的公正性、一致性與透明度。
5.1.3.2 違規認定標準
5.1.3.2.1 違規類型
違規類型的明確分類有助於確保處分的一致性與合理性。建立違規分類標準，依據違規影響程度與後果嚴重性，制定相應的認定標準與處分措施，確保違規處理的客觀性與公正性。
- 輕微違規：未依程序執行但無重大影響
- 一般違規：違反政策規定造成一定影響
- 嚴重違規：造成重大損失或法律風險
- 重大違規：涉及刑事責任或重大聲譽損害
5.1.3.2.2 調查程序
調查程序應確保調查的客觀性與公正性，保護當事人的合法權益。建立標準化的違規調查程序，確保調查過程的透明度與公正性，並在規定時限內完成調查與責任認定，維護程序正義。
5.1.3.3 處分措施
5.1.3.3.1 行政處分
行政處分應與違規程度相匹配，確保處分的合理性與威嚇效果。建立分級處分制度，依據違規嚴重程度給予相應處分，確保處分措施的公正性與一致性，發揮有效的威嚇與教育作用。
5.1.3.3.2 補救措施
補救措施旨在降低違規影響，防止類似事件再次發生。除行政處分外，應同時採取適當的補救措施，包括損害控制、教育訓練、制度改善等，確保違規事件得到妥善處理並建立預防機制。
5.1.3.4 預防與改善措施
預防與改善措施體現了持續改善的理念，有助於提升政策的有效性。透過系統性的違規原因分析，制定針對性的預防措施，建立違規案例學習機制，持續改善政策與程序，降低違規風險。
5.1.3.5 申訴機制
5.1.3.5.1 申訴程序
申訴機制保護當事人的合法權益，確保處分程序的公正性。建立完善的申訴審議程序，保障當事人申訴權利，確保申訴案件得到公正、及時的處理，維護程序正義與當事人合法權益。
5.1.3.5.2 反報復政策
保護措施鼓勵員工積極參與AI治理，營造良好的治理環境。建立完善的反報復保護機制，保護申訴人與善良檢舉人，禁止任何形式的報復行為，提供必要的法律協助與支援，確保公正透明的審議環境。
5.1.4 負責任使用義務
價值觀一致性是AI治理的核心原則，應將 Deloitte 共同價值觀融入AI應用的各個環節。
5.1.4.1 價值觀一致性
確保AI使用符合 Deloitte 共同價值觀，遵循相關法律法規與職業道德標準，促進AI技術的負責任發展與應用。將本所核心價值觀融入AI應用的各個環節，確保AI技術的使用與組織文化、職業道德標準保持一致。
5.1.4.2 社會責任考量
社會責任考量體現了本所的社會擔當，有助於建立良好的社會形象。積極考量AI應用對社會的影響，避免AI系統產生歧視或偏見，促進AI技術的公平與包容性發展，承擔企業社會責任。
5.1.4.3 可信任AI原則實踐
可信任AI原則是AI治理的重要指導原則，應貫穿AI應用的全生命週期。確保AI系統具備問責性、公平性、隱私性、負責任、穩健可靠、安全保障、透明可解釋等特性，建立可信任的AI應用環境。
5.2 AI系統治理與管理
本節規範AI系統從規劃、設計、開發/採購、部署、維運到退役整個生命週期的管理要求，主要適用於AI系統相關人員，包括技術團隊、專業服務、風險管理人員等。
5.2.1 AI系統清單與分類管理
建立並維護完整的AI系統清單，實施一致的風險分級標準，確保所有AI系統都能得到適當的風險管理與監控。
5.2.1.1 AI系統清單建置
系統清單應作為AI治理的基礎工具，提供全面的AI應用概況，支援風險管理與決策制定。建立並維護完整的AI系統清單/目錄，涵蓋所有使用中的AI系統，包含風險分級、用途說明、負責人員等關鍵資訊，定期更新確保資訊的即時性與準確性。
5.2.1.2 風險分級與分類
風險分級應考量技術風險、營運風險、法規風險、聲譽風險等多個面向，確保評估的全面性與準確性。建立一致的風險分級標準，將AI系統分為高、中、低風險等級，針對高風險AI系統制定詳細的風險緩解計畫，定期檢視分級適當性。
5.2.1.3 AI系統分類標準
5.2.1.3.1 低風險AI系統
此類系統風險較低，但仍需納入管理範圍，確保基本的安全與合規要求。涵蓋基本資料處理與分析工具、簡單的自動化流程等無涉及個人資料或重要決策的應用，採用基礎管理措施即可。
例如：
- 基本資料處理與分析工具
- 簡單的自動化流程
- 無涉及個人資料或重要決策的應用
5.2.1.3.2 中風險AI系統
此類系統需要適度的風險控制措施，確保輸出品質與使用安全。包括客戶服務聊天機器人、內部文件摘要工具、一般性資料分析應用等，需要建立適當的監控與品質控制機制。
例如：
- 客戶服務聊天機器人
- 內部文件摘要工具
- 一般性資料分析應用
5.2.1.3.3 高風險AI系統
此類系統需要嚴格的風險控制措施，包括詳細的風險評估、倫理審查、持續監控等。涵蓋涉及人力資源決策的系統、客戶信用評估或風險分級、涉及個人敏感資料處理的應用等，需要最高標準的管理與控制。
例如：
- 涉及人力資源決策的系統
- 客戶信用評估或風險分級
- 涉及個人敏感資料處理的應用
5.2.1.3.4 不可接受風險AI系統
此類系統原則上禁止使用，如有特殊需求應經過最高層級的審核與核准。包括可能造成人身傷害的系統、違反法律或倫理標準的應用、侵犯基本人權的技術等，需要最嚴格的管制措施。
例如：
- 可能造成人身傷害的系統
- 違反法律或倫理標準的應用
- 侵犯基本人權的技術
5.2.2 AI系統生命週期管理
建立AI系統從規劃、設計、開發/採購、部署、維運到退役的完整生命週期管理流程，確保每個階段都能得到適當的管理與控制。
5.2.2.1 規劃與設計階段
AI系統規劃應明確定義業務需求、技術要求、風險評估與合規要求，建立專案治理架構，確保AI系統設計符合本所政策與法規要求。劃階段應進行初步風險評估，識別潛在的技術、營運、法規與聲譽風險，制定相應的風險緩解策略。
5.2.2.2 開發與採購階段
建立AI系統開發或採購的管理協定，評估供應商的AI系統安全性、可靠性及合規性，確保開發或採購合約包含適當的AI風險管理條款。
供應商評估標準：
- 技術能力：評估技術成熟度與可靠性
- 安全標準：確認資訊安全防護措施
- 合規狀況：檢視法規遵循情形
- 服務品質：評估服務水準與支援能力
開發與採購管理應建立標準化的評估標準與程序，確保決策的專業性與合規性。
5.2.2.3 部署與上線階段
定義AI系統部署的標準作業程序，建立部署前的測試與驗證機制，確保系統符合技術與業務要求後方可上線。
部署階段應包含系統整合測試、使用者接受測試、安全測試與效能測試，確保系統穩定性與安全性。
5.2.2.4 營運與維護階段
建立AI系統維運監控機制，定期檢視AI系統效能與風險狀況，實施持續的系統優化與維護作業。
營運管理要點：
- 效能監控：持續監控系統效能指標
- 偏見檢測：定期檢測演算法偏見
- 安全防護：實施資訊安全防護措施
- 變更管理：制定系統變更管控流程
5.2.2.5 退役與處置階段
制定AI系統退役程序，包括資料備份與清除、系統下線、合約終止與資料返還等作業，確保退役過程符合法規要求。
退役階段應特別注意個人資料的安全處置，確保符合資料保護法規的要求。
5.2.2.6 合約管理
明確定義服務範圍與品質標準，規範資料處理與隱私保護要求，建立服務水準協議(SLA)，制定合約終止與資料返還程序。
合約管理應確保所有AI相關的法律風險都得到適當的控制，保護本所的合法權益。
5.2.3 風險管理與控制
建立系統性的AI風險識別、評估與處理流程，涵蓋技術風險、營運風險、法規風險、聲譽風險等各面向，確保AI應用的安全性與合規性。
5.2.3.1 風險識別與評估
風險識別應採用多元化的方法，包括專家評估、歷史資料分析、情境模擬等，確保風險識別的全面性。建立系統性的風險識別與評估機制，定期進行風險評估，並依據評估結果調整風險管理策略。
5.2.3.2 初步風險評估
初步風險評估應建立標準化的評估表單與流程，確保評估的一致性與效率。所有AI專案在啟動前應完成初步風險評估，涵蓋技術、資料、營運、法規、聲譽等各面向風險，為後續風險管理提供基礎。
包括但不限於：
- 技術風險：演算法偏見、模型準確性、系統穩定性
- 資料風險：資料品質、隱私保護、資料安全
- 營運風險：業務流程影響、人力資源配置
- 法規風險：法律合規性、監管要求
- 聲譽風險：公眾觀感、媒體關注
5.2.3.3 進階風險評估
進階風險評估應採用更嚴格的標準與方法，確保中高風險系統得到充分的風險控制。高風險AI系統應進行詳細風險評估，或委託第三方專業機構執行獨立評估，包含技術稽核與倫理審查，提出具體風險控制建議。
5.2.3.4 風險處理與監控
風險處理應採用多層次的控制措施，包括技術控制、流程控制、人員控制等，確保風險得到有效控制。針對識別的風險制定適當的處理措施，建立持續監控機制，追蹤風險處理措施的有效性，定期向管理階層報告。
5.2.3.5 風險控制措施
5.2.3.5.1 技術控制
技術控制是AI風險管理的核心，應建立完善的技術標準與檢測機制。透過模型驗證、偏見檢測、效能監控、安全防護等技術手段，確保AI系統的技術品質與安全性，降低技術風險對業務的影響。
實施說明：
- 模型驗證：建立模型測試與驗證程序
- 偏見檢測：定期檢測演算法偏見
- 效能監控：持續監控系統效能指標
- 安全防護：實施資訊安全防護措施
5.2.3.5.2 流程控制
流程控制確保AI系統的規範化管理，降低人為錯誤與管理風險。建立標準化的管理流程，包括審核機制、變更管理、文件管理、備援計畫等，確保AI系統的穩定運行與風險控制。
實施說明：
- 審核機制：建立多層級審核程序
- 變更管理：制定系統變更管控流程
- 文件管理：維護完整的技術文件
- 備援計畫：建立系統故障應變機制
5.2.3.5.3 人員控制
人員控制是風險管理的重要環節，應確保所有相關人員具備適當的知識與技能。透過權限管理、教育訓練、行為準則、監督機制等人員控制措施，確保AI系統的安全使用與合規操作。
實施說明：
- 權限管理：實施最小權限原則
- 教育訓練：定期辦理AI相關訓練
- 行為準則：制定AI使用行為規範
- 監督機制：建立內部監督制度
5.2.4 事件管理與應變
建立完善的AI事件管理程序，確保AI系統相關事件能夠得到及時、有效的處理，降低事件影響並從中學習改善。
5.2.4.1 AI事件管理程序
事件管理程序應與現有的IT事件管理體系整合，確保處理流程的一致性與效率。建立或運用現有的事件管理程序，處理AI系統相關事件，定義AI事件的分類標準與處理優先順序，建立完整的事件回報、調查、處理及後續追蹤機制。
5.2.4.2 事件分類標準
事件分類應明確定義各級事件的特徵與處理要求，確保事件能夠得到適當的重視與處理。建立四級事件分類標準，依據事件嚴重程度與影響範圍，制定相應的處理程序與時間要求，確保事件處理的及時性與有效性。
實施說明：
- 第一級：系統故障或效能異常
- 第二級：資料洩露或隱私侵犯
- 第三級：演算法偏見或歧視事件
- 第四級：重大安全事故或法規違反
5.2.4.3 事件應變與復原
應變計畫應考量不同類型事件的特性，制定針對性的應變措施，確保能夠快速有效地控制事件影響。制定AI系統事件的應變計畫與復原程序，定期進行事件應變演練，建立事件學習機制，持續改善事件管理效能。
5.2.4.4 應變程序
應變程序應建立明確的時間要求與責任分工，確保事件處理的及時性與有效性。建立標準化的應變程序，包括立即回應、損害控制、原因調查、改善措施、經驗學習等步驟，確保事件得到妥善處理並建立預防機制。
5.2.5 資料治理與管理
5.2.5.1 資料治理架構
建立AI系統使用資料的治理準則，確保資料來源、資料譜系（Data Lineage）、合約義務及資料治理的合規性，定義資料品質標準與監控機制。
5.2.5.2 資料管理原則
5.2.5.2.1 資料品質
資料品質是AI系統效能的基礎，應建立嚴格的品質標準與檢核機制。確保訓練資料的準確性與完整性，建立資料品質檢核機制，定期更新與維護資料集，記錄資料來源與處理歷程，為AI系統提供高品質的資料基礎。
5.2.5.2.2 資料安全與隱私
資料安全與隱私保護是AI系統治理的核心要素，應建立完善的保護機制與持續的監控評估流程。
5.2.5.2.3 資料隱私保護
資料隱私保護是法規合規的重要要求，應建立完善的隱私保護機制。遵循個人資料保護法規定，實施資料去識別化處理，建立資料存取權限控制，提供資料主體權利保障，確保AI系統處理的資料符合隱私法規要求。
5.2.5.2.4 資料安全措施
資料安全是AI系統安全的重要組成部分，應採用多層次的安全防護措施。實施資料加密保護，建立資料備份與復原機制，監控資料存取行為，防範資料外洩風險。
5.2.5.2.5 監控與評估
建立持續的安全與隱私監控機制，包括資料加密、存取控制及稽核追蹤機制，定期進行資料安全風險評估，確保保護措施的有效性，並及時應對新興威脅與風險。5.2.5.4 資料使用規範
5.2.5.3 合法使用
合法使用是資料管理的基本要求，應建立完善的合規檢核機制。確保資料取得的合法性，遵循資料使用目的限制，取得必要的同意或授權，符合跨境資料傳輸規定，建立資料使用的法規遵循機制。
5.2.5.4 倫理使用
倫理使用體現了本所的社會責任，應將倫理考量融入資料管理的各個環節。避免使用可能造成歧視的資料，尊重資料主體的權益，考量資料使用的社會影響，建立倫理審查機制，確保資料使用的倫理性。
5.2.6 合規監控與報告
明確定義AI系統相關人員理解並遵循AI政策的責任，建立完善的合規監控與報告機制，確保政策的有效執行。
5.2.6.1 AI系統相關人員合規義務
AI系統相關人員合規是政策有效執行的關鍵，應建立明確的責任分工與問責機制。明確定義AI系統相關人員理解並遵循AI政策的責任，建立AI政策遵循的監督與查核機制，定期評估政策遵循狀況並提出改善建議。
5.2.6.2 合規監控與報告
合規監控應建立量化的指標體系，確保合規狀況的可視化與可追蹤性。建立AI政策合規性的監控指標，定期向管理階層報告合規狀況，建立合規缺失的改善追蹤機制，確保政策執行的有效性。
5.2.6.3 法規遵循要求
法規遵循應建立動態的追蹤機制，及時反映法規變化對政策的影響。遵循台灣個人資料保護法及相關隱私法規，符合資通安全管理法的安全要求，遵守公司法及相關企業治理規範，配合主管機關的檢查與評估。
六、相關表單與工具
1.	Generative AI Use Cases Threshold Risk Assessment Form
2.	Generative AI Use Case Review Form
3.	AI Use Case Risk Review Playbook
4.	AP Generative AI QRM Guidelines
5.	Gen AI Market Activation Use Case Form

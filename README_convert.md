# 實踐要點格式轉換工具

這個工具可以將Markdown文件中的實踐要點從分號分隔格式自動轉換為列表格式。

## 功能特色

- 🔄 自動識別並轉換分號分隔的實踐要點
- 📋 轉換為清晰的列表格式
- 🔍 預覽模式，可以先查看轉換效果
- 💾 自動備份原文件
- 📊 統計轉換數量

## 安裝要求

- Python 3.6+
- 無需額外依賴包

## 使用方法

### 基本用法

```bash
# 轉換文件（輸出到新文件）
python convert_practice_points.py input.md output.md

# 自動生成輸出文件名
python convert_practice_points.py input.md
```

### 進階選項

```bash
# 預覽模式（不寫入文件，只顯示轉換效果）
python convert_practice_points.py -p input.md

# 創建備份文件
python convert_practice_points.py -b input.md output.md

# 查看幫助
python convert_practice_points.py -h
```

## 轉換範例

### 轉換前
```markdown
**實踐要點**：明確責任歸屬：每個AI系統都應有明確的負責人；決策可追溯：AI決策過程應可追蹤與稽核；後果承擔：對AI系統造成的後果承擔相應責任；持續改善：基於回饋持續改善系統效能。
```

### 轉換後
```markdown
**實踐要點**：
- 明確責任歸屬：每個AI系統都應有明確的負責人
- 決策可追溯：AI決策過程應可追蹤與稽核
- 後果承擔：對AI系統造成的後果承擔相應責任
- 持續改善：基於回饋持續改善系統效能
```

## 實際使用範例

### 轉換您的AI政策文件

```bash
# 預覽轉換效果
python convert_practice_points.py -p TW_AI_Policy_v5.md

# 創建備份並轉換
python convert_practice_points.py -b TW_AI_Policy_v5.md TW_AI_Policy_v5.1.md
```

### 測試工具

```bash
# 運行測試腳本
python test_convert.py
```

## 工具特點

### 智能識別
- 只轉換符合特定模式的實踐要點
- 保留已經是列表格式的內容不變
- 不會影響文件中的其他內容

### 安全性
- 支持備份原文件
- 預覽模式讓您先確認轉換效果
- 不會覆蓋原文件（除非明確指定）

### 統計信息
- 顯示轉換的實踐要點數量
- 提供轉換前後的對比

## 文件結構

```
.
├── convert_practice_points.py  # 主轉換工具
├── test_convert.py            # 測試腳本
├── README_convert.md          # 使用說明
└── TW_AI_Policy_v5.md        # 您的政策文件
```

## 注意事項

1. **編碼格式**：確保您的Markdown文件使用UTF-8編碼
2. **備份重要**：建議在轉換重要文件前先創建備份
3. **預覽確認**：使用預覽模式先確認轉換效果
4. **格式要求**：工具識別的模式為 `**實踐要點**：內容；內容；...`

## 故障排除

### 常見問題

**Q: 為什麼某些實踐要點沒有被轉換？**
A: 請檢查格式是否符合 `**實踐要點**：內容；內容；...` 的模式

**Q: 轉換後格式不正確怎麼辦？**
A: 使用預覽模式先確認效果，或檢查原文件的格式是否標準

**Q: 如何恢復原文件？**
A: 如果創建了備份文件，可以直接使用備份恢復

### 聯絡支援

如果遇到問題，請檢查：
1. Python版本是否為3.6+
2. 文件編碼是否為UTF-8
3. 實踐要點格式是否符合要求

## 更新日誌

- v1.0: 初始版本，支持基本的分號分隔轉列表功能
- 支持預覽模式和備份功能
- 添加詳細的統計信息

# 政策文件轉換工具 V2.0 - 增量處理版 (PowerShell)
# 編碼: UTF-8

$Host.UI.RawUI.WindowTitle = "政策文件轉換工具 V2.0"

function Show-Header {
    Write-Host ""
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "🚀 政策文件轉換工具 V2.0" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "✨ V2.0 新功能:" -ForegroundColor Yellow
    Write-Host "  ⚡ 增量處理 - 只轉換新檔案" -ForegroundColor White
    Write-Host "  📝 智能追蹤 - 自動記錄已處理檔案" -ForegroundColor White
    Write-Host "  🧹 自動清理 - 移除無用檔案" -ForegroundColor White
    Write-Host "  📁 自動歸檔 - 新檔案自動歸檔" -ForegroundColor White
    Write-Host ""
    
    Write-Host "📋 使用說明:" -ForegroundColor Cyan
    Write-Host "  - 將新的政策文件放在當前目錄或 'ref Policy' 資料夾" -ForegroundColor Gray
    Write-Host "  - 系統會自動偵測並只處理新檔案" -ForegroundColor Gray
    Write-Host "  - 大幅節省處理時間，提高效率" -ForegroundColor Gray
    Write-Host ""
}

function Show-Menu {
    Write-Host "請選擇執行模式:" -ForegroundColor Yellow
    Write-Host "[1] 增量處理 (推薦) - 只處理新檔案" -ForegroundColor Green
    Write-Host "[2] 強制處理 - 重新處理所有檔案" -ForegroundColor Yellow
    Write-Host "[3] 僅清理 - 只清理無用檔案" -ForegroundColor Magenta
    Write-Host "[4] 退出" -ForegroundColor Red
    Write-Host ""
}

function Execute-Conversion {
    param(
        [string]$Mode
    )
    
    $arguments = @()
    
    switch ($Mode) {
        "incremental" {
            Write-Host "⚡ 執行增量處理模式..." -ForegroundColor Cyan
            # 不需要額外參數
        }
        "force_all" {
            Write-Host "⚠️ 執行強制處理模式 (將重新處理所有檔案)..." -ForegroundColor Yellow
            $arguments += "--force-all"
        }
        "clean_only" {
            Write-Host "🧹 執行清理模式..." -ForegroundColor Magenta
            $arguments += "--clean-only"
        }
    }
    
    try {
        $process = Start-Process -FilePath "python" -ArgumentList (@("setup_policy_conversion_v2.py") + $arguments) -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Host ""
            Write-Host "========================================" -ForegroundColor Green
            Write-Host "✅ 處理完成！" -ForegroundColor Green
            Write-Host "========================================" -ForegroundColor Green
            Write-Host ""
            
            Write-Host "📁 結果位置:" -ForegroundColor Cyan
            Write-Host "  - 原始文件: 'ref Policy' 資料夾" -ForegroundColor White
            Write-Host "  - 轉換結果: 'md Policy' 資料夾" -ForegroundColor White
            Write-Host "  - 摘要報告: conversion_summary.md" -ForegroundColor White
            Write-Host "  - 詳細日誌: policy_conversion_v2.log" -ForegroundColor White
            Write-Host "  - 處理記錄: processed_files.json" -ForegroundColor White
            Write-Host ""
            
            Write-Host "💡 下次使用:" -ForegroundColor Yellow
            Write-Host "  - 只需將新檔案放入當前目錄或 'ref Policy'" -ForegroundColor Gray
            Write-Host "  - 執行增量處理即可，系統會自動偵測新檔案" -ForegroundColor Gray
            Write-Host ""
            
            # 詢問是否開啟結果資料夾
            if ($Mode -ne "clean_only") {
                $openFolder = Read-Host "是否開啟 'md Policy' 資料夾查看結果? (Y/N)"
                if ($openFolder -match '^[Yy]$') {
                    if (Test-Path "md Policy") {
                        Invoke-Item "md Policy"
                    }
                }
            }
            
        } else {
            Write-Host ""
            Write-Host "========================================" -ForegroundColor Red
            Write-Host "❌ 處理失敗" -ForegroundColor Red
            Write-Host "========================================" -ForegroundColor Red
            Write-Host ""
            
            Write-Host "請檢查以下項目:" -ForegroundColor Yellow
            Write-Host "1. 查看 policy_conversion_v2.log 了解錯誤詳情" -ForegroundColor Gray
            Write-Host "2. 確認 Python 環境正常" -ForegroundColor Gray
            Write-Host "3. 檢查檔案格式是否正確" -ForegroundColor Gray
            Write-Host ""
            
            # 詢問是否開啟日誌文件
            $openLog = Read-Host "是否開啟日誌文件查看錯誤詳情? (Y/N)"
            if ($openLog -match '^[Yy]$') {
                if (Test-Path "policy_conversion_v2.log") {
                    Invoke-Item "policy_conversion_v2.log"
                }
            }
        }
        
    } catch {
        Write-Host ""
        Write-Host "❌ 執行過程中發生錯誤: $_" -ForegroundColor Red
        Write-Host ""
    }
}

# 主程式迴圈
do {
    Clear-Host
    Show-Header
    Show-Menu
    
    $choice = Read-Host "請輸入選項 (1-4)"
    
    switch ($choice) {
        "1" {
            Execute-Conversion -Mode "incremental"
        }
        "2" {
            Execute-Conversion -Mode "force_all"
        }
        "3" {
            Execute-Conversion -Mode "clean_only"
        }
        "4" {
            Write-Host "再見！" -ForegroundColor Green
            exit 0
        }
        default {
            Write-Host "無效選項，請重新選擇" -ForegroundColor Red
        }
    }
    
    if ($choice -in @("1", "2", "3")) {
        Write-Host ""
        Read-Host "按 Enter 鍵返回選單"
    }
    
} while ($choice -ne "4")

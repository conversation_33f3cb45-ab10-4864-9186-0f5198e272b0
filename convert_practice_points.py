#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
實踐要點格式轉換工具
將分號分隔的實踐要點轉換為列表格式

使用方法:
python convert_practice_points.py input_file.md output_file.md
"""

import re
import sys
import argparse
from pathlib import Path


def convert_practice_points(text):
    """
    將實踐要點從分號分隔格式轉換為列表格式
    
    Args:
        text (str): 輸入文本
        
    Returns:
        str: 轉換後的文本
    """
    
    # 正則表達式匹配實踐要點模式
    # 匹配: **實踐要點**：內容；內容；內容...
    pattern = r'\*\*實踐要點\*\*：([^。\n]+)。?'
    
    def replace_practice_points(match):
        content = match.group(1)
        
        # 按分號分割內容
        points = content.split('；')
        
        # 清理每個要點（去除首尾空白）
        points = [point.strip() for point in points if point.strip()]
        
        # 如果只有一個要點且沒有冒號，直接返回原格式
        if len(points) == 1 and '：' not in points[0]:
            return match.group(0)
        
        # 構建列表格式
        result = "**實踐要點**：\n"
        for point in points:
            result += f"- {point}\n"
        
        # 移除最後一個換行符
        return result.rstrip()
    
    # 執行替換
    converted_text = re.sub(pattern, replace_practice_points, text)
    
    return converted_text


def process_file(input_file, output_file):
    """
    處理文件轉換
    
    Args:
        input_file (str): 輸入文件路徑
        output_file (str): 輸出文件路徑
    """
    
    try:
        # 讀取輸入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"讀取文件: {input_file}")
        
        # 轉換內容
        converted_content = convert_practice_points(content)
        
        # 寫入輸出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(converted_content)
        
        print(f"轉換完成，輸出文件: {output_file}")
        
        # 統計轉換數量
        original_count = len(re.findall(r'\*\*實踐要點\*\*：[^。\n]+；', content))
        print(f"共轉換了 {original_count} 個實踐要點")
        
    except FileNotFoundError:
        print(f"錯誤: 找不到輸入文件 {input_file}")
        sys.exit(1)
    except Exception as e:
        print(f"錯誤: {e}")
        sys.exit(1)


def main():
    """主函數"""
    
    parser = argparse.ArgumentParser(
        description='將實踐要點從分號分隔格式轉換為列表格式',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
範例:
  python convert_practice_points.py TW_AI_Policy_v5.md TW_AI_Policy_v5.1.md
  python convert_practice_points.py input.md output.md
        """
    )
    
    parser.add_argument('input_file', help='輸入的Markdown文件')
    parser.add_argument('output_file', nargs='?', help='輸出的Markdown文件（可選，默認為輸入文件名_converted.md）')
    parser.add_argument('--preview', '-p', action='store_true', help='預覽模式，只顯示轉換結果不寫入文件')
    parser.add_argument('--backup', '-b', action='store_true', help='創建輸入文件的備份')
    
    args = parser.parse_args()
    
    # 檢查輸入文件是否存在
    input_path = Path(args.input_file)
    if not input_path.exists():
        print(f"錯誤: 輸入文件 {args.input_file} 不存在")
        sys.exit(1)
    
    # 確定輸出文件名
    if args.output_file:
        output_file = args.output_file
    else:
        output_file = input_path.stem + '_converted' + input_path.suffix
    
    # 創建備份
    if args.backup:
        backup_file = input_path.stem + '_backup' + input_path.suffix
        try:
            import shutil
            shutil.copy2(args.input_file, backup_file)
            print(f"已創建備份文件: {backup_file}")
        except Exception as e:
            print(f"警告: 無法創建備份文件: {e}")
    
    # 預覽模式
    if args.preview:
        try:
            with open(args.input_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            converted_content = convert_practice_points(content)
            
            # 找出所有實踐要點並顯示轉換前後對比
            original_matches = re.findall(r'\*\*實踐要點\*\*：[^。\n]+', content)
            converted_matches = re.findall(r'\*\*實踐要點\*\*：\n(?:- [^\n]+\n?)+', converted_content)
            
            print("=== 轉換預覽 ===")
            for i, (orig, conv) in enumerate(zip(original_matches, converted_matches), 1):
                print(f"\n第 {i} 個實踐要點:")
                print("轉換前:")
                print(f"  {orig}")
                print("轉換後:")
                for line in conv.split('\n'):
                    print(f"  {line}")
            
            print(f"\n共找到 {len(original_matches)} 個實踐要點")
            
        except Exception as e:
            print(f"預覽失敗: {e}")
            sys.exit(1)
    else:
        # 執行轉換
        process_file(args.input_file, output_file)


if __name__ == "__main__":
    main()

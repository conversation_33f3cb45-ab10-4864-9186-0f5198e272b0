[{"input_file": "input\\AI Use Case Risk Review Playbook - FINAL FOR REVIEW.pptx", "output_file": "output\\AI Use Case Risk Review Playbook - FINAL FOR REVIEW_extracted.md", "input_size": 14198848, "output_size": 47613, "conversion_time": "2025-06-10T14:42:16.336382", "compression_ratio": 0.34}, {"input_file": "input\\01 DTTL DPM AI Risk Management Policy menu.pdf", "output_file": "output\\01 DTTL DPM AI Risk Management Policy menu_extracted.md", "input_size": 240328, "output_size": 1141, "conversion_time": "2025-06-10T14:42:17.819757", "compression_ratio": 0.47}, {"input_file": "input\\12. 生成式人工智能 (Generative AI) 使用指南 (包含 ChatGPT )_2023.03_中文.pdf", "output_file": "output\\12. 生成式人工智能 (Generative AI) 使用指南 (包含 ChatGPT )_2023.03_中文_extracted.md", "input_size": 506867, "output_size": 9317, "conversion_time": "2025-06-10T14:42:18.347209", "compression_ratio": 1.84}, {"input_file": "input\\AP Generative AI QRM Guidelines (Final).pdf", "output_file": "output\\AP Generative AI QRM Guidelines (Final)_extracted.md", "input_size": 1807568, "output_size": 202895, "conversion_time": "2025-06-10T14:42:25.091891", "compression_ratio": 11.22}, {"input_file": "input\\generative-ai-usage-guidelines-external-channels.pdf", "output_file": "output\\generative-ai-usage-guidelines-external-channels_extracted.md", "input_size": 381531, "output_size": 10595, "conversion_time": "2025-06-10T14:42:25.403421", "compression_ratio": 2.78}, {"input_file": "input\\Identifying Prohibited AI – Playbook for Risk & OGC.pdf", "output_file": "output\\Identifying Prohibited AI – Playbook for Risk & OGC_extracted.md", "input_size": 2104513, "output_size": 35406, "conversion_time": "2025-06-10T14:42:26.840426", "compression_ratio": 1.68}, {"input_file": "input\\1. Generative AI Use Cases Threshold Risk Assessment Form.docx", "output_file": "output\\1. Generative AI Use Cases Threshold Risk Assessment Form_extracted.md", "input_size": 40476, "output_size": 4318, "conversion_time": "2025-06-10T14:42:27.930579", "compression_ratio": 10.67}, {"input_file": "input\\2. Generative AI Use Case Review Form.docx", "output_file": "output\\2. Generative AI Use Case Review Form_extracted.md", "input_size": 96218, "output_size": 61614, "conversion_time": "2025-06-10T14:42:28.105908", "compression_ratio": 64.04}, {"input_file": "input\\Draft_AI_DPM_5Sept2024.docx", "output_file": "output\\Draft_AI_DPM_5Sept2024_extracted.md", "input_size": 38507, "output_size": 6916, "conversion_time": "2025-06-10T14:42:28.224903", "compression_ratio": 17.96}, {"input_file": "input\\Gen AI Market Activation Use Case Form.docx", "output_file": "output\\Gen AI Market Activation Use Case Form_extracted.md", "input_size": 91657, "output_size": 62226, "conversion_time": "2025-06-10T14:42:28.395991", "compression_ratio": 67.89}, {"input_file": "input\\GenAI Risk Threshold Assessment - Global Enabling Areas and Shared Services.docx", "output_file": "output\\GenAI Risk Threshold Assessment - Global Enabling Areas and Shared Services_extracted.md", "input_size": 71058, "output_size": 4662, "conversion_time": "2025-06-10T14:42:28.443656", "compression_ratio": 6.56}, {"input_file": "input\\TW_AI_Policy_v5.docx", "output_file": "output\\TW_AI_Policy_v5_extracted.md", "input_size": 41960, "output_size": 24668, "conversion_time": "2025-06-10T14:42:28.765540", "compression_ratio": 58.79}]
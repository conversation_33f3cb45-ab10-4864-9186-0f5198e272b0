# Draft_AI_DPM_5Sept2024

**DPM __**** – ****A****rtificial Intelligence (AI)**

**Introduction**

The Member Firms of Deloitte Touche Tohmatsu Limited (DTTL) are committed to complying with the principles of trustworthy Artificial Intelligence (AI).

ThisSectionoftheManualprovidespoliciesandguidance on establishing anAISystems (as defined below) governancestructureby Member Firms.

[AP Comment: Overall, the policy below heavily focus on the risk approach to AI Systems (e.g. governance and process). Should the AI Policy include about the interaction between the AI Risk Leader with the AI Leadership or executive body of the firm (please refer the formulation in paragraph 2 of DPM1570 Privacy).]

Tofulfillthiscommitment,thisSectionsets forth policies and guidance that apply to Member Firms.

ThisSectionoftheManualappliesto the utilization of AI[for both client service and internal use purposes].Other policies (such as DPM1570 – Privacy, DPM 1630 – Client Confidential Information, DPM 1420 – Independence, DPM 2060 - Ethics) may also apply.[AP Comment: Should we include PM40 Security here as well?]

** Policies****and****Guidance**

- For purposes of this Section, “AI System” is defined as a machine-based system that, for explicit or implicit objectives, infers, from the input it receives, how to generate outputs such as predictions, recommendations, or decisions that can influence physical or virtual environments.[AP Comment: is there a reason we do not pick up this point which is included in OECD definition: “Different AI systems vary in their levels of autonomy and adaptiveness after deployment” ?]
- Each Member Firm should have policies and processes to provide a reasonable level of assurance that AI Systems will be identified and managed in accordance with responsible business practices and relevant laws and regulations across the entirety of the AI System lifecycle (AI Systems Risk Program). The AI Systems Risk Program should be appropriately designed for the Member Firm’s practice and should include adequate resources considering the size of the Member Firm, the nature and scope of AI Systems being used by the Member Firm, and other relevant factors, such as client contractual obligations and local laws, including privacy laws and regulations. Such program should include the following components: [AP Comments: 1. As part of the AI Systems Risk Program, we may need a separate paragraph to address AI Systems that we develop or procure for clients’ use (E.g. Jointly developed with alliance partner), as currently it only deals with AI Systems “being used by the MF”. 2. What are the AI Systems Risk Program components? Do they refer to what is set out in para 2, 2nd bullet point below? Please make it clear.]
- An AI policy which should address the following areas:
- Identify AI Systems within the Deloitte Member Firm’s jurisdiction and for purposes of aligning with this Section. [AP Comment: What do we mean by ‘within the MF’s jurisdiction’? Should it be within the MF’s possession or control?]
- Establishment of an AI Systems Risk Program within the Deloitte Member Firm that:
- Establishes and maintains defined roles, including an individual with AI accountability at a senior level (e.g., AI Risk Lead/AI Risk Officer). This senior level role can be incorporated into an existing role. This role should denote appropriate escalation paths and decision-making rights. [AP Comment: Should the AI Risk Lead/ AI Risk Officer be a single person? If it is a risk role, should it be ‘accountability for the management of AI risk’ instead of AI strategy or transformation?]
- Establishment of a Data Ethics Oversight Body that considers AI ethics and use cases, where appropriate. [AP Comment: 1. Instead of the Data Ethics Oversight Body which seemingly limited to data related AI risk, it is suggested there should be an AI Risk Committee as an AI risk oversight body to be established. Also, the composition of it should include relevant SME leaders (e.g. Risk, Confidentiality and Privacy, OGC etc.)? 2. AI ethics and use cases review by the oversight Body should be required only for high risk cases instead of all cases. ]
- Establishes an AI system inventory/catalog that includes the risk rating for each AI System. [AP comment: Do we suggest a consistent risk rating standard here? Should the key requirement here is that for higher risk AI System, there is proper documented risk mitigation plan.]
- Establishes an AI risk management process to identify, assess and treat AI risks of AI System.
- Establishes or utilizes existing incident management processes in relation to AI Systems.
- Considers the procurement lifecycle of AI Systems. [AP comment: What do we mean by ‘considers’ in this context, please make it clear?]
- Considers the data used in AI Systems, including leveraging data, data lineage, contractual obligations, and data governance.
- Includes AI resources for assistance regarding AI use. [AP comment: What do we mean by AI resources? Do we mean people or guidance or service delivery transformation within AI System Risk program?]
- Identifies Partners, Professional Staff, and Support Staff responsibilities to understand and comply with the Member Firm AI policy.
- Includes training and awareness programs on the Member Firm’s AI policy.
- References a disciplinary process to address non-compliance with the AI policy.
- Obliges AI use in alignment with the Deloitte Shared Values.
- Reflect local customs, laws, and regulations, where applicable, and AI principles that are consistent with the AI Principles set forth in the Appendix below.
** Effective****Date**

Issued______2024, andeffective_______2024

** Appendix**

TheAIPrinciplesarealigned to Deloitte’s Trustworthy AI Frameworkasfollows:

- Accountable: Policies are in place to determine who is responsible for the decisions made or derived with the use of technology.
- Fair and impartial: The technology is designed and operated inclusively in an aim for equitable application, access, and outcomes.
- Private: User privacy is respected, and data is not used or stored beyond its intended and stated use and duration; users are able to opt-in / out of sharing their data.
- Responsible: The technology is created and operated in a socially responsible manner.
- Robust and reliable: The technology produces consistent and accurate outputs, withstands errors, and recovers quickly from unforeseen disruptions and misuse.
- Safe and secure: The technology is protected from risks that may cause individual and / or collective physical, emotional, environmental, and / or digital harm.
- Transparent and explainable: Users understand how technology is being leveraged, particularly in making decisions; these decisions are easy to understand, auditable and open to inspection.
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試實踐要點轉換工具
"""

from convert_practice_points import convert_practice_points


def test_conversion():
    """測試轉換功能"""
    
    # 測試案例1: 標準的分號分隔格式
    test_text1 = """
### 1. 問責性（Accountable）
建立政策以確定誰對使用AI技術所做出或衍生的決策負責。

**實踐要點**：明確責任歸屬：每個AI系統都應有明確的負責人；決策可追溯：AI決策過程應可追蹤與稽核；後果承擔：對AI系統造成的後果承擔相應責任；持續改善：基於回饋持續改善系統效能。

### 2. 公平與公正（Fair and Impartial）
AI技術的設計與營運應具包容性，以追求公平的應用、存取及結果。

**實踐要點**：避免偏見：識別並消除演算法中的不當偏見；平等對待：確保不同群體獲得公平對待；包容設計：考量多元族群的需求與特性；救濟機制：提供受不公平對待者的救濟途徑。
"""

    # 測試案例2: 已經是列表格式的內容（應該不變）
    test_text2 = """
**實踐要點**：
- 明確責任歸屬：每個AI系統都應有明確的負責人
- 決策可追溯：AI決策過程應可追蹤與稽核
- 後果承擔：對AI系統造成的後果承擔相應責任
"""

    # 測試案例3: 混合格式
    test_text3 = """
**實踐要點**：資料最小化：僅收集必要的個人資料；目的限制：資料使用應符合收集目的；同意機制：取得適當的資料使用同意；權利保障：保障資料主體的各項權利。

其他內容...

**實踐要點**：
- 已經是列表格式的內容
- 不應該被改變
"""

    print("=== 測試案例1: 標準分號分隔格式 ===")
    result1 = convert_practice_points(test_text1)
    print("轉換結果:")
    print(result1)
    print("\n" + "="*50 + "\n")

    print("=== 測試案例2: 已經是列表格式 ===")
    result2 = convert_practice_points(test_text2)
    print("轉換結果:")
    print(result2)
    print("\n" + "="*50 + "\n")

    print("=== 測試案例3: 混合格式 ===")
    result3 = convert_practice_points(test_text3)
    print("轉換結果:")
    print(result3)


if __name__ == "__main__":
    test_conversion()

# AI Use Case Risk Review Playbook - FINAL FOR REVIEW

## 投影片 1

### DECEMBER 2024

AI USE CASE RISK REVIEW PLAYBOOK

Learn about the leading practices for AI Risk Review processes within the organization

Internal use only

GUIDANCE FOR DELOITTE FIRMS AND GLOBAL

---

## 投影片 2


| 1 | Playbook context |
| --- | --- |
| 2 | AI use case risk review process |
| 3 | Guidance |
|  | AI use case risk levels |
|  | AI use case risk threshold assessment |
|  | AI use case risk review form |
|  | AI systems inventory |
| 4 | Where to go from here |

This playbook outlines AI use case risk review guidance and leading practices that Deloitte firms and Deloitte Global can leverage to establish or enhance their AI use case risk review processes

---

## 投影片 3

### PLAYBOOK CONTEXT

Get to know what is included in this playbook

---

## 投影片 4

As Deloitte moves to become an AI fueled business, consistent risk reviews across the network will be important to our overall success. This playbook of leading practices has been developed to be used across the Deloitte network

This playbook aims to provide guidance and leading practices for conducting AI use case risk reviews across both Deloitte Global and Deloitte firms.
By following the guidance outlined in this playbook, all firms will be able to:
Understand and reference leading practices for AI use case risk review processes
Apply the leading practices to establish or enhance their own risk review processes and supporting activities

This playbook is intended to:
Drive consistency across the network
Accelerate the development and risk review of AI use cases at pace
Foster cross–collaboration and learning in an emerging space
Embed Trustworthy AI in AI use case risk review processes

This playbook was developed collaboratively with input from Deloitte firms and Global businesses across the network.
Please note that this is the first version of this playbook. This playbook will continue to be enhanced as additional feedback is collected and leading practices are developed.

HOW WAS THIS PLAYBOOK DEVELOPED?

---

## 投影片 5

This playbook has been designed primarily for risk leaders and practitioners to leverage however, it will impact multiple other parties across the network

Risk Leaders / Practitioners

Risk Leaders / Practitioners conduct risk reviews to approve / not approve new AI use cases submitted and determine any approval conditions required to manage risks and / or meet regulatory compliance requirements. This playbook has been developed as reference for these individuals to understand evolving risk review leading practices in an emerging space and align their own processes.

Use Case / Technology / Product Owners

AI Risk Governing Body

Privacy and Confidentiality Teams

Use Case / Technology / Product Owners are responsible for submitting new AI use cases for review and will be responsible for completing key activities within the AI use case risk review process.

An AI Risk Governing Body is responsible for AI governance and risk management and can have different terminology depending on the geography (e.g., trust council, ethics council, public interest group, etc.)

Privacy and Confidentiality Teams support the risk review of AI use cases from a privacy and confidentiality perspective.

Independence Professionals support the risk review of AI use cases from an independence perspective.

Ethics Teams support the review of AI use cases to help adhere to Deloitte’s ethics and ethical technology principles.

Independence Professionals

Ethics Teams

Legal practitioners / Office of General Counsel (OGC)

Legal practitioners / Office of General Counsel (OGC) support the risk review of AI use cases from a legal and regulatory compliance perspective.

WHO HAS THIS PLAYBOOK BEEN DEVELOPED FOR?

WHO ELSE WILL THIS PLAYBOOK INFLUENCE?

---

## 投影片 6

The following steps have been suggested to keep in mind as you review the playbook to help establish or enhance your firm’s AI risk review processes

Review the leading practices
Review the processes and suggested activities outlined in the playbook to understand the recommended leading practices
Evaluate current practices
Assess your existing risk review processes and activities

Select relevant content
Define which risk review processes or activities within this playbook could benefit your firm
Customize and adapt
Tailor and integrate the selected processes and activities to fit your firm’s unique requirements
Engage with key stakeholders
Engage and collaborate with key stakeholders in your firm to confirm the defined content aligns with goals and incorporates any required feedback

Develop an implementation plan
Outline the steps, timelines, and resources needed to integrate the selected playbook components into your firm’s processes
Execute the implementation plan
Develop training resources and communication materials to educate relevant team members about new or updated processes
Implement planned changes and monitor progress for any challenges that need to be addressed

Continuous Improvement
Review any playbook updates released and confirm if updates are needed to the firm's processes
Provide feedback to the Global playbook team on the effectiveness of the playbook and / or any enhancements you would like to see

REVIEW

DEFINE

IMPLEMENT

IMPROVE

SUGGESTED STEPS TO FOLLOW:

Every firm will be at a different point in their journey of implementing their AI risk review processes, and therefore will leverage this playbook in different ways

---

## 投影片 7

### AI USE CASE RISK REVIEW PROCESS

Guidance for how to establish an AI use case risk review processes

---

## 投影片 8

As we move forward to becoming an AI–fueled business, it is important for each firm to establish a risk review process for evaluating AI use cases

What is an AI use case risk review process?

An AI use case risk review process includes the key activities and steps needed to evaluate the risks associated with an AI use case to determine if it can move forward with development or not
The objective of the process is to understand the risks associated with an AI use case, develop the appropriate mitigations (if applicable), and determine if the AI use case should move forward with development

What are the benefits of having an AI use case risk review process?

Develop and implement AI use cases in a responsible and ethical manner
Build trust and minimize negative consequences by proactively addressing and mitigating risks
Enable informed decision-making regarding the implementation of AI systems
Triage use cases based on risk level, to strategically utilize resources and time

An overview of AI risk review process leading practices
Suggested activities for you to take as an organization in establishing or enhancing your AI risk review processes

Review the subsequent slides for:

---

## 投影片 9

### IDEATION/ IDENTIFICATION

An AI use case risk review process involves key steps and activities to evaluate the risks associated with an AI use case, and determine if it should move forward

Objective: Identify / ideate an AI use case and obtain business endorsement.

Objective: Determine initial risk level for an AI use case and the associated risk activities.

Objective: Conduct an AI use case risk review to identify risks, mitigations, and determine if the use case is approved / not approved.

Objective: Monitor the AI use case changes throughout development to verify the initial approval conditions are being met.

Objective: Maintain an inventory of all AI systems and associated use cases.

Objective: Monitor the AI use case changes throughout production.

HIGH LEVEL RISK REVIEW PROCESS

PRODUCTION

DEVELOPMENT

PRE–DEVELOPMENT

AI USE CASE RISK REVIEW

MONITOR AI USE CASE

AI SYSTEMS INVENTORY

MONITOR AI USE CASE

AI USE CASE RISK TRIAGE

Description: Define new AI use cases and determine whether a similar use case or AI system already exists; secure business endorsement and funding before submitting for a risk review.

Description: Conduct a risk threshold assessment to triage use cases by risk level and determine the risk review activities associated with the risk levels:
Low / normal risk
Medium risk
High risk
Prohibited risk

Description: Complete the required AI use case risk review activities to assess an AI use case for approval (e.g., complete a full use case form, identify relevant risk guidance, conduct risk review assessment and meetings, determine approval conditions).

Description: Once the use case has been approved, continuously monitor for any changes in the AI use case during the development phase (e.g., modifications, data usage). Conduct the necessary steps to assess any potential risks associated with these changes.

Description: Document detailed information about each AI system and associated use cases such as AI system and/or use case name, use case scope, owner, data and other relevant details

Description: Continuously monitor any changes in the AI use case during the production phase (e.g., upgrades, modifications, decommissioning, data usage). Conduct the necessary steps to assess any potential risk associated with this changes.

See the next slide for the detailed process flow

---

## 投影片 10

### Submit AI use case risk threshold assessment

The following process outlines the suggested key steps involved in an AI use case risk review

IDEATION/ IDENTIFICATION

MONITOR AI USE CASE

MONITOR AI USE CASE

AI SYSTEMS INVENTORY

Document decision to approve / not approve and approval conditions if applicable

Submit use case risk review form

Conduct AI use case risk review

Conduct AI risk governing body review2

Applicable firm use case development processes

Monitor for use case changes & approval conditions being met

Review use case changes

Update AI systems inventory with use case

Monitor for use case changes

Review use case changes

Determine if the use case can be modified1

Triage use case and identify risk level

Identify use case and secure funding and business endorsement

Prohibited

Low / normal

High

At any point in the process, the risk level can be modified based on the risk review/information collected and the subsequent review activities can be adjusted. If a use case is not approved at any point during the risk review, the owner should be informed, and the decision should be documented.

1 if at any point during the use case risk review, the use case is classified as prohibited, the use case owner should determine if the use case can be modified in order to be approved. If modification is not possible, the use case should not proceed
2 Different geographies may have different terminology for this governing body (e.g., Trust council, Ethics Council, public interest group, etc.)

Legend

Use case owner

Use case risk reviewer

External process

Iterate as required

Start of process

End of process

Decision

Global templates available

High

PRODUCTION

DEVELOPMENT

PRE–DEVELOPMENT

AI USE CASE RISK REVIEW

AI USE CASE RISK TRIAGE

1

2

3

5

8

4

11

9

7

12

13

6

10

Use case is modified

Use case owner informed

Not approved

Approved

Continue through relevant production processes

Medium

Medium

Use case is not modified, and therefore unapproved

See the next slide for the leading practices associated with each of these process steps

---

## 投影片 11


| PROCESS STEP | # | ACTIVITY | LEADING PRACTICES | RESPONSIBLE ROLE |
| --- | --- | --- | --- | --- |
| IDEATION / IDENTIFICATION | 1 | Identify use case and secure funding and business endorsement | Before submitting an AI use case for risk review, it is the responsibility of the use case owner to: Gain business endorsement and secure funding if required Check the use case against the firm’s current AI systems inventory to confirm that a similar use case does not already exist, if a similar use case already exists, Deloitte firms should set–up a process for use case efficiencies to avoid duplication of efforts | Use case owner |
| AI USE CASE RISK TRIAGE | 2 | Submit AI use case risk threshold assessment | It is suggested that firms set up an AI risk threshold assessment and process to conduct an initial assessment of use case risks Firms should determine which system is best suited for their operations to submit AI use cases for assessment (e.g., ServiceNow portal, custom built portal, manual e-mail submission) For more information on how to develop a risk threshold assessment, please see the risk threshold assessment section in this playbook | Use case owner |
|  | 3 | Triage use case and identify risk level | The risk threshold assessment is used to triage the use case into the following suggested risk levels and determine the associated risk review activities: Low / normal risk: a full use case form is not needed, however relevant approval conditions should be documented and followed Medium risk: an AI use case risk review form should be submitted, and a risk review should be conducted. These activities will be iterative as required as often there needs to be a discussion between the risk review reviewer and the use case owner High risk: in addition to the activities noted for medium risk use cases, a review should be conducted by the firm’s AI risk governing body (e.g., trust council, ethics council, public interest group, etc.) Prohibited: if a use case is classified as prohibited it either (1) be modified to no longer be prohibited and then re-triaged or (2) should not proceed if it cannot be modified Note: The risk level classification can be changed at any point during the risk review as new information is available (for example the use case can be initially classified as high risk during the triage but later updated to medium risk after assessment of the use case form) | Use case risk reviewer |

For each step and activity outlined in the risk review process, the following leading practices provide further guidance (1/3)

---

## 投影片 12


| PROCESS STEP | # | ACTIVITY | LEADING PRACTICES | RESPONSIBLE ROLE |
| --- | --- | --- | --- | --- |
| AI USE CASE RISK REVIEW | 4 | Prohibited: Determine if the use case can be modified | For use cases deemed prohibited, use case owners and risk reviewers should work together to determine if modifications can be made to reduce to a risk level acceptable to proceed; if modifications are not possible the use case should not be approved Firms should establish clear process steps and criteria for how to review and modify a use case classified as prohibited | Use case risk reviewer |
|  | 5 | Submit AI use case risk review form | An AI use case risk review form should be submitted for use cases determined to be medium or high risk based on the initial risk threshold assessment” For more information, please see the AI use case form section in this playbook | Use case owner |
| AI Use Case Risk Review | 6 | Conduct AI use case risk review | A risk review is an iterative process where the risks of the use case are considered during a thorough evaluation of the information submitted in the use case form. This step will typically require ongoing discussions between the use case owner and the use case risk reviewer to confirm information, clarify uncertainties and address any concerns | Use case risk reviewer |
|  | 7 | Conduct AI risk governing body review | Use cases deemed high risk following the AI use case risk review should be submitted to a firm’s AI risk governing body for a comprehensive review and further identification of potential risks and mitigations prior to receiving approval Note: firms may have different governing bodies (e.g., trust council, ethics council, public interest group, etc.) or a specific governing body may need to be stood up if it does not already exist | Use case risk reviewer |
|  | 8 | Document decision to approve / not approve and approval conditions if applicable | The decision to approve / not approve a use case should be documented along with reasoning and any required approval conditions if applicable; this information should be shared with the use case owner The use case owner should be notified by the risk reviewer if there are existing approval conditions applicable to the use case within the firm (e.g., vendor risk guidance, general AI risk guidance, etc.) | Use case risk reviewer |

For each step and activity outlined in the risk review process, the following leading practices provide further guidance (2/3)

---

## 投影片 13


| PROCESS STEP | # | ACTIVITY | LEADING PRACTICES | RESPONSIBLE ROLE |
| --- | --- | --- | --- | --- |
| MONITOR AI USE CASE | 9 | Monitor use case for changes and approval conditions are met | Firms should establish a process, if not already in place, to conduct regular reviews of use cases to confirm any approval conditions are properly implemented and to support readiness for any applicable compliance requirements As the use case moves through the development phase, there is the potential that the parameters of the use case may change (e.g., the data being used). Should this occur, it is the responsibility of the use case owner to notify the use case risk reviewer of any changes or new risks to determine what additional risk assessment activities are required | Use case owner |
|  | 10 | Review use case changes | Every firm should establish a process to review use case changes and to conduct a review for modifications during the development phase | Use case risk reviewer |
| AI SYSTEMS INVENTORY | 11 | Update AI systems inventory with use case | Once the use case has moved through the development phase it should be entered into the AI systems inventory to document key AI system and use case information For more information, see the AI systems inventory section in this playbook |  |
| MONITOR AI USE CASE | 12 | Monitor for use case changes & approval conditions being met | Once the use case is in the production phase, there is the potential that the parameters of the use case may change (e.g., the data being used). Should this occur, it is the responsibility of the use case owner to notify the use case risk reviewer of any changes or new risks to determine what additional risk assessment activities are required | Use case owner |
|  | 13 | Review use case changes | Every firm should establish a process to review use case changes and to conduct a review for modifications during the production phase | Use case risk reviewer |

For each step and activity outlined in the risk review process, the following leading practices provide further guidance (3/3)

See the next slide for suggested firm activities in establishing or enhancing your risk review processes

---

## 投影片 14

Suggested firm activity: Whether you're starting from scratch or refining your risk review process, suggested guidance below has been provided for how to establish or enhance an AI use case risk review process

Evaluate the firm’s existing AI use case risk review processes against the guidance in this playbook to identify and adapt any process steps or activities
Consider providing communication materials and training to facilitate the adoption of the updated process

Step 1

Identify and outline the key steps, activities, and decision points within the process
Engage with relevant stakeholders (e.g., legal and compliance teams, ethics, risk and industry leaders) to gather diverse perspectives to support the development of process that effectively identifies risk and mitigations
Customize the process to align with the specific requirements, goals, and risk appetite of the organization. Consider factors such as the complexity of AI use cases, regulatory requirements, and industry standards

Step 1

Designate what role(s) will be responsible for overseeing the entire process, such as an AI risk manager or a designated risk committee
Assign specific roles for each activity in the process, including subject matter advisors, risk reviewers, etc. Clearly define their responsibilities and expectations
Establish effective communication channels for all stakeholders to encourage cross-functional collaboration to promote responsible and efficient decision–making

Step 2

Implement the AI use case risk review process and develop communication materials, such as process flowcharts, guidelines or FAQs, to address any questions or concerns that may arise during the implementation process

Step 3

Define the specific components of the process to design a well–structured process flow

Define roles and responsibilities for each activity of the process

Implement the process, providing training and communication material as needed

Option 1: An AI Use Case Risk Review Process is already in place

Option 2: An AI Use Case Risk Review Process is not already in place

---

## 投影片 15

### AI USE CASE RISK LEVELS

Guidance on how to define AI use case risk levels

---

## 投影片 16

Having well defined risk levels provides firms with a structured and consistent approach to assessing and managing the risks associated with AI use cases

What are AI use case risk levels?

AI use case risk levels (e.g., low / normal, medium, high, prohibited) support the governance of a firm’s risk review process
Categorizing use cases into different risk levels allows firms to establish the appropriate risk review activities required for each risk level
Firms should develop risk levels that are aligned to their risk tolerance and consider applicable risk factors such as existing risk frameworks within their firm, Trustworthy AI dimensions, and regulatory / compliance requirements applicable to that firm

What are the benefits of having AI use case risk levels?

Provides the basis for triaging use cases to:
Apply the appropriate level, and effort, of use case review for each risk level
Prioritize resources based on each risk level’s need
Accelerate speed–to–market value for low / normal risk use cases
Support compliance with regulations and standards if applicable (e.g., EU AI Act high risk and prohibited AI)

Suggested activities for firms to follow to develop AI use case risk levels
Risk levels reference definition examples

Review the subsequent slides for:

---

## 投影片 17

Suggested firm activity: each firm will need to define use case risk levels that are right for their firm to classify AI use cases and can reference the suggested steps and activities below

Consider if the risk levels suggested in this playbook will work for your firm (i.e., low / normal, medium, high and prohibited) and if not, adjust as required
Determine if there are any regulatory / compliance or firm specific requirements that need to be considered (e.g., EU AI Act or firm specific risk frameworks)

Step 1

Determine which risk factors references are important to your firm to factor into the risk levels. Some suggested considerations:
Leading global frameworks or regulations (e.g., the EU AI Act or GDPR)
Trustworthy AI dimensions (safe & secure, robust & reliable, accountable, responsible, private, transparent & explainable and fair & impartial)
Data sensitivity and confidentiality (e.g., client data or personal information)

Step 2

Use the risk factors determined in step 2 to define the risk levels
Engage with relevant stakeholders such as business / industry leaders, legal and compliance teams, ethics, risk, security, and privacy to gather input on the appropriate risk levels

Step 3

Once the initial set of risk levels have been established, it is recommended that they are stress tested against a set of use cases to evaluate their effectiveness.

See next slide for examples of risk level definitions

---

## 投影片 18

AI use case risk level overviews and examples have been provided for firms to consider when defining their risk levels; see subsequent slides for further details on each risk level

AI systems / use cases that:
Do not meet the conditions of medium, high or prohibited risk
Have low potential for harm or negative impact
Use only public or synthetic data
OR
Are proofs of concept (PoC) that are time-boxed and will be decommissioned at the end of the PoC
OR
Are similar to an existing AI system / use case where existing approval conditions can be leveraged

LOW / NORMAL RISK

AI systems / use cases that:
Do not meet the conditions of high or prohibited risk
OR
Will be external / public facing (and / or the AI outputs will be external / public facing)
OR
Use client confidential or personal data or will integrate with other systems containing Deloitte, client or personal data
OR
Include the use of an ‘AI agent’ or chatbot

MEDIUM RISK

AI systems / use cases that:
Considered to be ‘high risk’ in accordance with the EU AI Act or a similar regulation (if applicable)
OR
Could threaten the safety, livelihoods, or fundamental rights of people / groups of people
OR
Have the potential for significant public scrutiny
OR
There is an element of automated decision making

HIGH RISK

AI systems / use cases that:
Considered to be ‘prohibited’ under the EU AI Act or a similar regulation (if applicable)
OR
Go against Deloitte’s ethical guidelines and / or purpose. Please see the Global Principles of Business Conduct for more information

PROHIBITED RISK

Example:
An AI tool which will integrate with client data in a heavily regulated industry (e.g., the energy sector).

Example:
An AI system that will store personal information related to employee performance analysis.

Example:
An AI use case which uses social scoring to provision funds.

Example:
A proof of concept for a client bid that uses public and/or synthetic data and will be built in Deloitte approved development environment.

See next slide for expanded considerations for low / normal risk

---

## 投影片 19

The following criteria has been developed for firms to consider in developing the definitions for their low / normal risk AI systems

See next slide for expanded considerations for medium-risk

DELOITTE RISK CONSIDERATIONS

AI systems that do not meet the requirements for medium, high or prohibited risk and have any of the following characteristics:
Is a proof of concept (PoC) that will be conducted in a sandbox environment where the system is time boxed and decommissioned at the end of testing; client users will not be granted access and outputs will only be used for learning
Is not intended for use within a heavily regulated or high-risk industry / sector (e.g. defense, security & justice or government & public services)
Is similar to an existing use case or system where approval conditions can be leveraged

EU AI ACT CONSIDERATIONS

AI systems that do not meet the requirements for medium, high or prohibited risk and have any of the following characteristics:
Performs a narrow procedural task
Improves the result of a previously completed human activity
Detects decision-making patterns or deviations from prior decision-making patterns and is not meant to replace or influence the previously completed human assessment without proper human review
Performs a preparatory task to an assessment relevant for the purpose of the use cases listed in Annex III of the EU AI Act. Please see Annex III for more information

DATA CONSIDERATIONS

Data used is limited to the below:
Only public or synthetic data
Client data (including client personal data) is only used on a client or client-provisioned and prescribed environment in accordance with their instructions, licenses and technical controls

---

## 投影片 20

The following criteria has been developed for firms to consider in developing the definitions for their medium risk AI systems

See next slide for expanded considerations for high-risk

DELOITTE RISK CONSIDERATIONS

AI systems that do not meet the requirements for high or prohibited risk and have any of the following characteristics:
Will be external / public facing (and / or the AI outputs will be external / public facing)
Is intended for use within a high-risk industry or sector (e.g., health care, financial)
Is involved in the training / fine-tuning a large language model (LLM)
Includes the use of an ‘AI agent’ or chatbot
Will learn from data and / or prompt inputs
Utilizes a Deloitte-provisioned license but the technology / platform has not gone through formal internal Deloitte reviews with existing approval conditions or published risk guidance OR the anticipated usage is outside of the existing approval conditions or published risk guidance

EU AI ACT CONSIDERATIONS

AI systems that do not meet the requirements for high or prohibited risk, but may have specific transparency requirements including any of the following characteristics:
Interact directly with humans
Generate synthetic audio, image, video or text content
Generate or manipulate image, audio or video content constituting a deep fake

DATA CONSIDERATIONS

Data used satisfies any of the following requirements:
Client confidential or personal data (other than authentication data) will be handled, utilized and / or stores inside a Deloitte or 3rd party environment
The application / system will integrate with other systems containing Deloitte, client or personal data
Data will be obtained from multiple clients, engagements, opportunities, geographies, or other 3rd party data sources will be combined
Data will be reused for a secondary purpose

---

## 投影片 21

The following criteria has been developed for firms to consider in developing the definitions for their high risk AI systems

See next slide for expanded considerations for prohibited risk

DELOITTE RISK CONSIDERATIONS

AI systems could result in at least one of the following outcomes:
Could threaten the safety, livelihoods, or fundamental rights of people/groups of people
Automated decision making
Targets or applies to vulnerable groups (e.g., children, disabled)
Will or could be utilized for military or political purposes
Has the potential for significant public scrutiny by any of the following means:
Create significant media coverage of Deloitte’s involvement and/or association
Be of significant interest to the public
Lead to regulatory investigation of Deloitte and/or our client

EU AI ACT CONSIDERATIONS

AI systems used for at least one of the following purposes:
Remote biometric identification, biometric categorization, or emotion recognition
Safety components in the management and operation of critical infrastructure (e.g., digital infrastructure or electricity)
AI systems that are a safety component of a product
Education access, admission or assignment, or used to monitor and detect behavior during tests or evaluate learning outcomes
Recruitment, monitoring or determining employee performance, termination or promotion, or to make decisions affecting terms of work–related relationships or employee tasks
Assessing the eligibility of persons to access or enjoy essential public or private services and benefits, or to revoke such benefits
Evaluating the creditworthiness of natural persons or assessing their credit score
Risk assessing and pricing life and health insurance
Evaluating and classifying emergency calls, or used for the dispatch of emergency first response services
Law enforcement related activities
Migration, border control or asylum management activities (including AI systems used as polygraphs or similar tools)
The administration of justice or democratic purposes (including AI systems used to influence the outcome of an election or the voting behavior of natural persons)

DATA CONSIDERATIONS

Data used satisfies any of the following requirements:
High-risk confidential data is in scope
Special category or sensitive personal data is in scope
Data will be reused for a secondary purpose
Publicly available data is obtained through web-scraping will be utilized and no new information is generated
Data would result in major financial loss, damage or reputational impact to a data subject, Deloitte or Deloitte’s clients in the event of data loss/breach

---

## 投影片 22

The following criteria has been developed for firms to consider in developing the definitions for their prohibited risk AI systems

DELOITTE RISK CONSIDERATIONS

AI systems that do not align with Deloitte’s ethical guidelines and / or purpose. Please see our Global Principles of Business Conduct for more information

EU AI ACT CONSIDERATIONS

AI systems that involve at least one of the following1:
Deploys subliminal, manipulative, or deceptive techniques
Exploits vulnerable persons due to factors such as age, ethnicity, socioeconomic status, etc.
Conducts social scoring
Assesses the risk of a person committing a criminal offence
Creates or expands facial recognition databases through untargeted scraping of facial images from the internet or CCTV footage
Infers the emotions or sentiments of a person in the areas of workplace and education institutions
Biometric categorization that categorizes individuals based on their biometric data to infer the personal characteristics of a person (e.g. race, political opinions, etc.)
Real-time biometric identification systems used in publicly accessible spaces for the purposes of law enforcement
1The included considerations are summary descriptions of the current EU AI Act; please note some use cases may be permissible if exception conditions are met

DATA CONSIDERATIONS

Data used satisfies any of the following requirements:
Systems where personal information is to be used for purposes, or for which the information will be processed by, or in connection with, is incompatible with the purpose for which the personal information was originally collected
Deloitte systems where employee personal information is used without specific approval from Quality & Risk, Office of General Counsel and / or Talent
Systems where open source or proprietary information is protected by trademark or copyright or governed by specific license terms that restrict the way in which the information can be used, and for which consent to use has not been obtained
Systems that use data not in adherence with what has been agreed to in a client contract

---

## 投影片 23

### RISK THRESHOLD ASSESSMENT

Guidance for how to develop a risk threshold assessment

---

## 投影片 24

### What is a use case risk threshold assessment?

A preliminary risk assessment to determine the initial risk level (e.g., low / normal, medium, high, and prohibited) and identify what risk review activities are needed to effectively assess and mitigate risks for the different risk levels

What are the benefits of having a use case risk threshold assessment?

Triage use cases to determine the appropriate level of risk review activities required, and allocate resources effectively (e.g., in–depth risk review for high risk use cases)
Accelerate speed–to–market for low / normal risk use cases
Engage early with use case owners educate use case owners and to capture correct information for effective use case review

Suggested activities for firms to implement risk threshold assessment
Risk threshold assessment format examples

The risk threshold assessment enables firms to triage use cases based on risk level and determine the appropriate risk review activities that should then occur

Review the subsequent slides for:

---

## 投影片 25

Suggested firm activity: guidance and suggested activities have been developed to support the implementation or enhancement of a risk threshold assessment

Evaluate your firm’s existing risk threshold assessment against the guidance in this playbook to identify if there are any enhancements you want to make to your existing assessment (please reference the risk threshold assessment format examples in the subsequent slides)

Step 1

Define which questions will be included in the risk threshold assessment. Questions should be clear and specific and consider risk levels and risk considerations that have been defined for your firm (please reference the example risk threshold assessments in the subsequent slides)
Design questions that assess the potential impact and likelihood of each risk and help the use case owner answer “what could go wrong?”
Determine if there are any regulatory requirements (e.g., EU AI Act) that should be considered in building your risk threshold assessment

Step 1

Establish a clear and consistent method to evaluate the risk threshold assessment
Consider how you will use the information provided to determine an initial risk level (e.g., quantitative measures, qualitative measures, etc.).

Step 2

Implement the risk threshold assessment and develop communication materials (e.g., guidelines or FAQ) to address questions users may have

Step 3

Define questions to include in the risk threshold assessment

Define how the risk threshold assessment will be evaluated / scored

Implement the risk threshold assessment

Option 1: A risk threshold assessment is already in place

Option 2: A risk threshold assessment is not already in place

See next slide for risk threshold assessment format examples

---

## 投影片 26


| Generative AI Triage Risk Questions | Delete as appropriate |  | Risk–level |
| --- | --- | --- | --- |
| Does your use case or its outputs relate to any of the following activities which are prohibited (other than in a few specific exceptions*) under the EU AI Act? deploying subliminal, manipulative, or deceptive techniques to distort behaviour and impair informed decision–making, causing significant harm exploiting vulnerabilities related to age, disability, or socio–economic circumstances to distort behaviour, causing significant harm social scoring, (i.e., evaluating or classifying individuals or groups based on social behaviour or personal traits) assessing the risk of an individual committing criminal offences solely based on profiling or personality traits compiling or expanding facial recognition databases by untargeted scraping of facial images from the internet or CCTV footage. inferring emotions in workplaces or educational institutions biometric categorisation systems inferring sensitive attributes (e.g., race, political opinions, trade union membership, religious or philosophical beliefs, sex life, or sexual orientation) ‘real–time’ remote biometric identification (RBI) in publicly accessible spaces for law enforcement Note that use cases relating to prohibited activities would not be expected to be authorised. | Yes | No |  |
| Is there the potential for significant public scrutiny or interest in the use case? Namely, could the use case potentially: create significant media coverage of Deloitte’s involvement and/or association; involve clients, sectors, or types of service, which could be detrimental to Deloitte’s brand and reputation by our engagement, association, or public perception; be of significant interest to the public; lead to regulatory investigation of Deloitte and/or our client (this could be amplified in combination with all points above) | Yes | No |  |
| Could the operation of the GenAI system threaten the safety, livelihoods, or rights of people / groups of people? When answering this question also consider the potential impact on people of inaccuracies, errors, bias, failure or misuse in the operation of the GenAI system (or the use of its outputs). | Yes | No |  |
| If the answer to question 7 was ‘no’, does your use case relate to either of the following purposes? Use GenAI to develop or enhance the way we deliver our business to clients Use GenAI in relation to existing or new client engagements | Yes | No |  |
| If the answer to question 7 was ‘no’, do any of the following apply to the GenAI application / system / tool in your use case? It would utilize a Deloitte–provisioned license (i.e., not a client–provisioned license): but the technology / platform is not in the current list of the firm’s approved Gen AI platforms / tools; OR It is in the list of the firm’s current approved GenAI platforms; however, the model or tool to be used is in ‘preview’ rather than general availability; OR It is in the list of the firm’s current approved GenAI platforms; however, the anticipated usage is outside of the published guardrails for the platform / tool. | Yes | No |  |


|  High Risk |  Medium Risk |
| --- | --- |

Examples of different risk threshold assessment formats have been included to further support firms in developing their risk threshold assessment, including a survey format…

Consists of quantitative yes / no questions
The use case risk level is determined by pre–set criteria of how the yes / no questions are answered, for example:
Low / normal risk - all questions are answered “no”
Medium risk - one “medium risk” question gets answered “yes”
High risk - one “high risk” question gets answered “yes”, or multiple “medium risk” questions get answered “yes”
Risk reviewer typically engages with the use case owner to support proper completion

Overview of how the survey format works:

This is a sample, please refer to the full survey format risk threshold assessment example

Example risk threshold assessment

---

## 投影片 27


| AI Risk Treshold Assessment |  |
| --- | --- |
| Which group does this GenAI use case relate to?​ | Click or tap here to enter text. |
| Briefly describe the business use case | Click or tap here to enter text. |
| Do any of the following apply? | ☐ The AI solution will integrate with other systems containing confidential/sensitive data. ☐ There is an element of automated decision–making (even if there is human review following the automated decision process) ☐ A small, medium or Large Language Model (LLM) is being trained/fine–tuned. ☐ The solution will be client–facing. ☐ None of the above |
| Do you have business leader support for the development and funding of this use case?*​​​ | ☐ Yes ☐ No |
| What is the name and role of your business leader? | Click or tap here to enter text. |
| Where will the solution be deployed? | Choose an item. |
| Which Deloitte or third–party GenAI platform/tools/features will be used? *​​​​​​​​ | ☐ AWS (Bedrock) ☐ AWS (Code Whisperer) ☐ GitHub Copilot ☐ Google (Vertex AI Services) ☐ Microsoft 365 Copilot ☐ Microsoft Azure Open AI ☐ Deloitte ☐ Other |
| If Deloitte or other, please describe | Click or tap here to enter text. |
| Are the vendors/platforms being used under a Deloitte Global agreement? | ☐ Yes ☐ No |
| If this is a hosted solution, where will it be hosted? | Click or tap here to enter text. |

### …a qualitative risk threshold assessment format…

Risk threshold assessment includes various question types (e.g., descriptions, checkboxes, yes/no)
The risk reviewer determines the risk level and if SMA engagement is needed o conduct a thorough review based on the qualitative answers

Overview of how the qualitative format works:

This is a sample, please refer to the full qualitative format risk threshold assessment example

Example risk threshold assessment

---

## 投影片 28

### …and an Excel based format

Combines both yes / no questions with qualitative comments to provide thorough detail where appropriate
Excel based functionality built in to automatically determine the risk level based on answers to the yes / no questions
Option to include additional tabs to provide supplementary information for users (e.g., guidance, etc.)

Overview of how the Excel format works

This is a sample, please refer to the full Excel format risk threshold assessment example

Example risk threshold assessment

---

## 投影片 29

### AI USE CASE FORM

Guidance for how to leverage the existing Global AI use case form or develop a use case form

---

## 投影片 30

An AI use case form enables detailed information to be collected on use cases to support a thorough risk review and evaluation

What is an AI use case form?

The AI use case form captures detailed information (e.g., use case objectives, scope, technical system and model details, data used, etc.) needed to conduct a comprehensive risk review for medium and high risk use cases
The AI use case form is aligned to Deloitte’s Trustworthy AI Framework

What are the benefits of using an AI use case form?

Drive a consistent and structured approach to conducting comprehensive AI risk reviews for medium and high risk use cases
Foster ethical AI development and implementation by aligning risk reviews with the Trustworthy AI Framework and ethical technology principles
Facilitate discussion and clarification between use case owners and risk reviewers to drive better decision making and a shared understanding of the AI use case

Suggested activities for firms to implement an AI use case form
Link to the Global use case form for reference

Review the subsequent slides for:

---

## 投影片 31

Suggested firm activity: guidance and suggested activities have been developed to help firms develop their own AI use case form

Reference the Global use case form to develop or adapt your firm’s risk review form to conduct a detailed AI use case risk review
When reviewing the form, consider whether it captures all the necessary information required to effectively assess AI use cases, such as data requirements, potential risks, and ethical considerations and can be easily understood by those completing the form.

Step 1

Develop communication materials (e.g., guidelines or FAQs) to address any questions or concerns that may arise during implementation or as use case owners are completing the form
Educate teams on the purpose and use of your firm’s form to facilitate understanding and proper completion

Step 2

The current Global use case form will undergo a review and update to increase clarity and ease of use for sections / questions highlighted during the development of this playbook (e.g., trustworthy AI questions). The updates will be completed by end of FY25 and will be included in Phase 2 of the playbook. Please regularly review the latest version of this form to keep it up to date

Step 3

See the next slide for a sample of the Global use case form

---

## 投影片 32

The following provides an overview and sample of the current Global use case form that firms can consider in developing their own use case form

The form was developed with firms and global businesses to provide a structured approach to evaluating use cases, embedding trustworthy AI framework and ethical technology principles into risk considerations
The form includes qualitative questions that are reviewed by the risk reviewer and relevant SMAs

Overview of the Global use case Form

This is a sample, please refer to the full Global use case form

Please note that the Global use case form will be going through a review and update in collaboration with firms
to be released by end of FY25

---

## 投影片 33

### AI SYSTEMS INVENTORY

Guidance for developing an AI systems inventory

---

## 投影片 34

An AI systems inventory provides a comprehensive list of all systems that incorporate AI within a firms and sets a strong foundation for AI governance and regulatory compliance

What is an AI systems inventory?

An AI systems inventory documents all systems that incorporate AI within an organization and is an important step is developing strong foundation for AI governance practices and supports compliance with regulatory requirements
An AI systems inventory should reference all systems that contain AI, and their approved and specific use cases, data processed, scope of use, inclusive of both function and geography, and risk level
As regulations are further analyzed pertaining to AI systems inventories (e.g., EU AI Act), further guidance will be provided on inventory requirements; however, in the meantime, it is suggested that firms establish and maintain an AI systems inventory to support risk management and future regulatory compliance requirements

What are the benefits of having an AI systems inventory?

Supports strategic utilization of AI
Provides a comprehensive view of how AI is used across an organization, enabling responsible and optimized implementation and resource management by identifying existing solutions and use cases that can be reused or adapted
Lays the foundation for AI Governance
Helps support compliance with relevant regulations and ethical guidelines related to AI development and deployment
Enables regulatory compliance
Prepares organizations for regulatory compliance where applicable (e.g., EU AI Act high risk use case compliance, GDPR Record of Processing Act)

Suggested activities to establish and maintain an AI systems inventory
Suggested information to capture in an AI systems inventory

Review the subsequent slides for:

---

## 投影片 35

Suggested firm activity: guidance and suggested activities have been developed to help firms establish an AI systems inventory

The following provides suggested guidance to consider as part of developing your firm’s AI systems inventory

Consider what information should be captured in your firm’s AI systems inventory. Firms that fall under regulations such as the GDPR or the EU AI Act should reference any applicable requirements in developing their inventories.
Engage with relevant stakeholders such as OGC, confidentiality & privacy, ethics and DTech team members
Suggestions for information to include in the AI systems inventory can be found on the subsequent slide

Step 1

Determine if your currently have a tool/method/process in place that can be leveraged, if not, determine what tool will be used to establish your AI systems inventory (e.g., excel, online tool, extension of an existing technology library)
Firms can consider if the following tools can be leveraged to develop their AI systems inventory:
Central Management Database (CMDB) – existing record of all internal technologies (please see appendix for additional information)
Record of Processing Activity (RoPA) – for GDPR compliant firms, consider if extending the RoPA to include your AI systems inventory requirements given the synergies (please see appendix for additional information)

Step 2

When defining the process for maintaining the AI Systems inventory, consider the following suggestions:
Assure that the AI systems inventory is embedded into the technology adoption process
Assign clear ownership and responsibility for maintaining the inventory; this could be a dedicated team or individual
Establish an inventory review process and cadence to capture updates (e.g., quarterly, annually, etc.)
Make sure that the inventory is easily accessible and available to relevant stakeholders and consider how appropriate access controls and permissions will be established

Step 3

---

## 投影片 36


| AI SYSTEMS INFORMATION | DESCRIPTION OF COMPONENT |
| --- | --- |
| AI systems owner | Person responsible for the AI system |
| AI systems or technology name | How the system will be known to users |
| Models used1 | Detail on models used and any third-party AI models integrated into the AI system, including the specific version(s) |
| AI use case name1 | How the use case/feature will be identified |
| Use case summary | Description of what the use case aims to achieve and how the problem is addressed |
| Use case scope | Provide detail on the operational and geographical scope of the use case |
| Risk level | Example: low / normal, medium, high and prohibited |
| AI systems status | Examples: Under development (e.g., proof of concept, internal pilot, limited pilot with few external users) In production Decommissioned |
| Data source | Describe data inputs and source(s) |
| Data use | Describe data generated and how outputs will be used |
| Monitoring & audit schedule | Provide detail on the required use case monitoring and auditing activities |

Suggestions have been included for information that firms can consider capturing in developing their own AI systems inventory

1Note that there may be multiple models and use cases associated with a single AI system

---

## 投影片 37

### WHERE TO GO FROM HERE

---

## 投影片 38

What can firms do next to align their AI use case risk review process and activities with the leading practices and guidance outlined in this playbook?

Next steps firms can take after reviewing this playbook

Implement an AI use case risk review process if one is not already in place
Review use case risk review levels and determine what can be leveraged in your firm
Develop and implement a risk threshold assessment if one is not already in place
Review the Global AI use case form and adapt it to your firm’s existing form or needs as necessary
Develop an AI systems inventory and determine what information should be captured

f you have any feedback you would like to provide on this playbook or any questions, please reach out to Alanna Hedden.

---

## 投影片 39

Additional resources and additional information have been provided to support firms in using the content and guidance provided in this playbook

Explore Deloitte’s Trustworthy AI Eminence material here: Trustworthy AI™ framework
Explore the new “Trust in the Era of Generative AI” video mini–series and report here: Trust in the era of Generative AI
Explore Deloitte’s Generative AI Fluency Program here: Generative AI Fluency
Explore the globally available environments here: Generative AI Environments and Platforms Page
Explore the vendor guardrails that have been put in place here: Generative AI
Download the guide for Generative AI Independence Considerations here: Generative AI Independence
Explore Deloitte’s Trustworthy AI Framework training material here: Deloitte’s Trustworthy AI Framework Training and Guidance
Explore the “Deloitte’s Trustworthy AI Framework Webinar” here: Deloitte’s Trustworthy AI Framework Webinar

---

## 投影片 40

### APPENDIX

---

## 投影片 41

Deloitte’s PM40–Security and Configuration Management Database are existing policies and frameworks firms can reference in developing their risk review process and supporting activities

What is the Deloitte Policy Manual PM40–Security?

This policy provides a set of principles, obligations, and responsibilities regarding security, applicable to Deloitte Global, the Central Entities and Member Firms and relevant Third Parties and has been developed in alignment with international security standards (e.g., ISO 27001 and ISO 27002) and in accordance with general business requirements and relevant laws and regulations that firms can reference in developing their risk review activities including risk levels and AI systems inventory
PM40 outlines guidance on steps that firms should take to establish to protect our people, our facilities, the Deloitte brand, and maintaining the availability, confidentiality and integrity of information assets including internal and client confidential information, systems and tools on which the business depends
Please see PM40–Security for more information

What is the Configuration Management Database (CMDB)?

The Configuration Management Database (CMDB) aims to create a digital representation of Deloitte Technology's products and services, ensuring that teams across the organization can utilize common data to effectively operate, protect, and secure the IT Services delivered to our customers
When developing an AI systems inventory, firms should consider how the AI systems inventory could connect to the CMDB to drive efficiencies
Please see CMDB for more information

---

## 投影片 42

The General Data Protection Regulation (GDPR) mandates a Record of Processing Activity (RoPA); given the synergies with an AI systems inventory, GDPR compliant firms may want to consider extending for their RoPA to also be their AI systems inventory

What is the GDPR Record of Processing Activity (RoPA)?

The RoPA documents the processing activities of personal data and forms a critical pillar of personal data governance
The RoPA contains at a minimum:
Organization’s name, contact and whether it is a controller or a processor
Purposes of processing
Description of the categories of individuals, personal data and recipients of personal data
Details on record of transfer to third countries and safeguards in place
Retention schedules; and
Description of the technical and organizational security measures in place

How can firms leverage the Record of Processing Activity (RoPA) to develop their AI systems inventory?

Firms could choose to extend their existing RoPA to include the elements they would want captured as part of an AI systems inventory
Similar to what would be needed for an AI systems inventory, the RoPA
The RoPA framework allows for there to be a “one to many” relationship between AI systems and use cases that would support a comprehensive AI systems inventory
Like an AI systems inventory, the data contained within the RoPA supports compliance readiness

---

## 投影片 43

Deloitte refers to one or more of Deloitte Touche Tohmatsu Limited ("DTTL"), its global network of member firms, and their related entities (collectively, the "Deloitte organization"). DTTL (also referred to as "Deloitte Global") and each of its member firms and related entities are legally separate and independent entities, which cannot obligate or bind each other in respect of third parties. DTTL and each DTTL member firm and related entity is liable only for its own acts and omissions, and not those of each other. DTTL does not provide services to clients. Please see www.deloitte.com/about to learn more.
Deloitte provides industry–leading audit and assurance, tax and legal, consulting, financial advisory, and risk advisory services to nearly 90% of the Fortune Global 500® and thousands of private companies. Our people deliver measurable and lasting results that help reinforce public trust in capital markets, enable clients to transform and thrive, and lead the way toward a stronger economy, a more equitable society, and a sustainable world. Building on its 175–plus year history, Deloitte spans more than 150 countries and territories. Learn how Deloitte’s approximately 457,000 people worldwide make an impact that matters at www.deloitte.com.
This communication contains general information only, and none of Deloitte Touche Tohmatsu Limited ("DTTL"), its global network of member firms or their related entities (collectively, the "Deloitte organization") is, by means of this communication, rendering professional advice or services. Before making any decision or taking any action that may affect your finances or your business, you should consult a qualified professional adviser.
No representations, warranties or undertakings (express or implied) are given as to the accuracy or completeness of the information in this communication, and none of DTTL, its member firms, related entities, employees or agents shall be liable or responsible for any loss or damage whatsoever arising directly or indirectly in connection with any person relying on this communication. DTTL and each of its member firms, and their related entities, are legally separate and independent entities.
© 2024. For information, contact Deloitte Global.

---

# 文件轉換工具 v2.0

將 PDF、DOCX、PPTX 文件轉換為高品質 Markdown 格式的專業工具。

## 🌟 v2.0 主要特色

### 📄 格式結構完整還原
- **標題層級**: 自動識別並轉換為正確的 Markdown 標題
- **清單結構**: 支援有序和無序清單的完整轉換
- **表格格式**: 轉為標準 Markdown 表格，保持結構完整
- **圖片處理**: 提取圖片並以 Markdown 語法引用

### 🎯 特殊內容處理
- **PDF分欄**: 智能處理多欄位佈局
- **頁眉頁腳**: 自動識別並移除頁碼、版權等信息
- **DOCX批註**: 提取並單獨顯示文檔批註
- **DOCX腳註**: 處理腳註內容
- **PPTX投影片**: 按投影片分頁組織內容

### 🌏 編碼與語言支援
- **UTF-8編碼**: 確保中文字元正確處理
- **多語言支援**: 支援中英文混合文檔
- **特殊字符**: 正確處理各種特殊符號

### 📊 智能內容識別
- **標題檢測**: 根據樣式、字體大小、內容模式判斷
- **列表識別**: 自動識別項目符號和數字編號
- **表格優化**: 智能格式化表格內容
- **頁眉頁腳過濾**: 多種模式匹配，避免無關內容

## 📦 安裝要求

### Python 環境
- Python 3.6 或更高版本

### 依賴套件
```bash
pip install python-docx pdfplumber PyPDF2 python-pptx Pillow
```

或使用自動安裝：
```bash
python install_dependencies_v2.py
```

## 🚀 使用方法

### 快速開始
```bash
# 使用批次處理腳本（推薦）
convert_documents_v2.bat

# 直接使用Python
python document_converter_v2.py
```

### 命令行選項
```bash
# 批次轉換所有文件
python document_converter_v2.py

# 指定目錄
python document_converter_v2.py -i docs -o markdown --images img

# 轉換單個文件
python document_converter_v2.py -f document.pdf

# 詳細模式
python document_converter_v2.py --verbose

# 清理臨時文件
python document_converter_v2.py --cleanup
```

## 📁 目錄結構

```
.
├── input/                    # 放置要轉換的文件
│   ├── document.pdf
│   ├── presentation.pptx
│   └── report.docx
├── output/                   # 轉換後的Markdown文件
│   ├── document_extracted.md
│   ├── presentation_extracted.md
│   ├── report_extracted.md
│   ├── conversion_summary.md
│   └── conversion_report.json
├── images/                   # 提取的圖片文件
│   ├── document_image1.png
│   └── presentation_image1.jpg
├── document_converter_v2.py
├── install_dependencies_v2.py
├── convert_documents_v2.bat
└── document_converter_v2.log
```

## 🔧 轉換效果

### PDF 轉換
- ✅ 使用 pdfplumber 優先，PyPDF2 備用
- ✅ 智能分欄處理
- ✅ 表格結構提取
- ✅ 頁眉頁腳自動過濾
- ✅ 標題自動識別

### DOCX 轉換
- ✅ 保持文檔元素順序
- ✅ 標題樣式正確轉換
- ✅ 表格格式完整保留
- ✅ 文字格式（粗體、斜體）
- ✅ 批註和腳註提取

### PPTX 轉換
- ✅ 按投影片分頁組織
- ✅ 標題和內容分離
- ✅ 項目符號列表處理
- ✅ 表格內容提取
- ✅ 投影片分隔標記

## 📊 輸出格式

### 文件命名
- 原文件：`document.pdf`
- 轉換結果：`document_extracted.md`

### Markdown 結構
```markdown
# 文檔標題

## 第 1 頁

### 章節標題

段落內容...

| 表格標題1 | 表格標題2 |
|----------|----------|
| 內容1    | 內容2    |

![圖片描述](images/document_image1.png)

- 列表項目1
- 列表項目2

---

## 腳註

1. 腳註內容...

## 批註

- 批註內容...
```

## 📋 轉換報告

### JSON 報告 (`conversion_report.json`)
```json
{
  "conversion_date": "2024-01-01T12:00:00",
  "statistics": {
    "total_files": 3,
    "successful": 3,
    "failed": 0
  },
  "results": [
    {
      "input_file": "input/document.pdf",
      "output_file": "output/document_extracted.md",
      "file_type": "PDF",
      "success": true,
      "pages_count": 10,
      "tables_count": 2,
      "conversion_time": 1.23
    }
  ]
}
```

### Markdown 報告 (`conversion_summary.md`)
- 轉換統計
- 成功率分析
- 詳細轉換表格
- 失敗原因分析

## 🐛 故障排除

### 常見問題

**Q: 轉換後中文顯示亂碼**
A: 確保使用 UTF-8 編碼，Windows 用戶建議使用 PowerShell

**Q: PDF 表格轉換不完整**
A: v2.0 使用 pdfplumber 大幅改善表格提取，複雜表格可能需要人工校對

**Q: DOCX 格式丟失**
A: v2.0 保留粗體、斜體等基本格式，複雜格式會簡化

**Q: 轉換速度慢**
A: 大文件或包含大量圖片的文件轉換較慢，屬正常現象

### 日誌檢查
```bash
# 查看轉換日誌
Get-Content document_converter_v2.log -Tail 20

# 查看轉換報告
Get-Content output/conversion_summary.md
```

## 🔄 版本對比

| 功能 | v1.0 | v2.0 |
|------|------|------|
| 文字分離 | 基本 | ✅ 智能分離 |
| 表格處理 | 簡單 | ✅ 結構完整 |
| 頁眉頁腳 | 無處理 | ✅ 智能過濾 |
| 標題識別 | 樣式判斷 | ✅ 多重判斷 |
| 圖片處理 | 無 | ✅ 提取引用 |
| 錯誤處理 | 基本 | ✅ 完整報告 |
| 批註腳註 | 無 | ✅ 單獨提取 |
| 編碼支援 | 基本 | ✅ UTF-8完整 |

## 🎯 最佳實務

1. **文件準備**
   - 確保文件沒有密碼保護
   - 檢查文件完整性
   - 避免同時開啟文件

2. **批次處理**
   - 將相關文件放在同一目錄
   - 使用一致的命名規則
   - 定期清理臨時文件

3. **結果檢查**
   - 查看轉換報告確認成功率
   - 檢查重要表格和圖片
   - 驗證中文字元顯示

4. **效能優化**
   - 大文件分批處理
   - 定期清理輸出目錄
   - 使用 SSD 提升速度

## 📈 技術架構

### 處理流程
1. 文件格式檢測
2. 對應解析器調用
3. 內容結構化處理
4. 特殊內容過濾
5. Markdown 格式轉換
6. UTF-8 編碼輸出
7. 報告生成

### 核心技術
- **pdfplumber**: PDF 文字和表格提取
- **python-docx**: DOCX 文檔解析
- **python-pptx**: PPTX 簡報處理
- **Pillow**: 圖片處理
- **正則表達式**: 內容模式匹配

文件轉換工具 v2.0 為您提供專業級的文檔轉換體驗！🎉

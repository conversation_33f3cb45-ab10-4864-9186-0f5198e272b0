# 文件轉換工具 - DOCX/PDF/PPTX to Markdown

這個工具可以將 Microsoft Office 文件（DOCX、PDF、PPTX）自動轉換為 Markdown 格式，特別適合用於文件分析和處理。

## 🌟 功能特色

- 📄 **多格式支援**：DOCX、PDF、PPTX
- 🔄 **批量轉換**：一次處理多個文件
- ⚡ **增量處理**：只處理新文件或已變更的文件
- 🧹 **自動清理**：清理臨時文件和快取
- 📊 **轉換記錄**：保留轉換歷史和狀態
- 🎯 **智能結構**：保留文檔的基本結構

## 📦 安裝要求

### Python 環境
- Python 3.6 或更高版本

### 依賴套件
工具會自動安裝以下套件：
```bash
pip install python-docx PyPDF2 python-pptx
```

## 🚀 快速開始

### 方法1：使用批次處理腳本（推薦）

#### Windows 命令提示符
```bash
convert_documents.bat
```

#### PowerShell
```powershell
.\convert_documents.ps1
```

### 方法2：直接使用Python

#### 安裝依賴套件
```bash
python install_dependencies.py
```

#### 轉換所有文件
```bash
python document_converter.py
```

## 📁 目錄結構

```
.
├── input/                    # 放置要轉換的文件
│   ├── document1.docx
│   ├── presentation.pptx
│   └── report.pdf
├── output/                   # 轉換後的Markdown文件
│   ├── document1.md
│   ├── presentation.md
│   └── report.md
├── document_converter.py     # 主轉換程式
├── install_dependencies.py  # 依賴套件安裝程式
├── convert_documents.bat    # Windows批次處理腳本
├── convert_documents.ps1    # PowerShell腳本
├── conversion_cache.json    # 轉換快取（自動生成）
└── converter.log           # 轉換日誌（自動生成）
```

## 🔧 使用方法

### 基本用法

1. **準備文件**：將要轉換的 DOCX、PDF、PPTX 文件放入 `input` 目錄
2. **執行轉換**：運行批次處理腳本或Python程式
3. **查看結果**：轉換後的 Markdown 文件會出現在 `output` 目錄

### 進階選項

```bash
# 轉換所有文件（增量模式）
python document_converter.py

# 指定輸入和輸出目錄
python document_converter.py -i docs -o markdown

# 轉換單個文件
python document_converter.py -f document.docx

# 強制重新轉換所有文件
python document_converter.py --force

# 清理臨時文件
python document_converter.py --cleanup

# 查看幫助
python document_converter.py --help
```

## 📋 轉換效果

### DOCX 轉換
- ✅ 段落文字
- ✅ 標題層級（Heading 1-6）
- ✅ 表格結構
- ✅ 基本格式

### PDF 轉換
- ✅ 文字內容
- ✅ 分頁結構
- ⚠️ 圖片和複雜排版可能丟失

### PPTX 轉換
- ✅ 投影片標題
- ✅ 文字內容
- ✅ 項目符號列表
- ✅ 投影片分隔

## 🔄 增量處理

工具使用檔案雜湊值來追蹤文件變更：
- 只處理新文件或已變更的文件
- 跳過未變更的文件，提高效率
- 快取資訊保存在 `conversion_cache.json`

## 🧹 清理功能

自動清理以下臨時文件：
- `*.tmp` - 臨時文件
- `~$*` - Office 臨時文件
- `.~*` - 隱藏臨時文件

## 📊 轉換選項說明

### 批次處理腳本選項

1. **增量轉換**：只轉換新文件或已變更的文件
2. **強制重新轉換**：重新轉換所有文件，忽略快取
3. **清理後轉換**：先清理臨時文件，再強制轉換
4. **只清理**：只清理臨時文件，不進行轉換

## 🐛 故障排除

### 常見問題

**Q: 轉換失敗，提示缺少依賴套件**
```bash
# 手動安裝依賴套件
pip install python-docx PyPDF2 python-pptx

# 或運行安裝腳本
python install_dependencies.py
```

**Q: PDF 轉換效果不佳**
A: PDF 轉換依賴文件的文字層，掃描版PDF或圖片PDF效果較差

**Q: DOCX 表格格式丟失**
A: 工具保留基本表格結構，複雜格式會簡化為Markdown表格

**Q: 轉換後的中文顯示異常**
A: 確保使用UTF-8編碼，Windows用戶建議使用PowerShell

### 日誌檢查

查看 `converter.log` 文件獲取詳細錯誤信息：
```bash
# Windows
type converter.log

# PowerShell
Get-Content converter.log

# 查看最新日誌
Get-Content converter.log -Tail 20
```

## 🔧 自定義配置

### 修改轉換邏輯

編輯 `document_converter.py` 中的轉換方法：
- `convert_docx_to_md()` - DOCX轉換邏輯
- `convert_pdf_to_md()` - PDF轉換邏輯  
- `convert_pptx_to_md()` - PPTX轉換邏輯

### 添加新的文件類型

在 `DocumentConverter` 類中：
1. 添加新的副檔名到 `supported_extensions`
2. 實作對應的轉換方法
3. 在 `convert_file()` 中添加處理邏輯

## 📈 效能優化

- **增量處理**：避免重複轉換未變更的文件
- **快取機制**：記錄轉換狀態，提高後續處理速度
- **批量處理**：一次處理多個文件
- **記憶體管理**：逐個處理文件，避免記憶體溢出

## 🤝 使用建議

1. **定期清理**：定期清理臨時文件和快取
2. **備份重要文件**：轉換前備份重要文件
3. **檢查結果**：轉換後檢查Markdown格式是否正確
4. **批量處理**：將相關文件放在同一目錄批量處理

## 📝 更新日誌

- v1.0: 初始版本，支援DOCX、PDF、PPTX轉換
- 支援增量處理和快取機制
- 添加自動清理功能
- 提供批次處理腳本

## 🆘 技術支援

如果遇到問題：
1. 檢查Python版本是否為3.6+
2. 確認依賴套件已正確安裝
3. 查看 `converter.log` 日誌文件
4. 確保文件格式正確且未損壞

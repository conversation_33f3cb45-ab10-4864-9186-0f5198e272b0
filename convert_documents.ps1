# 文件轉換工具 - PowerShell版本
# 將 DOCX、PDF、PPTX 轉換為 Markdown

[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Document Converter - DOCX/PDF/PPTX to Markdown" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 檢查Python是否可用
try {
    $pythonVersion = python --version 2>&1
    Write-Host "[INFO] Found Python: $pythonVersion" -ForegroundColor Green
}
catch {
    Write-Host "[ERROR] Python not found, please install Python 3.6+" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# 檢查並安裝依賴套件
Write-Host "[INFO] Checking dependencies..." -ForegroundColor Blue
try {
    python install_dependencies.py
    if ($LASTEXITCODE -ne 0) {
        Write-Host "[WARNING] Some dependencies may have installation issues" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "[WARNING] Error checking dependencies: $_" -ForegroundColor Yellow
}

Write-Host ""

# 創建必要的目錄
if (-not (Test-Path "input")) {
    New-Item -ItemType Directory -Name "input" | Out-Null
    Write-Host "[INFO] Created input directory" -ForegroundColor Green
}

if (-not (Test-Path "output")) {
    New-Item -ItemType Directory -Name "output" | Out-Null
    Write-Host "[INFO] Created output directory" -ForegroundColor Green
}

Write-Host ""
Write-Host "[INFO] Directory structure:" -ForegroundColor Cyan
Write-Host "- input/  : Place files to convert (DOCX, PDF, PPTX)" -ForegroundColor White
Write-Host "- output/ : Converted Markdown files" -ForegroundColor White
Write-Host ""

# 檢查input目錄中的文件
$supportedFiles = Get-ChildItem -Path "input" -Include "*.docx", "*.pdf", "*.pptx" -File
$fileCount = $supportedFiles.Count

if ($fileCount -eq 0) {
    Write-Host "[INFO] No supported files found in input directory" -ForegroundColor Yellow
    Write-Host "Please place DOCX, PDF, or PPTX files in the input directory" -ForegroundColor Yellow
    Write-Host ""
    $openInput = Read-Host "Open input directory? (y/n)"
    if ($openInput -eq "y" -or $openInput -eq "Y") {
        Start-Process "input"
    }
    exit 0
}

Write-Host "[INFO] Found $fileCount files to convert:" -ForegroundColor Green
foreach ($file in $supportedFiles) {
    Write-Host "  - $($file.Name)" -ForegroundColor White
}
Write-Host ""

# 轉換選項
Write-Host "Conversion options:" -ForegroundColor Cyan
Write-Host "1. Convert all files (incremental mode)" -ForegroundColor White
Write-Host "2. Force reconvert all files" -ForegroundColor White
Write-Host "3. Cleanup temp files then convert" -ForegroundColor White
Write-Host "4. Only cleanup temp files" -ForegroundColor White
Write-Host ""

$choice = Read-Host "Please choose (1-4)"

Write-Host ""
Write-Host "[INFO] Starting processing..." -ForegroundColor Blue

# 執行轉換
try {
    switch ($choice) {
        "1" {
            Write-Host "[MODE] Incremental conversion" -ForegroundColor Yellow
            python document_converter.py
        }
        "2" {
            Write-Host "[MODE] Force reconvert all" -ForegroundColor Yellow
            python document_converter.py --force
        }
        "3" {
            Write-Host "[MODE] Cleanup then convert" -ForegroundColor Yellow
            python document_converter.py --cleanup --force
        }
        "4" {
            Write-Host "[MODE] Cleanup only" -ForegroundColor Yellow
            python document_converter.py --cleanup
        }
        default {
            Write-Host "[MODE] Default incremental conversion" -ForegroundColor Yellow
            python document_converter.py
        }
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "[SUCCESS] Conversion completed!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Converted files are saved in the output directory" -ForegroundColor Cyan
        Write-Host ""
        
        $openOutput = Read-Host "Open output directory to view results? (y/n)"
        if ($openOutput -eq "y" -or $openOutput -eq "Y") {
            Start-Process "output"
        }
    }
    else {
        Write-Host ""
        Write-Host "[ERROR] Error occurred during conversion" -ForegroundColor Red
        Write-Host "Please check converter.log for detailed error information" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "[ERROR] Exception during conversion: $_" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"

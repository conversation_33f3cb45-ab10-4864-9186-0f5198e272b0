#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版實踐要點格式轉換工具
"""

import re

def convert_practice_points_simple(input_file, output_file):
    """
    簡化版轉換函數
    """
    
    # 讀取文件
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 正則表達式匹配實踐要點
    pattern = r'\*\*實踐要點\*\*：([^。\n]+)。?'
    
    def replace_function(match):
        content = match.group(1)
        points = content.split('；')
        points = [point.strip() for point in points if point.strip()]
        
        if len(points) <= 1:
            return match.group(0)
        
        result = "**實踐要點**：\n"
        for point in points:
            result += f"- {point}\n"
        
        return result.rstrip()
    
    # 執行替換
    converted_content = re.sub(pattern, replace_function, content)
    
    # 寫入文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(converted_content)
    
    # 統計
    original_count = len(re.findall(r'\*\*實踐要點\*\*：[^。\n]+；', content))
    print(f"轉換完成！共轉換了 {original_count} 個實踐要點")
    print(f"輸入文件: {input_file}")
    print(f"輸出文件: {output_file}")

if __name__ == "__main__":
    # 使用範例
    input_file = "TW_AI_Policy_v5.md"
    output_file = "TW_AI_Policy_v5_converted.md"
    
    try:
        convert_practice_points_simple(input_file, output_file)
    except FileNotFoundError:
        print(f"錯誤: 找不到文件 {input_file}")
        print("請確認文件存在於當前目錄中")
    except Exception as e:
        print(f"轉換失敗: {e}")

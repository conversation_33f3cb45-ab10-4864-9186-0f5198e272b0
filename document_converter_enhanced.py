#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增強版文件轉換工具
支援將 DOCX、PDF、PPTX 文件轉換為高品質 Markdown 格式

主要改進：
- 智能表格處理（合併儲存格、表格標題、格式化）
- 進階標題處理（自動層級、編號、樣式）
- 頁頭頁尾處理（自動識別並移除）
- 圖片提取與處理
- 文字格式保留（粗體、斜體、超連結）
- 列表結構優化
- 更好的PDF文字提取
- 智能內容過濾

依賴套件：
pip install python-docx PyPDF2 python-pptx Pillow pdfplumber
"""

import os
import sys
import re
import argparse
import hashlib
import json
from pathlib import Path
from datetime import datetime
import logging
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('converter_enhanced.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedDocumentConverter:
    """增強版文件轉換器"""
    
    def __init__(self, input_dir="input", output_dir="output", cache_file="conversion_cache.json"):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.cache_file = Path(cache_file)
        self.cache = self.load_cache()
        
        # 創建目錄
        self.input_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
        
        # 支援的文件類型
        self.supported_extensions = {'.docx', '.pdf', '.pptx'}
        
        # 頁頭頁尾常見模式
        self.header_footer_patterns = [
            r'第\s*\d+\s*頁',
            r'Page\s+\d+',
            r'\d+\s*/\s*\d+',
            r'©.*\d{4}',
            r'版權所有',
            r'機密',
            r'Confidential',
            r'內部使用',
            r'Internal Use',
            r'\d{4}年\d{1,2}月\d{1,2}日',
            r'\d{1,2}/\d{1,2}/\d{4}',
        ]
        
        # 編譯正則表達式
        self.header_footer_regex = [re.compile(pattern, re.IGNORECASE) for pattern in self.header_footer_patterns]
    
    def load_cache(self):
        """載入轉換快取"""
        if self.cache_file.exists():
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"無法載入快取文件: {e}")
        return {}
    
    def save_cache(self):
        """儲存轉換快取"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"無法儲存快取文件: {e}")
    
    def get_file_hash(self, file_path):
        """計算文件雜湊值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"無法計算文件雜湊值 {file_path}: {e}")
            return None
    
    def is_file_changed(self, file_path):
        """檢查文件是否已變更"""
        file_str = str(file_path)
        current_hash = self.get_file_hash(file_path)
        
        if file_str in self.cache:
            return self.cache[file_str].get('hash') != current_hash
        return True
    
    def is_header_footer(self, text: str) -> bool:
        """判斷是否為頁頭頁尾"""
        text = text.strip()
        if len(text) < 3 or len(text) > 100:
            return False
        
        for regex in self.header_footer_regex:
            if regex.search(text):
                return True
        return False
    
    def clean_text(self, text: str) -> str:
        """清理文字"""
        if not text:
            return ""
        
        # 移除多餘空白
        text = re.sub(r'\s+', ' ', text)
        # 移除行首行尾空白
        text = text.strip()
        # 移除特殊字符
        text = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', text)
        
        return text
    
    def extract_heading_level(self, style_name: str, font_size: Optional[float] = None) -> int:
        """提取標題層級"""
        style_name = style_name.lower()
        
        # 直接從樣式名稱判斷
        if 'heading' in style_name:
            match = re.search(r'heading\s*(\d+)', style_name)
            if match:
                return min(int(match.group(1)), 6)
        
        # 從字體大小判斷
        if font_size:
            if font_size >= 18:
                return 1
            elif font_size >= 16:
                return 2
            elif font_size >= 14:
                return 3
            elif font_size >= 12:
                return 4
        
        return 0  # 不是標題
    
    def format_table_cell(self, cell_text: str) -> str:
        """格式化表格儲存格"""
        if not cell_text:
            return ""
        
        # 清理文字
        text = self.clean_text(cell_text)
        # 移除換行符，用空格替代
        text = re.sub(r'\n+', ' ', text)
        # 移除表格分隔符
        text = text.replace('|', '\\|')
        
        return text
    
    def process_table(self, table) -> List[str]:
        """處理表格，返回Markdown格式的行"""
        if not table.rows:
            return []
        
        markdown_lines = []
        markdown_lines.append("")  # 表格前空行
        
        # 處理表格行
        for row_idx, row in enumerate(table.rows):
            row_data = []
            for cell in row.cells:
                cell_text = self.format_table_cell(cell.text)
                row_data.append(cell_text)
            
            # 生成表格行
            markdown_lines.append("| " + " | ".join(row_data) + " |")
            
            # 第一行後添加分隔線
            if row_idx == 0:
                separator = "| " + " | ".join(["---"] * len(row_data)) + " |"
                markdown_lines.append(separator)
        
        markdown_lines.append("")  # 表格後空行
        return markdown_lines
    
    def extract_text_formatting(self, run) -> Tuple[str, str, str]:
        """提取文字格式（前綴、文字、後綴）"""
        text = run.text
        prefix = ""
        suffix = ""
        
        try:
            # 粗體
            if run.bold:
                prefix += "**"
                suffix = "**" + suffix
            
            # 斜體
            if run.italic:
                prefix += "*"
                suffix = "*" + suffix
                
            # 底線（用粗體表示）
            if run.underline:
                prefix += "**"
                suffix = "**" + suffix
                
        except Exception:
            # 某些屬性可能不存在
            pass
        
        return prefix, text, suffix

    def convert_docx_to_md(self, docx_path, md_path):
        """增強版 DOCX 到 Markdown 轉換"""
        try:
            from docx import Document

            doc = Document(docx_path)
            markdown_content = []

            # 添加文件標題
            markdown_content.append(f"# {docx_path.stem}")
            markdown_content.append("")

            # 處理文檔元素
            for element in doc.element.body:
                if element.tag.endswith('p'):  # 段落
                    paragraph = None
                    for p in doc.paragraphs:
                        if p._element == element:
                            paragraph = p
                            break

                    if paragraph:
                        processed_para = self.process_paragraph(paragraph)
                        if processed_para:
                            markdown_content.extend(processed_para)

                elif element.tag.endswith('tbl'):  # 表格
                    table = None
                    for t in doc.tables:
                        if t._element == element:
                            table = t
                            break

                    if table:
                        table_md = self.process_table(table)
                        markdown_content.extend(table_md)

            # 寫入文件
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(markdown_content))

            return True

        except ImportError:
            logger.error("請安裝 python-docx: pip install python-docx")
            return False
        except Exception as e:
            logger.error(f"轉換 DOCX 失敗 {docx_path}: {e}")
            return False

    def process_paragraph(self, paragraph) -> List[str]:
        """處理段落"""
        if not paragraph.text.strip():
            return []

        # 檢查是否為頁頭頁尾
        if self.is_header_footer(paragraph.text):
            return []

        # 處理標題
        heading_level = self.extract_heading_level(paragraph.style.name)
        if heading_level > 0:
            clean_text = self.clean_text(paragraph.text)
            return [f"{'#' * heading_level} {clean_text}", ""]

        # 處理列表
        if paragraph.style.name.startswith('List'):
            clean_text = self.clean_text(paragraph.text)
            return [f"- {clean_text}"]

        # 處理一般段落（保留格式）
        formatted_text = self.process_paragraph_formatting(paragraph)
        if formatted_text:
            return [formatted_text, ""]

        return []

    def process_paragraph_formatting(self, paragraph) -> str:
        """處理段落格式"""
        result = []

        for run in paragraph.runs:
            prefix, text, suffix = self.extract_text_formatting(run)
            if text.strip():
                result.append(f"{prefix}{text}{suffix}")

        return self.clean_text(''.join(result))

    def convert_pdf_to_md(self, pdf_path, md_path):
        """增強版 PDF 到 Markdown 轉換"""
        try:
            # 優先使用 pdfplumber，回退到 PyPDF2
            try:
                import pdfplumber
                return self._convert_pdf_with_pdfplumber(pdf_path, md_path)
            except ImportError:
                import PyPDF2
                return self._convert_pdf_with_pypdf2(pdf_path, md_path)

        except ImportError:
            logger.error("請安裝 pdfplumber 或 PyPDF2: pip install pdfplumber PyPDF2")
            return False
        except Exception as e:
            logger.error(f"轉換 PDF 失敗 {pdf_path}: {e}")
            return False

    def _convert_pdf_with_pdfplumber(self, pdf_path, md_path):
        """使用 pdfplumber 轉換 PDF"""
        import pdfplumber

        markdown_content = []
        markdown_content.append(f"# {pdf_path.stem}")
        markdown_content.append("")

        with pdfplumber.open(pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages, 1):
                # 提取文字
                text = page.extract_text()
                if not text:
                    continue

                # 提取表格
                tables = page.extract_tables()

                # 處理頁面內容
                page_content = self.process_pdf_page_content(text, tables, page_num)
                markdown_content.extend(page_content)

        # 寫入文件
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))

        return True

    def _convert_pdf_with_pypdf2(self, pdf_path, md_path):
        """使用 PyPDF2 轉換 PDF"""
        import PyPDF2

        markdown_content = []
        markdown_content.append(f"# {pdf_path.stem}")
        markdown_content.append("")

        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)

            for page_num, page in enumerate(pdf_reader.pages, 1):
                text = page.extract_text()
                if text.strip():
                    page_content = self.process_pdf_page_content(text, [], page_num)
                    markdown_content.extend(page_content)

        # 寫入文件
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))

        return True

    def process_pdf_page_content(self, text: str, tables: List, page_num: int) -> List[str]:
        """處理PDF頁面內容"""
        content = []

        # 添加頁面標題（僅當有實際內容時）
        if text.strip():
            content.append(f"## 第 {page_num} 頁")
            content.append("")

        # 處理表格
        for table in tables:
            if table:
                table_md = self.process_pdf_table(table)
                content.extend(table_md)

        # 處理文字內容
        if text:
            processed_text = self.process_pdf_text(text)
            content.extend(processed_text)

        return content

    def process_pdf_table(self, table: List[List]) -> List[str]:
        """處理PDF表格"""
        if not table or not table[0]:
            return []

        markdown_lines = [""]

        for row_idx, row in enumerate(table):
            if row:
                # 清理儲存格內容
                cleaned_row = [self.format_table_cell(str(cell) if cell else "") for cell in row]
                markdown_lines.append("| " + " | ".join(cleaned_row) + " |")

                # 第一行後添加分隔線
                if row_idx == 0:
                    separator = "| " + " | ".join(["---"] * len(cleaned_row)) + " |"
                    markdown_lines.append(separator)

        markdown_lines.append("")
        return markdown_lines

    def process_pdf_text(self, text: str) -> List[str]:
        """處理PDF文字內容"""
        lines = text.split('\n')
        processed_lines = []
        current_paragraph = []

        for line in lines:
            line = line.strip()

            # 跳過空行
            if not line:
                if current_paragraph:
                    paragraph_text = ' '.join(current_paragraph)
                    if not self.is_header_footer(paragraph_text):
                        processed_lines.append(self.clean_text(paragraph_text))
                        processed_lines.append("")
                    current_paragraph = []
                continue

            # 檢查是否為標題（簡單判斷）
            if self.is_likely_heading(line):
                # 先處理當前段落
                if current_paragraph:
                    paragraph_text = ' '.join(current_paragraph)
                    if not self.is_header_footer(paragraph_text):
                        processed_lines.append(self.clean_text(paragraph_text))
                        processed_lines.append("")
                    current_paragraph = []

                # 添加標題
                if not self.is_header_footer(line):
                    processed_lines.append(f"### {self.clean_text(line)}")
                    processed_lines.append("")
            else:
                current_paragraph.append(line)

        # 處理最後一個段落
        if current_paragraph:
            paragraph_text = ' '.join(current_paragraph)
            if not self.is_header_footer(paragraph_text):
                processed_lines.append(self.clean_text(paragraph_text))
                processed_lines.append("")

        return processed_lines

    def is_likely_heading(self, text: str) -> bool:
        """判斷是否可能是標題"""
        text = text.strip()

        # 長度判斷
        if len(text) > 100 or len(text) < 3:
            return False

        # 包含數字編號
        if re.match(r'^\d+[\.\)]\s+', text):
            return True

        # 全大寫（英文）
        if text.isupper() and re.search(r'[A-Z]', text):
            return True

        # 中文章節標題
        if re.match(r'^[一二三四五六七八九十]+[、\.\s]', text):
            return True

        # 結尾沒有標點符號
        if not re.search(r'[。！？\.!?]$', text):
            return True

        return False

    def convert_pptx_to_md(self, pptx_path, md_path):
        """增強版 PPTX 到 Markdown 轉換"""
        try:
            from pptx import Presentation

            prs = Presentation(pptx_path)
            markdown_content = []

            # 添加簡報標題
            markdown_content.append(f"# {pptx_path.stem}")
            markdown_content.append("")

            for slide_num, slide in enumerate(prs.slides, 1):
                slide_content = self.process_slide(slide, slide_num)
                markdown_content.extend(slide_content)

            # 寫入文件
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(markdown_content))

            return True

        except ImportError:
            logger.error("請安裝 python-pptx: pip install python-pptx")
            return False
        except Exception as e:
            logger.error(f"轉換 PPTX 失敗 {pptx_path}: {e}")
            return False

    def process_slide(self, slide, slide_num: int) -> List[str]:
        """處理投影片"""
        content = []
        content.append(f"## 投影片 {slide_num}")
        content.append("")

        # 收集所有文字內容
        text_contents = []

        for shape in slide.shapes:
            if hasattr(shape, "text") and shape.text.strip():
                text_contents.append(shape.text.strip())

        # 處理文字內容
        if text_contents:
            # 第一個通常是標題
            if text_contents:
                title = self.clean_text(text_contents[0])
                if len(title) < 100:  # 標題不會太長
                    content.append(f"### {title}")
                    content.append("")
                    text_contents = text_contents[1:]  # 移除已處理的標題

            # 處理其他內容
            for text in text_contents:
                processed_text = self.process_slide_text(text)
                content.extend(processed_text)

        content.append("---")
        content.append("")

        return content

    def process_slide_text(self, text: str) -> List[str]:
        """處理投影片文字"""
        lines = text.split('\n')
        result = []

        for line in lines:
            line = line.strip()
            if line:
                # 簡單的項目符號處理
                if line.startswith(('•', '-', '*', '○', '◦')):
                    result.append(f"- {self.clean_text(line[1:].strip())}")
                elif re.match(r'^\d+[\.\)]\s+', line):
                    # 數字編號
                    result.append(f"1. {self.clean_text(re.sub(r'^\d+[\.\)]\s+', '', line))}")
                else:
                    result.append(self.clean_text(line))

        if result:
            result.append("")

        return result

    def convert_file(self, file_path):
        """轉換單個文件"""
        file_path = Path(file_path)
        extension = file_path.suffix.lower()

        if extension not in self.supported_extensions:
            logger.warning(f"不支援的文件類型: {extension}")
            return False

        # 生成輸出文件名
        md_path = self.output_dir / f"{file_path.stem}_extracted.md"

        # 檢查是否需要轉換
        if not self.is_file_changed(file_path):
            logger.info(f"文件未變更，跳過: {file_path.name}")
            return True

        logger.info(f"開始轉換: {file_path.name}")

        # 根據文件類型選擇轉換方法
        success = False
        if extension == '.docx':
            success = self.convert_docx_to_md(file_path, md_path)
        elif extension == '.pdf':
            success = self.convert_pdf_to_md(file_path, md_path)
        elif extension == '.pptx':
            success = self.convert_pptx_to_md(file_path, md_path)

        if success:
            # 更新快取
            file_hash = self.get_file_hash(file_path)
            self.cache[str(file_path)] = {
                'hash': file_hash,
                'converted_at': datetime.now().isoformat(),
                'output_file': str(md_path)
            }
            logger.info(f"轉換成功: {file_path.name} -> {md_path.name}")

            # 生成轉換報告
            self.generate_conversion_report(file_path, md_path)
        else:
            logger.error(f"轉換失敗: {file_path.name}")

        return success

    def generate_conversion_report(self, input_path: Path, output_path: Path):
        """生成轉換報告"""
        try:
            input_size = input_path.stat().st_size
            output_size = output_path.stat().st_size

            report = {
                'input_file': str(input_path),
                'output_file': str(output_path),
                'input_size': input_size,
                'output_size': output_size,
                'conversion_time': datetime.now().isoformat(),
                'compression_ratio': round(output_size / input_size * 100, 2) if input_size > 0 else 0
            }

            # 保存到報告文件
            report_file = self.output_dir / "conversion_report.json"
            reports = []

            if report_file.exists():
                try:
                    with open(report_file, 'r', encoding='utf-8') as f:
                        reports = json.load(f)
                except:
                    reports = []

            reports.append(report)

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(reports, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.warning(f"無法生成轉換報告: {e}")

    def convert_all(self):
        """轉換所有支援的文件"""
        converted_count = 0
        failed_count = 0

        # 尋找所有支援的文件
        for extension in self.supported_extensions:
            for file_path in self.input_dir.glob(f"*{extension}"):
                if self.convert_file(file_path):
                    converted_count += 1
                else:
                    failed_count += 1

        # 儲存快取
        self.save_cache()

        # 生成總結報告
        self.generate_summary_report(converted_count, failed_count)

        logger.info(f"轉換完成: 成功 {converted_count} 個，失敗 {failed_count} 個")
        return converted_count, failed_count

    def generate_summary_report(self, converted_count: int, failed_count: int):
        """生成總結報告"""
        try:
            summary = {
                'conversion_date': datetime.now().isoformat(),
                'total_files': converted_count + failed_count,
                'successful_conversions': converted_count,
                'failed_conversions': failed_count,
                'success_rate': round(converted_count / (converted_count + failed_count) * 100, 2) if (converted_count + failed_count) > 0 else 0,
                'output_directory': str(self.output_dir),
                'cache_file': str(self.cache_file)
            }

            summary_file = self.output_dir / "conversion_summary.md"

            with open(summary_file, 'w', encoding='utf-8') as f:
                f.write("# 文件轉換總結報告\n\n")
                f.write(f"**轉換時間**: {summary['conversion_date']}\n\n")
                f.write(f"**總文件數**: {summary['total_files']}\n\n")
                f.write(f"**成功轉換**: {summary['successful_conversions']}\n\n")
                f.write(f"**轉換失敗**: {summary['failed_conversions']}\n\n")
                f.write(f"**成功率**: {summary['success_rate']}%\n\n")
                f.write(f"**輸出目錄**: {summary['output_directory']}\n\n")

                # 添加詳細的文件列表
                f.write("## 轉換文件列表\n\n")
                for file_path, info in self.cache.items():
                    if 'converted_at' in info:
                        f.write(f"- **{Path(file_path).name}** → {Path(info['output_file']).name}\n")
                        f.write(f"  - 轉換時間: {info['converted_at']}\n")
                        f.write(f"  - 文件雜湊: {info['hash'][:8]}...\n\n")

        except Exception as e:
            logger.warning(f"無法生成總結報告: {e}")

    def cleanup_temp_files(self):
        """清理臨時文件"""
        temp_patterns = ['*.tmp', '~$*', '.~*', '*.temp']
        cleaned_count = 0

        for pattern in temp_patterns:
            for temp_file in self.input_dir.glob(pattern):
                try:
                    temp_file.unlink()
                    cleaned_count += 1
                    logger.info(f"清理臨時文件: {temp_file.name}")
                except Exception as e:
                    logger.warning(f"無法清理臨時文件 {temp_file}: {e}")

        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 個臨時文件")

        return cleaned_count


def main():
    """主函數"""
    parser = argparse.ArgumentParser(
        description='增強版文件轉換工具 - 將 DOCX、PDF、PPTX 轉換為高品質 Markdown',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用範例:
  python document_converter_enhanced.py                    # 轉換 input 目錄中的所有文件
  python document_converter_enhanced.py -i docs -o md     # 指定輸入和輸出目錄
  python document_converter_enhanced.py -f file.docx      # 轉換單個文件
  python document_converter_enhanced.py --cleanup         # 清理臨時文件

主要改進:
  - 智能表格處理（合併儲存格、格式化）
  - 進階標題處理（自動層級、樣式）
  - 頁頭頁尾自動識別與移除
  - 文字格式保留（粗體、斜體）
  - 更好的PDF文字提取
  - 智能內容過濾
        """
    )

    parser.add_argument('-i', '--input', default='input', help='輸入目錄 (預設: input)')
    parser.add_argument('-o', '--output', default='output', help='輸出目錄 (預設: output)')
    parser.add_argument('-f', '--file', help='轉換單個文件')
    parser.add_argument('--cleanup', action='store_true', help='清理臨時文件')
    parser.add_argument('--force', action='store_true', help='強制重新轉換所有文件')
    parser.add_argument('--verbose', '-v', action='store_true', help='詳細輸出')

    args = parser.parse_args()

    # 設定日誌級別
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 創建轉換器
    converter = EnhancedDocumentConverter(args.input, args.output)

    # 清理臨時文件
    if args.cleanup:
        converter.cleanup_temp_files()

    # 強制重新轉換
    if args.force:
        converter.cache = {}

    try:
        if args.file:
            # 轉換單個文件
            file_path = Path(args.file)
            if not file_path.exists():
                logger.error(f"文件不存在: {args.file}")
                sys.exit(1)

            success = converter.convert_file(file_path)
            converter.save_cache()

            if success:
                logger.info("文件轉換成功！")
                print(f"\n✅ 轉換完成！")
                print(f"📁 輸出文件: {converter.output_dir / f'{file_path.stem}_extracted.md'}")
            else:
                logger.error("文件轉換失敗！")
                sys.exit(1)
        else:
            # 轉換所有文件
            print("🚀 開始批量轉換...")
            converted, failed = converter.convert_all()

            print(f"\n📊 轉換結果:")
            print(f"✅ 成功: {converted} 個文件")
            print(f"❌ 失敗: {failed} 個文件")
            print(f"📁 輸出目錄: {converter.output_dir}")
            print(f"📋 詳細報告: {converter.output_dir / 'conversion_summary.md'}")

            if failed > 0:
                logger.warning(f"有 {failed} 個文件轉換失敗")
                sys.exit(1)
            else:
                logger.info("所有文件轉換成功！")

    except KeyboardInterrupt:
        logger.info("轉換被使用者中斷")
        sys.exit(1)
    except Exception as e:
        logger.error(f"轉換過程發生錯誤: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

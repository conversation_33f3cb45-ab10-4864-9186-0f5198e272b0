@echo off
chcp 65001 >nul
echo ========================================
echo 文件轉換工具 - DOCX/PDF/PPTX to Markdown
echo ========================================
echo.

REM 檢查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [錯誤] 找不到Python，請確認已安裝Python 3.6+
    pause
    exit /b 1
)

REM 檢查是否需要安裝依賴套件
echo [資訊] 檢查依賴套件...
python install_dependencies.py

if %errorlevel% neq 0 (
    echo [警告] 依賴套件安裝可能有問題
    echo.
)

REM 創建必要的目錄
if not exist "input" (
    mkdir input
    echo [資訊] 已創建 input 目錄
)

if not exist "output" (
    mkdir output
    echo [資訊] 已創建 output 目錄
)

echo.
echo [資訊] 目錄結構:
echo - input/  : 放置要轉換的文件 (DOCX, PDF, PPTX)
echo - output/ : 轉換後的 Markdown 文件
echo.

REM 檢查input目錄是否有文件
set file_count=0
for %%f in (input\*.docx input\*.pdf input\*.pptx) do (
    set /a file_count+=1
)

if %file_count% equ 0 (
    echo [提示] input 目錄中沒有找到支援的文件
    echo 請將 DOCX、PDF 或 PPTX 文件放入 input 目錄中
    echo.
    echo 按任意鍵打開 input 目錄...
    pause >nul
    start input
    exit /b 0
)

echo [資訊] 找到 %file_count% 個文件待轉換
echo.

REM 詢問轉換選項
echo 轉換選項:
echo 1. 轉換所有文件 (增量模式)
echo 2. 強制重新轉換所有文件
echo 3. 清理臨時文件後轉換
echo 4. 只清理臨時文件
echo.
set /p choice="請選擇 (1-4): "

echo.
echo [資訊] 開始處理...

if "%choice%"=="1" (
    echo [模式] 增量轉換
    python document_converter.py
) else if "%choice%"=="2" (
    echo [模式] 強制重新轉換
    python document_converter.py --force
) else if "%choice%"=="3" (
    echo [模式] 清理後轉換
    python document_converter.py --cleanup --force
) else if "%choice%"=="4" (
    echo [模式] 只清理臨時文件
    python document_converter.py --cleanup
) else (
    echo [預設] 增量轉換
    python document_converter.py
)

if %errorlevel% equ 0 (
    echo.
    echo [成功] 轉換完成！
    echo.
    echo 轉換結果保存在 output 目錄中
    echo.
    set /p open_output="是否要打開 output 目錄查看結果？(y/n): "
    if /i "%open_output%"=="y" (
        start output
    )
) else (
    echo.
    echo [錯誤] 轉換過程中發生錯誤
    echo 請檢查 converter.log 文件查看詳細錯誤信息
)

echo.
echo 按任意鍵退出...
pause >nul

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
政策文件轉換自動化部署腳本 V2.0
增量處理和清理功能升級版

新功能:
1. 增量處理 - 只處理新檔案
2. 檔案追蹤 - 記錄已處理檔案
3. 自動清理 - 清除無用檔案，保留有用的 README
4. 智能歸檔 - 新檔案處理完成後自動歸檔

流程:
1. 檢查 ref Policy 中的新檔案
2. 只轉換新檔案
3. 清理無用檔案
4. 更新處理記錄

作者: AI Assistant
版本: 2.0
"""

import os
import sys
import shutil
import subprocess
import logging
from pathlib import Path
from typing import List, Dict, Optional, Set
import argparse
from datetime import datetime
import json
import hashlib

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('policy_conversion_v2.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class PolicyConversionManagerV2:
    """政策文件轉換管理器 V2.0"""
    
    def __init__(self, base_dir="."):
        """初始化管理器"""
        self.base_dir = Path(base_dir)
        self.ref_policy_dir = self.base_dir / "ref Policy"
        self.md_policy_dir = self.base_dir / "md Policy"
        self.temp_extracted_dir = self.base_dir / "extracted"
        
        # 處理記錄檔案
        self.tracking_file = self.base_dir / "processed_files.json"
        
        # 支援的文件格式
        self.supported_formats = ['.pdf', '.docx', '.pptx']
        
        # 保留的有用檔案
        self.keep_files = {
            'README_DocumentExtractor.md',
            'README_PolicyConversion.md',
            'TW_AI_Policy.md',
            'reference.md',
            'conversion_summary.md',
            'policy_conversion.log',
            'policy_conversion_v2.log',
            'processed_files.json'
        }
        
        logger.info("政策文件轉換管理器 V2.0 初始化完成")
        logger.info(f"基礎目錄: {self.base_dir.absolute()}")
        logger.info(f"參考政策目錄: {self.ref_policy_dir}")
        logger.info(f"Markdown 政策目錄: {self.md_policy_dir}")
    
    def load_processed_files(self) -> Dict[str, str]:
        """載入已處理檔案記錄"""
        if self.tracking_file.exists():
            try:
                with open(self.tracking_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"載入處理記錄失敗: {e}")
        return {}
    
    def save_processed_files(self, processed_files: Dict[str, str]):
        """儲存已處理檔案記錄"""
        try:
            with open(self.tracking_file, 'w', encoding='utf-8') as f:
                json.dump(processed_files, f, ensure_ascii=False, indent=2)
            logger.info(f"處理記錄已更新: {len(processed_files)} 個檔案")
        except Exception as e:
            logger.error(f"儲存處理記錄失敗: {e}")
    
    def get_file_hash(self, file_path: Path) -> str:
        """計算檔案雜湊值"""
        try:
            with open(file_path, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception as e:
            logger.warning(f"計算檔案雜湊值失敗 {file_path}: {e}")
            return ""
    
    def find_new_files(self) -> List[Path]:
        """尋找 ref Policy 中的新檔案"""
        logger.info("正在掃描新檔案...")
        
        if not self.ref_policy_dir.exists():
            logger.info("ref Policy 目錄不存在，跳過新檔案掃描")
            return []
        
        processed_files = self.load_processed_files()
        new_files = []
        
        for file_path in self.ref_policy_dir.rglob("*"):
            if file_path.is_file() and file_path.suffix.lower() in self.supported_formats:
                file_key = str(file_path.relative_to(self.ref_policy_dir))
                current_hash = self.get_file_hash(file_path)
                
                # 檢查是否為新檔案或已修改檔案
                if file_key not in processed_files or processed_files[file_key] != current_hash:
                    new_files.append(file_path)
                    logger.info(f"發現新檔案: {file_path.name}")
        
        logger.info(f"找到 {len(new_files)} 個新檔案需要處理")
        return new_files
    
    def find_files_to_process(self) -> List[Path]:
        """尋找當前目錄中需要移動到 ref Policy 的檔案"""
        logger.info("正在掃描當前目錄中的政策文件...")
        
        policy_files = []
        processed_files = self.load_processed_files()
        
        for format_ext in self.supported_formats:
            pattern = f"*{format_ext}"
            files = list(self.base_dir.glob(pattern))
            
            for file_path in files:
                # 跳過已在 ref Policy 目錄中的文件
                if str(file_path).startswith(str(self.ref_policy_dir)):
                    continue
                
                file_key = file_path.name
                current_hash = self.get_file_hash(file_path)
                
                # 檢查是否為新檔案
                if file_key not in processed_files or processed_files[file_key] != current_hash:
                    policy_files.append(file_path)
        
        logger.info(f"找到 {len(policy_files)} 個新檔案需要移動到 ref Policy")
        return policy_files
    
    def create_directory_structure(self):
        """創建目錄結構"""
        logger.info("正在檢查目錄結構...")
        
        try:
            # 創建 ref Policy 目錄
            self.ref_policy_dir.mkdir(exist_ok=True)
            logger.info(f"✓ 確認目錄: {self.ref_policy_dir}")
            
            # 創建 md Policy 目錄
            self.md_policy_dir.mkdir(exist_ok=True)
            logger.info(f"✓ 確認目錄: {self.md_policy_dir}")
            
            # 創建臨時提取目錄
            self.temp_extracted_dir.mkdir(exist_ok=True)
            logger.info(f"✓ 確認臨時目錄: {self.temp_extracted_dir}")
            
            return True
            
        except Exception as e:
            logger.error(f"創建目錄結構失敗: {e}")
            return False
    
    def check_dependencies(self):
        """檢查依賴套件"""
        logger.info("檢查依賴套件...")
        
        packages = ['PyPDF2', 'pdfplumber', 'python-docx', 'python-pptx']
        missing_packages = []
        
        for package in packages:
            try:
                __import__(package.replace('-', '_').lower())
                logger.info(f"✓ {package} 已安裝")
            except ImportError:
                missing_packages.append(package)
                logger.warning(f"✗ {package} 未安裝")
        
        if missing_packages:
            logger.info(f"需要安裝 {len(missing_packages)} 個套件")
            return self.install_missing_packages(missing_packages)
        else:
            logger.info("所有依賴套件已安裝")
            return True
    
    def install_missing_packages(self, packages: List[str]):
        """安裝缺失的套件"""
        logger.info("正在安裝缺失的依賴套件...")
        
        success_count = 0
        for package in packages:
            try:
                logger.info(f"安裝 {package}...")
                result = subprocess.run([
                    sys.executable, '-m', 'pip', 'install', package
                ], capture_output=True, text=True)
                
                if result.returncode == 0:
                    logger.info(f"✓ {package} 安裝成功")
                    success_count += 1
                else:
                    logger.warning(f"✗ {package} 安裝失敗: {result.stderr}")
                    
            except Exception as e:
                logger.warning(f"✗ {package} 安裝錯誤: {e}")
        
        logger.info(f"依賴套件安裝完成: {success_count}/{len(packages)} 成功")
        return success_count > 0
    
    def move_files_to_ref_policy(self, files: List[Path]) -> List[Path]:
        """移動文件到 ref Policy 目錄"""
        if not files:
            logger.info("沒有檔案需要移動")
            return []
        
        logger.info(f"正在移動 {len(files)} 個檔案到 ref Policy 目錄...")
        
        moved_files = []
        
        for file in files:
            try:
                dest_path = self.ref_policy_dir / file.name
                
                # 如果目標文件已存在，添加時間戳
                if dest_path.exists():
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    stem = dest_path.stem
                    suffix = dest_path.suffix
                    dest_path = self.ref_policy_dir / f"{stem}_{timestamp}{suffix}"
                
                shutil.move(str(file), str(dest_path))
                logger.info(f"✓ 移動: {file.name} -> {dest_path.name}")
                moved_files.append(dest_path)
                
            except Exception as e:
                logger.error(f"✗ 移動文件失敗 {file.name}: {e}")
        
        logger.info(f"文件移動完成: {len(moved_files)}/{len(files)} 成功")
        return moved_files
    
    def run_document_extractor(self, input_files: List[Path] = None):
        """執行文件提取器"""
        logger.info("正在執行文件提取...")
        
        try:
            # 檢查 document_extractor.py 是否存在
            extractor_path = self.base_dir / "document_extractor.py"
            if not extractor_path.exists():
                logger.error("document_extractor.py 不存在，請確認文件已正確部署")
                return False
            
            # 如果指定了特定檔案，只處理這些檔案
            if input_files:
                # 創建臨時目錄存放要處理的檔案
                temp_input_dir = self.base_dir / "temp_input"
                temp_input_dir.mkdir(exist_ok=True)
                
                try:
                    # 複製指定檔案到臨時目錄
                    for file_path in input_files:
                        temp_file = temp_input_dir / file_path.name
                        shutil.copy2(str(file_path), str(temp_file))
                    
                    # 執行提取器
                    cmd = [
                        sys.executable, 
                        str(extractor_path),
                        '-i', str(temp_input_dir),
                        '-o', str(self.temp_extracted_dir)
                    ]
                    
                finally:
                    # 清理臨時目錄
                    if temp_input_dir.exists():
                        shutil.rmtree(temp_input_dir)
            else:
                # 處理整個 ref Policy 目錄
                cmd = [
                    sys.executable, 
                    str(extractor_path),
                    '-i', str(self.ref_policy_dir),
                    '-o', str(self.temp_extracted_dir)
                ]
            
            logger.info(f"執行命令: {' '.join(cmd)}")
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                logger.info("✓ 文件提取完成")
                return True
            else:
                logger.error("✗ 文件提取失敗")
                logger.error(f"錯誤輸出: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"執行文件提取器時發生錯誤: {e}")
            return False

    def move_extracted_to_md_policy(self):
        """移動提取的文件到 md Policy 目錄"""
        logger.info("正在移動提取的文件到 md Policy 目錄...")

        try:
            # 尋找所有 .md 文件
            md_files = list(self.temp_extracted_dir.glob("*.md"))

            moved_count = 0
            for md_file in md_files:
                try:
                    dest_path = self.md_policy_dir / md_file.name

                    # 如果目標文件已存在，覆蓋（因為是更新版本）
                    if dest_path.exists():
                        dest_path.unlink()

                    shutil.move(str(md_file), str(dest_path))
                    logger.info(f"✓ 移動: {md_file.name}")
                    moved_count += 1

                except Exception as e:
                    logger.error(f"✗ 移動文件失敗 {md_file.name}: {e}")

            logger.info(f"Markdown 文件移動完成: {moved_count} 個文件")

            # 清理臨時目錄
            if self.temp_extracted_dir.exists():
                shutil.rmtree(self.temp_extracted_dir)
                logger.info("✓ 清理臨時目錄")

            return moved_count > 0

        except Exception as e:
            logger.error(f"移動提取文件時發生錯誤: {e}")
            return False

    def update_processed_files_record(self, processed_files: List[Path]):
        """更新已處理檔案記錄"""
        logger.info("正在更新處理記錄...")

        current_records = self.load_processed_files()

        for file_path in processed_files:
            if file_path.exists():
                # 使用相對於 ref Policy 的路徑作為 key
                if str(file_path).startswith(str(self.ref_policy_dir)):
                    file_key = str(file_path.relative_to(self.ref_policy_dir))
                else:
                    file_key = file_path.name

                file_hash = self.get_file_hash(file_path)
                current_records[file_key] = file_hash
                logger.info(f"記錄檔案: {file_key}")

        self.save_processed_files(current_records)

    def clean_unnecessary_files(self):
        """清理無用檔案，保留有用的 README"""
        logger.info("正在清理無用檔案...")

        cleaned_count = 0

        # 清理根目錄中的無用檔案
        for file_path in self.base_dir.iterdir():
            if file_path.is_file():
                # 跳過保留的檔案
                if file_path.name in self.keep_files:
                    continue

                # 跳過隱藏檔案和系統檔案
                if file_path.name.startswith('.'):
                    continue

                # 清理舊的安裝腳本和執行腳本
                if file_path.name in [
                    'install_dependencies.bat',
                    'install_dependencies.ps1',
                    'run_policy_conversion.bat',
                    'run_policy_conversion.ps1',
                    'setup_policy_conversion.py',  # 舊版本
                    'document_extractor.log'
                ]:
                    try:
                        file_path.unlink()
                        logger.info(f"✓ 清理: {file_path.name}")
                        cleaned_count += 1
                    except Exception as e:
                        logger.warning(f"清理檔案失敗 {file_path.name}: {e}")

        # 清理 AI_Policy_v1 目錄（如果存在）
        old_policy_dir = self.base_dir / "AI_Policy_v1"
        if old_policy_dir.exists():
            try:
                shutil.rmtree(old_policy_dir)
                logger.info("✓ 清理舊版本政策目錄: AI_Policy_v1")
                cleaned_count += 1
            except Exception as e:
                logger.warning(f"清理舊版本目錄失敗: {e}")

        logger.info(f"清理完成: 移除了 {cleaned_count} 個無用檔案/目錄")
        return cleaned_count

    def generate_summary_report(self, processed_files: List[Path]):
        """生成摘要報告"""
        logger.info("正在生成摘要報告...")

        try:
            # 統計文件數量
            ref_files = list(self.ref_policy_dir.glob("*")) if self.ref_policy_dir.exists() else []
            md_files = list(self.md_policy_dir.glob("*.md")) if self.md_policy_dir.exists() else []

            # 生成報告內容
            report_content = f"""# 政策文件轉換摘要報告 V2.0

**轉換時間**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
**處理模式**: 增量處理 (只處理新檔案)
**本次處理**: {len(processed_files)} 個新檔案

## 📁 目錄結構

```
{self.base_dir.name}/
├── ref Policy/          # 原始政策文件 ({len([f for f in ref_files if f.is_file()])} 個文件)
├── md Policy/           # 轉換後的 Markdown 文件 ({len(md_files)} 個文件)
├── document_extractor.py
├── setup_policy_conversion_v2.py
└── processed_files.json # 處理記錄
```

## 🆕 本次處理的新檔案

"""

            if processed_files:
                for file in processed_files:
                    if file.exists():
                        size_mb = file.stat().st_size / (1024 * 1024)
                        report_content += f"- **{file.name}** ({size_mb:.2f} MB)\n"
            else:
                report_content += "- 無新檔案需要處理\n"

            report_content += f"""

## 📄 所有原始政策文件 (ref Policy)

"""

            for file in ref_files:
                if file.is_file():
                    size_mb = file.stat().st_size / (1024 * 1024)
                    report_content += f"- **{file.name}** ({size_mb:.2f} MB)\n"

            report_content += f"""

## 📝 轉換後的 Markdown 文件 (md Policy)

"""

            for file in md_files:
                if file.is_file():
                    size_kb = file.stat().st_size / 1024
                    report_content += f"- **{file.name}** ({size_kb:.1f} KB)\n"

            report_content += f"""

## 📊 轉換統計

- **總檔案數量**: {len([f for f in ref_files if f.is_file()])} 個
- **本次處理數量**: {len(processed_files)} 個
- **Markdown 檔案數量**: {len(md_files)} 個
- **處理成功率**: {(len(processed_files)/max(len(processed_files), 1)*100):.1f}%

## 🔄 增量處理優勢

- ⚡ **快速處理**: 只轉換新檔案，大幅節省時間
- 📝 **智能追蹤**: 自動記錄已處理檔案，避免重複處理
- 🧹 **自動清理**: 移除無用檔案，保持目錄整潔
- 📁 **自動歸檔**: 新檔案處理完成後自動歸檔到 ref Policy

## 🎯 下一步建議

1. 檢查 `md Policy` 目錄中的轉換結果
2. 使用轉換後的 Markdown 文件更新 AI 政策
3. 新增檔案時，只需放入當前目錄或 `ref Policy`，系統會自動處理

---
*此報告由政策文件轉換管理器 V2.0 自動生成*
"""

            # 儲存報告
            report_path = self.base_dir / "conversion_summary.md"
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)

            logger.info(f"✓ 摘要報告已儲存: {report_path}")
            return True

        except Exception as e:
            logger.error(f"生成摘要報告失敗: {e}")
            return False

    def run_incremental_conversion(self):
        """執行增量轉換流程"""
        logger.info("="*60)
        logger.info("開始政策文件增量轉換流程 V2.0")
        logger.info("="*60)

        # 檢查目錄結構
        if not self.create_directory_structure():
            return False

        # 檢查依賴套件
        if not self.check_dependencies():
            logger.warning("部分依賴套件未安裝，可能影響轉換效果")

        # 尋找當前目錄中的新檔案並移動到 ref Policy
        current_dir_files = self.find_files_to_process()
        moved_files = []
        if current_dir_files:
            logger.info(f"\n🔄 步驟: 移動新檔案到 ref Policy")
            moved_files = self.move_files_to_ref_policy(current_dir_files)
            logger.info(f"✅ 移動完成: {len(moved_files)} 個檔案")

        # 尋找 ref Policy 中的新檔案
        logger.info(f"\n🔄 步驟: 掃描 ref Policy 中的新檔案")
        new_files = self.find_new_files()

        # 合併需要處理的檔案
        all_files_to_process = moved_files + new_files

        if not all_files_to_process:
            logger.info("✅ 沒有新檔案需要處理")
            logger.info("🎉 增量轉換流程完成！")
            return True

        # 執行文件轉換
        logger.info(f"\n🔄 步驟: 轉換 {len(all_files_to_process)} 個新檔案")
        if not self.run_document_extractor(all_files_to_process):
            logger.error("❌ 文件轉換失敗")
            return False
        logger.info(f"✅ 文件轉換完成")

        # 移動轉換結果
        logger.info(f"\n🔄 步驟: 移動轉換結果到 md Policy")
        if not self.move_extracted_to_md_policy():
            logger.error("❌ 移動轉換結果失敗")
            return False
        logger.info(f"✅ 轉換結果移動完成")

        # 更新處理記錄
        logger.info(f"\n🔄 步驟: 更新處理記錄")
        self.update_processed_files_record(all_files_to_process)
        logger.info(f"✅ 處理記錄更新完成")

        # 清理無用檔案
        logger.info(f"\n🔄 步驟: 清理無用檔案")
        cleaned_count = self.clean_unnecessary_files()
        logger.info(f"✅ 清理完成: 移除 {cleaned_count} 個無用檔案")

        # 生成摘要報告
        logger.info(f"\n🔄 步驟: 生成摘要報告")
        if not self.generate_summary_report(all_files_to_process):
            logger.warning("⚠️ 生成摘要報告失敗，但不影響主流程")
        else:
            logger.info(f"✅ 摘要報告生成完成")

        logger.info("\n" + "="*60)
        logger.info("🎉 增量轉換流程完成！")
        logger.info("="*60)
        logger.info(f"📁 原始文件位置: {self.ref_policy_dir}")
        logger.info(f"📝 轉換結果位置: {self.md_policy_dir}")
        logger.info(f"📊 摘要報告: conversion_summary.md")
        logger.info(f"📋 詳細日誌: policy_conversion_v2.log")
        logger.info(f"🔄 處理記錄: processed_files.json")

        return True

def main():
    """主函數"""
    parser = argparse.ArgumentParser(description='政策文件轉換自動化工具 V2.0')
    parser.add_argument('--clean-only', action='store_true', help='僅執行清理功能')
    parser.add_argument('--force-all', action='store_true', help='強制處理所有檔案（忽略處理記錄）')

    args = parser.parse_args()

    print("🚀 政策文件轉換自動化工具 V2.0")
    print("="*50)

    try:
        manager = PolicyConversionManagerV2()

        if args.clean_only:
            print("🧹 執行清理模式...")
            cleaned_count = manager.clean_unnecessary_files()
            print(f"✅ 清理完成: 移除了 {cleaned_count} 個無用檔案")
            return

        if args.force_all:
            print("⚠️ 強制處理模式: 將重新處理所有檔案")
            # 清空處理記錄
            manager.save_processed_files({})

        success = manager.run_incremental_conversion()

        if success:
            print("\n✅ 增量轉換流程執行成功！")
            print("\n📋 V2.0 新功能:")
            print("- ⚡ 增量處理: 只轉換新檔案")
            print("- 📝 智能追蹤: 自動記錄已處理檔案")
            print("- 🧹 自動清理: 移除無用檔案")
            print("- 📁 自動歸檔: 新檔案自動歸檔")
            print("\n📋 使用方法:")
            print("1. 將新檔案放入當前目錄或 'ref Policy' 目錄")
            print("2. 執行此程式，系統會自動處理新檔案")
            print("3. 查看 'conversion_summary.md' 了解處理結果")
        else:
            print("\n❌ 轉換流程執行失敗，請查看日誌文件")

    except KeyboardInterrupt:
        print("\n⏹️ 使用者中斷執行")
    except Exception as e:
        print(f"\n💥 執行過程中發生未預期的錯誤: {e}")
        logger.error(f"主程式錯誤: {e}")

if __name__ == "__main__":
    main()

# 增強版文件轉換工具 - DOCX/PDF/PPTX to Markdown

這是一個大幅改進的文件轉換工具，可以將 Microsoft Office 文件（DOCX、PDF、PPTX）轉換為高品質的 Markdown 格式。

## 🌟 主要改進

### 📊 智能表格處理
- **合併儲存格支援**：正確處理複雜表格結構
- **表格格式化**：自動清理和格式化表格內容
- **標題行識別**：自動添加Markdown表格分隔線
- **內容清理**：移除表格中的特殊字符和格式

### 🎯 進階標題處理
- **自動層級識別**：根據樣式和字體大小判斷標題層級
- **編號處理**：支援數字編號和中文編號
- **樣式保留**：保持原文檔的標題結構
- **智能判斷**：區分標題和一般段落

### 🧹 頁頭頁尾處理
- **自動識別**：智能識別頁頭頁尾內容
- **模式匹配**：支援多種頁碼和版權信息格式
- **內容過濾**：自動移除無關的頁面信息
- **保留重要內容**：避免誤刪重要信息

### 💎 文字格式保留
- **粗體文字**：保留 `**粗體**` 格式
- **斜體文字**：保留 `*斜體*` 格式
- **底線文字**：轉換為粗體顯示
- **超連結**：保留連結結構（計劃功能）

### 📈 更好的PDF處理
- **雙引擎支援**：優先使用pdfplumber，回退到PyPDF2
- **表格提取**：從PDF中提取表格結構
- **文字清理**：智能段落分割和內容清理
- **標題識別**：自動識別PDF中的標題

### 🔍 智能內容過濾
- **重複內容移除**：避免重複的頁頭頁尾
- **空白處理**：智能處理空行和空白字符
- **特殊字符清理**：移除控制字符和無效內容
- **段落優化**：改善段落結構和可讀性

## 📦 安裝要求

### Python 環境
- Python 3.6 或更高版本

### 依賴套件
```bash
pip install python-docx PyPDF2 pdfplumber python-pptx Pillow
```

或使用自動安裝腳本：
```bash
python install_dependencies_enhanced.py
```

## 🚀 使用方法

### 快速開始
```bash
# 使用批次處理腳本（推薦）
convert_documents_enhanced.bat

# 直接使用Python
python document_converter_enhanced.py
```

### 進階選項
```bash
# 轉換所有文件
python document_converter_enhanced.py

# 指定目錄
python document_converter_enhanced.py -i docs -o markdown

# 轉換單個文件
python document_converter_enhanced.py -f document.docx

# 強制重新轉換
python document_converter_enhanced.py --force

# 詳細模式
python document_converter_enhanced.py --verbose

# 清理臨時文件
python document_converter_enhanced.py --cleanup
```

## 📁 輸出文件

### 文件命名
- 原文件：`document.docx`
- 轉換結果：`document_extracted.md`

### 報告文件
- **轉換總結**：`output/conversion_summary.md`
- **詳細記錄**：`output/conversion_report.json`
- **日誌文件**：`converter_enhanced.log`

## 🔧 轉換效果對比

### 原版 vs 增強版

| 功能 | 原版 | 增強版 |
|------|------|--------|
| 表格處理 | 基本 | ✅ 智能格式化 |
| 標題識別 | 簡單 | ✅ 多層級自動識別 |
| 頁頭頁尾 | 無處理 | ✅ 自動移除 |
| 文字格式 | 純文字 | ✅ 保留粗體斜體 |
| PDF處理 | PyPDF2 | ✅ 雙引擎支援 |
| 內容過濾 | 無 | ✅ 智能清理 |
| 轉換報告 | 無 | ✅ 詳細報告 |

### DOCX 轉換改進
- ✅ 保留文檔結構順序
- ✅ 智能標題層級判斷
- ✅ 表格格式優化
- ✅ 文字格式保留
- ✅ 列表結構處理

### PDF 轉換改進
- ✅ 使用pdfplumber提升文字提取品質
- ✅ 表格結構識別
- ✅ 智能段落分割
- ✅ 標題自動識別
- ✅ 頁頭頁尾過濾

### PPTX 轉換改進
- ✅ 投影片標題識別
- ✅ 項目符號處理
- ✅ 內容結構優化
- ✅ 文字清理改進

## 📊 效能優化

### 處理速度
- **增量處理**：只轉換變更的文件
- **快取機制**：避免重複處理
- **批量處理**：高效處理多個文件

### 記憶體管理
- **逐個處理**：避免記憶體溢出
- **資源清理**：及時釋放資源
- **錯誤恢復**：單個文件失敗不影響其他文件

## 🐛 故障排除

### 常見問題

**Q: 轉換後的表格格式不正確**
A: 增強版已大幅改善表格處理，支援複雜表格結構

**Q: PDF轉換效果不佳**
A: 增強版使用pdfplumber引擎，大幅提升PDF處理品質

**Q: 標題層級不正確**
A: 增強版支援智能標題識別，根據樣式和字體大小判斷

**Q: 輸出包含頁頭頁尾**
A: 增強版自動識別並移除頁頭頁尾內容

### 日誌檢查
```bash
# 查看詳細日誌
Get-Content converter_enhanced.log -Tail 20

# 查看轉換報告
Get-Content output/conversion_summary.md
```

## 🔄 版本比較

### 選擇建議
- **新用戶**：直接使用增強版
- **現有用戶**：建議升級到增強版
- **簡單需求**：可繼續使用原版
- **高品質需求**：強烈推薦增強版

### 遷移指南
1. 備份現有轉換結果
2. 安裝增強版依賴套件
3. 使用增強版重新轉換
4. 比較轉換品質
5. 切換到增強版

## 🎯 最佳實務

1. **定期清理**：使用 `--cleanup` 清理臨時文件
2. **檢查結果**：查看轉換報告確認品質
3. **批量處理**：一次處理多個相關文件
4. **備份重要文件**：轉換前備份原始文件
5. **使用詳細模式**：遇到問題時使用 `--verbose`

增強版文件轉換工具為您提供更高品質的轉換結果！🎉

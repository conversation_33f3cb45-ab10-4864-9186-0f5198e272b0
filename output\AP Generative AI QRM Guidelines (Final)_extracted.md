# AP Generative AI QRM Guidelines (Final)

## 第 1 頁

### AP Generative AI QRM Guidelines May 2024 (Version 1.0)

## 第 2 頁

### 表格 1

| Contents 1. Objectives 2. Understanding Generative AI and Trustworthy AI 3. Generative AI Risks and Limitations 4. Use Case Evaluation and Approval Process 5. Generative AI Data Guardrails- By Data Type and Persona 6. Generative AI Contracting Considerations 7. Generative AI IP Considerations 8. Generative AI Independence Considerations 9. Deloitte’s Trustworthy AI Framework Guidance 10. Contacts and References © 2024 Deloitte Asia Pacific Services Limited | AP Generative AI QRM Guidelines 2 |
| --- | --- |

Contents 1. Objectives 2. Understanding Generative AI and Trustworthy AI 3. Generative AI Risks and Limitations 4. Use Case Evaluation and Approval Process 5. Generative AI Data Guardrails- By Data Type and Persona 6. Generative AI Contracting Considerations 7. Generative AI IP Considerations 8. Generative AI Independence Considerations 9. Deloitte’s Trustworthy AI Framework Guidance 10. Contacts and References

## 第 3 頁

### 表格 1

|  | To help prepare for a bold and successful future with Generative AI, to capture the rapid growth of Generative AI market capabilities, and to transform services to be augmented by Generative AI. It is important to understand the nature and scale of the risks, as well as the governance strategies that can help mitigate them. The objectives of this AP Generative AI QRM guidelines are to: • Provide a high-level introduction of Generative AI. • Introduce Deloitte’s Trustworthy AI Framework that underpins the QRM assessment approach for Generative AI. • Explain the risks and limitations of Generative AI. • Establish the risk triage process to assess risk level of Generative AI use cases at the start and apply appropriate risk management strategy and process to enable speed to the market. • Set out the QRM considerations and provide guardrails for key risk areas, including data security, contracting, IP, independence and Trustworthy AI. • Provide contacts and tangible tools to facilitate the QRM assessment during the Generative AI development. Please note that these guidelines may be refined and updated as it is used in practice. | None | None |
| --- | --- | --- | --- |
| None | ©© 22002244 DDeellooiittttee AAssiiaa PPaacciiffiicc SSeerrvviicceess LLiimmiitteedd |  |  |

### 表格 2

| To help prepare for a bold and successful future with Generative AI, to capture the rapid growth of Generative AI market capabilities, and to transform services to be augmented by Generative AI. It is important to understand the nature and scale of the risks, as well as the governance strategies that can help mitigate them. | None |
| --- | --- |
| The objectives of this AP Generative AI QRM guidelines are to: • Provide a high-level introduction of Generative AI. • Introduce Deloitte’s Trustworthy AI Framework that underpins the QRM assessment approach for Generative AI. • Explain the risks and limitations of Generative AI. • Establish the risk triage process to assess risk level of Generative AI use cases at the start and apply appropriate risk management strategy and process to enable speed to the market. • Set out the QRM considerations and provide guardrails for key risk areas, including data security, contracting, IP, independence and Trustworthy AI. • Provide contacts and tangible tools to facilitate the QRM assessment during the Generative AI development. Please note that these guidelines may be refined and updated as it is used in practice. | None |
| None | The objectives of this AP Generative AI QRM guidelines are to: • Provide a high-level introduction of Generative AI. • Introduce Deloitte’s Trustworthy AI Framework that underpins the QRM assessment approach for Generative AI. • Explain the risks and limitations of Generative AI. • Establish the risk triage process to assess risk level of Generative AI use cases at the start and apply appropriate risk management strategy and process to enable speed to the market. • Set out the QRM considerations and provide guardrails for key risk areas, including data security, contracting, IP, independence and Trustworthy AI. • Provide contacts and tangible tools to facilitate the QRM assessment during the Generative AI development. Please note that these guidelines may be refined and updated as it is used in practice. |

To help prepare for a bold and successful future with Generative AI, to capture the rapid growth of Generative AI market capabilities, and to transform services to be augmented by Generative AI. It is important to understand the nature and scale of the risks, as well as the governance strategies that can help mitigate them. The objectives of this AP Generative AI QRM guidelines are to: • Provide a high-level introduction of Generative AI. • Introduce Deloitte’s Trustworthy AI Framework that underpins the QRM assessment approach for Generative AI. • Explain the risks and limitations of Generative AI. • Establish the risk triage process to assess risk level of Generative AI use cases at the start and apply appropriate risk management strategy and process to enable speed to the market. • Set out the QRM considerations and provide guardrails for key risk areas, including data security, contracting, IP, independence and Trustworthy AI. • Provide contacts and tangible tools to facilitate the QRM assessment during the Generative AI development. Please note that these guidelines may be refined and updated as it is used in practice.

## 第 4 頁

### Understanding Generative AI

## 第 5 頁

### 表格 1

|  |
| --- |
| What is Generative AI? The ability of machines to create outputs across various modalities How does it work? Uses Foundation Models trained on massive amounts of data to understand human communication and natural language Why now? Innovations in technology allows for the realization of an autonomous creation economy Who is involved? Technology leaders and start-ups are developing user applications on these underlying models |
|  |

Understanding Generative AI EXAMPLE MODALITIES Text Generation Code Generation What is Generative AI? The ability of machines to create outputs across various modalities How does it work? Uses Foundation Models trained on massive amounts of data to understand human communication and natural language Image Generation Why now? Innovations in technology allows for the realization of an autonomous creation economy Who is involved? Video Generation Audio Generation Technology leaders and start-ups are developing user applications on these underlying models Note: To learn more about Generative AI technology, refer to Generative AI Fluency for learning pathways and resources.

## 第 6 頁

Broad Categories of Value Capture from Generative AI There are six (6) categories of usage that are emerging for our clients: ACCELERATING INNOVATION COST REDUCTION (PRODUCTS/SERVICES) Reduce cost, typically by 30% or greater primarily Increase the pace of new product or new service through automating job functions and then undertaking development and speedier go-to-market job substitutions . Drug Discovery Call Center Operations (Life Sciences) (Cross-Industry) NEW DISCOVERY & INSIGHTS PROCESS EFFICIENCY Uncover new ideas, insights, questions and generally unleash creativity Create process efficiencies through automating standard tasks and reducing manual interventions AI Augmented Financial Advisor Claims Processing (Financial Services) (Insurance) GROWTH GOVERNMENT CITIZEN SERVICES Increase revenue generation through hyper-personalized Increase accuracy of various government and local marketing for target customers programs and create easier access for at-risk populations Content Generation Welfare Distribution for Citizens (Marketing/Advertisement) (Government) Refer to Deloitte Generative AI Dossier for details Generative AI Use Case

## 第 7 頁

Trusted AI and Trustworthy AI capability is a market differentiator for Deloitte Deloitte has the Multi-Disciplinary Model to lead with the clients through their entire AI transformation journey, with AI Governance playing a key role in differentiating its Generative AI solutions by advising clients based in this QRM Guidelines for Generative AI. AI Governance is key for industries such as FSI and GPS, where Generative AI is adopted, where there is emphasis on AI regulations and proper management of client data in AI applications. Delivering the AI Premium and Lead In the AI Ecosystem AI-Led Transformation AI Governance AI Strategy Clients who wants to adopt / enable Generative Clients are emphasizing to de-risk and adopt Clients with the aspirations and roadmap to AI safe practices in AI initiatives: develop Generative AI capability: • Implement transformation program with AI • Improve Governance baseline for • Prioritization of Generative AI use cases Generative AI adoption / usage that align • Scale Generative AI minimum viable • Enhance existing CoE & Delivery Centers with regulations product (MVP) / assets into production • Develop use cases and MVP environments • Leverage Trustworthy AI Framework to build trust with AI applications (see Deloitte’s Trustworthy AI Framework in the next slide) • Ensure adequate policies and controls on the use of sensitive business and customer data

## 第 8 頁

### 表格 1

| SAFE AND SECURE |
| --- |
|  |

### 表格 2

|  |
| --- |
| PRIVATE |

### 表格 3

|  |
| --- |
| ROBUST / RELIABLE |

### 表格 4

|  |
| --- |
| TRANSPARENT / EXPLAINABLE |

### 表格 5

|  |
| --- |
| ACCOUNTABLE |

### 表格 6

|  |
| --- |
| FAIR / IMPARTIAL |

### 表格 7

|  |
| --- |
| RESPONSIBLE |

Deloitte’s Trustworthy AI Framework Deloitte’s Trustworthy AI Framework helps us to develop ethical safeguards across seven key dimensions—a crucial step in managing the risks and capitalizing on the returns associated with AI. It assists team members and clients in building and using AI-powered systems while promoting trustworthy and responsible use of AI. Deloitte’s Trustworthy AI Framework Guidance and Questions Guide SAFE AND SECURE PRIVATE risks (including Cyber) that may respected in accordance with cause physical and/or digital harm contractual and regulatory obligations, and data is not used beyond its intended and stated ROBUST / RELIABLE purpose AI systems can learn from humans and other systems, where acceptable, and produce consistent and reliable outputs TRANSPARENT / EXPLAINABLE Trustworthy Participants can understand how AI Framework their data is being used and how AI ACCOUNTABLE systems make decisions; algorithms, Policies are in place to determine attributes, and correlations are open who is responsible for the decisions to inspection made or derived with the use of technology FAIR / IMPARTIAL RESPONSIBLE AI applications include internal and The technology is created and external checks to help ensure operated in a socially responsible equitable application across manner participants Note: See details of the Evaluation Questions/Considerations in the “Trustworthy AI Framework” Section.

## 第 9 頁

### Generative AI Risks and Limitations

## 第 10 頁

### 表格 1

| Bias in; bias out. If the training data is biased (e.g., over/under- representation of a population cohort, sexism, racism), then the outputs generated could also exhibit biases. Bias reductions in the training data and/or human supervision during model training is needed |
| --- |
| Bias |

### 表格 2

| These risks pertain to compliance with existing laws, regulations (including independence requirements, see “GAI Independence Considerations”), and standards governing the use of AI technologies which may vary in different jurisdictions (see legislation tracker) |
| --- |
| Legal & Regulatory |

### 表格 3

| Is the AI being used in a manner consistent with the purpose of the overall exercise? Is a human being brought into the loop to decide whether the AI’s suggestion needs adjustment before actual use or whether the use of AI is ethical (e.g., submitting an AI-generated essay as your own)? |
| --- |
| Ethical Use |

### 表格 4

| Models might output statements that are factually false. Sources and citations are unavailable for most models. Users should be conscious that outputs could be inaccurate and perform due diligence to validate generated content |
| --- |
| Hallucination |

### 表格 5

| SaaS-AI companies may use prompt payloads to train future versions of the base model, potentially including confidential data that could expose the user to IP infringement claims, including copyrights, trademarks, or patents. See further information from “GAI IP Considerations” section |
| --- |
| IP Protection & Infringement |

### 表格 6

| Generative AI systems can be vulnerable to adversarial attaches, to maintain operations and customer trust, proactively minimizing risk from malicious behavior on the network is critical. For example, a customer service bot revealing confidential information to a hacker either by prompt or unintentionally |
| --- |
| Malicious Behavior |

### 表格 7

| The opacity of Generative AI systems can hinder understanding and accountability. Lack of transparency and explainability may raise concerns about decision- making processes, bias and adherence to ethical and legal standards |
| --- |
| Transparency & Explainability |

### 表格 8

| Generative AI Models are built on data sharing. Consent for data used (confidential information, personally identifiable information) is necessary, and the residency of data in geo-locations should also be compliant with legal and contractual requirements. See further information from “GAI Data Guardrails” section. |
| --- |
| Confidentiality & Privacy |

### 表格 9

| Generative AI models can exhibit unintended behaviours, reinforce existing biases, or generate content that promotes harmful stereotypes or discriminatory practices, leading to negative social, economic, or culture impacts |
| --- |
| Unintended Consequence |

### 表格 10

| The deployment of Generative AI can have wide-ranging social and economic consequences, including job replacement, economic inequality, changes in labor markets, and shifts in power dynamics, which may require careful consideration and mitigation strategies |
| --- |
| Social and Economic Impacts |

Generative AI Risks and Limitations There are risks and limitations to consider when using Generative AI Bias in; bias out. If the training data These risks pertain to compliance Is the AI being used in a manner Models might output statements SaaS-AI companies may use prompt is biased (e.g., over/under- with existing laws, regulations consistent with the purpose of the that are factually false. Sources and payloads to train future versions of representation of a population (including independence overall exercise? Is a human being citations are unavailable for most the base model, potentially cohort, sexism, racism), then the requirements, see “GAI brought into the loop to decide models. Users should be conscious including confidential data that outputs generated could also Independence Considerations”), whether the AI’s suggestion needs that outputs could be inaccurate could expose the user to IP exhibit biases. Bias reductions in and standards governing the use of adjustment before actual use or and perform due diligence to infringement claims, including the training data and/or human AI technologies which may vary in whether the use of AI is ethical validate generated content copyrights, trademarks, or patents. supervision during model training is different jurisdictions (see (e.g., submitting an AI-generated See further information from “GAI needed legislation tracker) essay as your own)? IP Considerations” section Bias Legal & Regulatory Ethical Use Hallucination IP Protection & Infringement Generative AI systems can be The opacity of Generative AI Generative AI Models are built on Generative AI models can exhibit The deployment of Generative AI vulnerable to adversarial attaches, systems can hinder understanding data sharing. Consent for data used unintended behaviours, reinforce can have wide-ranging social and to maintain operations and and accountability. Lack of (confidential information, existing biases, or generate content economic consequences, including customer trust, proactively transparency and explainability personally identifiable information) that promotes harmful stereotypes job replacement, economic minimizing risk from malicious may raise concerns about decision- is necessary, and the residency of or discriminatory practices, leading inequality, changes in labor behavior on the network is critical. making processes, bias and data in geo-locations should also be to negative social, economic, or markets, and shifts in power For example, a customer service adherence to ethical and legal compliant with legal and culture impacts dynamics, which may require information to a hacker either by further information from “GAI Data mitigation strategies prompt or unintentionally Guardrails” section.

## 第 11 頁

Generative AI – Other risk considerations In addition to Generative AI specific risks and limitations, there are additional risks we need to consider as we leverage Generative AI to transform our service delivery as well as helping our clients to harness the potential of Generative AI. as how the data is processed, whether it is stored or potentially used and shared by the Generative AI applications (e.g.: to use & protection train third party models) • Ineffective security of Generative AI tools may expose Deloitte data, Deloitte client data and Deloitte IP to unauthorized Cyber Security users, malicious activity, phishing attacks and other cyber breaches. • IP rights arising in both the materials used to train the AI (input) and the results created by the AI (output) needs to be Intellectual Property (IP) considered. Copyright laws as they relate to Generative AI created content are complex and evolving. • Emerging global AI / Generative AI regulation will impact the way in which Generative AI tools and models are developed and Regulatory deployed. Sovereignty of data will impact jurisdictional location and application of Generative AI. • Current client contracts terms need to be considered, including whether data can be used within a Generative AI system. Client contractual terms Our current contractual positions may need to evolve to enable our Generative AI strategy, as well as the risk considerations • Independence considerations for Generative AI activities or services include: contracting and use of external data sets, co- Independence developed solutions for clients, alliances and marketplace business relationships within ecosystems, conducting labs for restricted entities and permissible interactions with restricted vendors (e.g. Open AI, Microsoft etc.) Governance governance and oversight, including consideration of business value, legal and regulatory obligations, data use, human intervention, system integrations, and potential for harm.

## 第 12 頁

### Use Case Evaluation and Approval Process

## 第 13 頁

### 表格 1

| Client or Production use | Examples of High or Medium-Risk use case: • Use Gen AI to develop a Deloitte service offering • Use Gen AI (including Deloitte approved Gen AI Platforms2) to enhance existing or new client engagement delivery beyond the guardrail or prescribed permitted use • Apply Gen AI solution developed from experiment or non-production to a client service |
| --- | --- |

### 表格 2

| Experiment or Non- Production Use | Examples of Low-Risk use cases: • Experiment with Gen AI to learn more about its capabilities • Use Gen AI to develop a Proof-of-Concept (PoC) for a client within the client’s environment • Use Gen AI to explore the way to transform Deloitte works internally • Use Gen AI to develop or support development of a bid response or proposal (e.g. to generate content for a presentation) |
| --- | --- |

Generative AI Risk Triage Process In order to enable speed to the market, a Generative AI risk triage process is adopted to stratify use cases into different risk levels with corresponding evaluation process, if any as follows: Approval by PF: “YES” to AI Trust Council/ Public High-risk HIGH-RISK Gen AI Use Case Interest Committee/ indicator RISK TRIAGE RRL before proceed* Approved Deep-Dive process with relevant SMEs Does the use case have any Use Case1 (See slide 16) high-risk or medium-risk Start “YES” to MEDIUM-RISK indicators? Medium-risk here! indicator Examples of High or Medium-Risk use case: (Refer to next 2 slides for key • Use Gen AI to develop a Deloitte service offering considerations from Threshold Client or • Use Gen AI (including Deloitte approved Gen AI Platforms2) to enhance existing or new Risk Assessment Form) Production client engagement delivery beyond the guardrail or prescribed permitted use NO “High-risk” use • Apply Gen AI solution developed from experiment or non-production to a client service risk” indicators Follow relevant Approval requirements: LOW-RISK “Conditions of use” • In the event of “Yes” response questions with high-risk indicator (i.e. red), the business case is required to be endorsed by local AI Trust Council/ Public Examples of Low-Risk use cases: Interest Committee/ RRL before further proceeding. • Experiment with Gen AI to learn more about its capabilities • In the event of more than 1 “Yes” response to questions with medium-risk Experiment • Use Gen AI to develop a Proof-of-Concept (PoC) for a client within the client’s environment indicator (i.e. yellow), it should be considered whether the overall risk level or Non- • Use Gen AI to explore the way to transform Deloitte works internally may be escalated to high-risk. Production • Use Gen AI to develop or support development of a bid response or proposal (e.g. to generate • For both high-risk and medium-risk use case, a Gen AI Use Case Review Form Use content for a presentation) should be completed as submission to relevant SMEs for further review and evaluation (after business case is approved by GAI Clearing house or Market 1. A use case to be developed and endorsed by local GAI Clearing House/ Market Incubator before further proceeding of risk incubator). evaluation. 2. For Deloitte approved Gen AI Platform, please refer to AI Genesis and contact your local ITS. *Approval may require an accelerated deep-dive process with relevant BU QRM and SMEs to inform the “go/no-go” decision

## 第 14 頁

Basic Project Information Client Name/ Requestor Name Engagement Lead Service line / Function Estimated timeframe Expected Fee Short description of the use case and how Generative AI is leveraged to achieve it Overall Rating according to the Threshold Risk Assessment Form (please refer to the next page): High  / Medium  / Low  The form should be completed by the proposed Gen AI use case team and reviewed by designated BU QRM point of contact (see “Contact and Reference” section). The approval requirements are: • In the event of “Yes” response to questions with high-risk indicator (i.e. red), the business case is required to be endorsed by local AI Trust Council/ Public Interest Committee/ RRL before further proceeding. • In the event of more than 1 “Yes” response to questions with medium-risk indicator (i.e. yellow), it should be considered whether the overall risk level may be escalated to high-risk. • For both high-risk and medium-risk use case, a Gen AI Use Case Form should be completed as submission to BU QRM and relevant SMEs for further review and evaluation (after business case is approved by GAI Clearing house or Market incubator).

## 第 15 頁

High / Medium Generative AI Triage Risk Questions Yes No Risk-level 1 Does your use case relate to any of the following? (Select all that apply)   ⚫ • Safety • Biometric identification or verification • Critical infrastructure • AI systems that impact access to service or opportunities (e.g. employment, education, social security, essential services such as healthcare) • Justice/law enforcement • Immigration • Clinical decision making • Asystematic monitoring of a publicly accessible area, such as video surveillance data and network behavioral tracking 2 Is there a potential regulatory impact, public scrutiny, and/or litigation that needs to be considered as part of your use-case?   ⚫ 3 Is our client operating in a heavily regulated industry (e.g. financial services, healthcare and pharmaceuticals, aerospace & defence, etc) and the use case   ⚫ involve the use of client’s confidential data? (i.e. you’re operating in a heavily regulated industry; your use case involvestransferring confidential/sensitive data outside of your jurisdiction)? 4 Does your use case relate to any of the following purpose?   ⚫ • Use Gen AI to develop a Deloitte service offering • Use Gen AI (including Deloitte approved Gen AI Platforms*) to enhance existing or new client engagement delivery beyond the guardrails or permitted use • ApplyGen AI solution developed from experiment or non-production to a client service 5 Is the Gen AI application applied in your use case a non-publicly available source of Gen AI tool or new application which not in the list of Deloitte or your local   ⚫ firm’s approved Gen AI platforms*? If the answer is “yes”, who is the provider/subscriber of the Gen AI application? 6 Does any of the following apply?   ⚫ • The Gen AI application/system will be used by public directly, e.g. an online consumer-facing platform • There is an element of automated decision making (even if there is human review following the automated decision process) • There is decision to be made in reliance on the output of the application • The use case relates to children or vulnerable groups (either from a data input perspective or as the beneficiaries)? • We are scraping publicly available data to generate or obtain insights • We are training/fine-tuning a Large Language Model (LLM) * For Deloitte approved Gen AI Platform, please refer to AI Genesis and contact your local ITS.

## 第 16 頁

### 表格 1

| s e i t i v i t c A y e K | None | None |
| --- | --- | --- |
| s e / t sa l ol p om T e T | Threshold Risk Assessment Form Threshold Risk Assessment Form.docx | Gen AI Use Case Review Form Generative AI Use Case Review Form.docx |

Generative AI Use Case Deep-Dive Approval Process (for Medium/High Risk Use Cases) PHASE Identify Consult Deployment Use Case Submission and Risk Triage Deep-Dive Risk Consultation Risk Support • Engagement partner/LCSP and team contacts GAI • After the use case is endorsed, engagement team • Prior to bringing a solution to real-world Clearinghouse or Market Incubator Team or BU should complete a Gen AI Use Case Review Form production and live data use, the engagement QRM and submits idea into the Threshold Risk and submit to BU QRM. team must complete all applicable GAI testing Assessment Formand pass to GAI Clearinghouse/ and evaluation. • BU QRMperform a deep-dive risk discovery (see Market Incubator Team for risk triage process. next two slides for discovery considerations) • Standard BU QRM processes including DRB, O2E • The GAI Clearinghouse/ Market Incubator team (together with other enabling teams including the applies to GAI engagements. s e would involve BU QRM to review the Threshold GAI Clearinghouse/ Market Incubator, OGC, BSO, i • Engagement team conducts continuous t i Risk Assessment form and inform engagement and Independence) to discuss risk considerations v monitoring andapplication validation throughout i t team which risk level of the use case is in (High/ including mitigations and plan how to best provide c the project lifecycle and escalation to BU QRM as A Medium/ Low) according to the triage. support. necessary. y e K • If the use case is Low risk level, team can proceed by following the “Conditions of Use (for Low Risk Use Cases)” • If the use case is in High/ Medium risk level, use casehas to be further prepared and endorsed by GAI Clearinghouse/Market Incubator team, before further deep-dive evaluation process. s / e t Threshold Risk Assessment Form Gen AI Use Case Review Form sa l ol p N/A om T Threshold Risk Assessment Form.docx Generative AI Use Case Review Form.docx e T Note: If the Generative AI development involves an asset, the above approval process should be embedded as part of the overall Asset Certification process.

## 第 17 頁

### 表格 1

| Generative AI Risk Discovery Considerations Category Considerations User Input There are some dependencies around how Generative AI is used as the outputs generated are based on the inputs provided. This means that outputs generated in the Generative AI tool including recommendations may not be comprehensive and/or incorrect if the user does not prompt correctly. It may not be able to answer questions that are worded in a specific way and may require rewording or refining of the input to understand the ask. Quality & Generative AI could produce biased content that leads to misinformation and ‘fake news’. It is more difficult to know the source and provenance of Misinformation information as there may be a lack of quality in the responses it delivers which can lead to challenges in gaining public trust.Some industries (Finance, Legal, Education, Medical) may not be ready to use Generative AI and carry ‘a greater than normal risk’ of potential harm from misleading or incorrect Outputs. Ethics Consider ethical responsibilities when using Generative AI tools such as Generative AI. Outputs generated using Generative AI could be perceived as your ‘own work’ which is considered unethical and can have serious implications on reputation andbrand (yours and the Deloitte's). It is important that there is transparency around the use of Generative AI. Copyright Misuse of Generative AI can open the door to copyright and cheating. It can promote new kinds of plagiarism that ignore the rights of original content Infringement creators. Gen AI can write an essay within seconds, making it easier for students to cheat, or avoid the process of learning. Such concerns have lead to educational institutions banning or blocking the use of user access to Generative AI tools. Data Limitation Some systems (e.g. ChatGPT) limit AI training data to no later than 2021, so the Gen AI system may have limited knowledge of more recent information and not be able to generate responses utilising data since then. Outputs, answers, and recommendations are therefore not comprehensive and may not include the latest on-point matters/inputs. Misuse with intent to Whilst AI models are programmed to avoid specific content type that is harmful or illegal, such as text containing violence, explicit sex, or other potentially cause harm harmful content, there is potential for Gen AI to unintentionally generate such content. Generative AI systems need to detect harmful or offensive content and need to be constantly monitored in order to be able to detect such content, so it does not produce such output. Information Security Generative AI is opening the door to virtually infinite new ways of carrying out online crime as it could impersonate people formore social engineering cyber-attacks. It can also produce a higher quality of phishing emails than most hackers send today which are usually recognisable by the spelling and grammar errors they contain. In addition, there is the risk of harmful misuse of personal data including data leaks since AI models are trained on large datasets. Note: The above considerations are not exhaustive. © 2024 Deloitte Asia Pacific Services Limited AP Generative AI QRM Guidelines 17 | None | s used as the outputs generated are based on the inputs provided. This means that outputs ations may not be comprehensive and/or incorrect if the user does not prompt correctly. It may ific way and may require rewording or refining of the input to understand the ask. |
| --- | --- | --- |
| None | Considerations |  |
| None | There are some dependencies around how Generative AI is used as the outputs generated are based on the inputs provided. This means that outputs generated in the Generative AI tool including recommendations may not be comprehensive and/or incorrect if the user does not prompt correctly. It may not be able to answer questions that are worded in a specific way and may require rewording or refining of the input to understand the ask. | s used as the outputs generated are based on the inputs provided. This means that outputs ations may not be comprehensive and/or incorrect if the user does not prompt correctly. It may ific way and may require rewording or refining of the input to understand the ask. |
| None | Generative AI could produce biased content that leads to misinformation and ‘fake news’. It is more difficult to know the source and provenance of information as there may be a lack of quality in the responses it delivers which can lead to challenges in gaining public trust.Some industries (Finance, Legal, Education, Medical) may not be ready to use Generative AI and carry ‘a greater than normal risk’ of potential harm from misleading or incorrect Outputs. | None |
| None | Consider ethical responsibilities when using Generative AI tools such as Generative AI. Outputs generated using Generative AI could be perceived as your ‘own work’ which is considered unethical and can have serious implications on reputation andbrand (yours and the Deloitte's). It is important that there is transparency around the use of Generative AI. | None |
| None | Misuse of Generative AI can open the door to copyright and cheating. It can promote new kinds of plagiarism that ignore the rights of original content creators. Gen AI can write an essay within seconds, making it easier for students to cheat, or avoid the process of learning. Such concerns have lead to educational institutions banning or blocking the use of user access to Generative AI tools. | None |
| None | Some systems (e.g. ChatGPT) limit AI training data to no later than 2021, so the Gen AI system may have limited knowledge of more recent information and not be able to generate responses utilising data since then. Outputs, answers, and recommendations are therefore not comprehensive and may not include the latest on-point matters/inputs. | None |
| None | Whilst AI models are programmed to avoid specific content type that is harmful or illegal, such as text containing violence, explicit sex, or other potentially harmful content, there is potential for Gen AI to unintentionally generate such content. Generative AI systems need to detect harmful or offensive content and need to be constantly monitored in order to be able to detect such content, so it does not produce such output. | None |
| None | Generative AI is opening the door to virtually infinite new ways of carrying out online crime as it could impersonate people formore social engineering cyber-attacks. It can also produce a higher quality of phishing emails than most hackers send today which are usually recognisable by the spelling and grammar errors they contain. In addition, there is the risk of harmful misuse of personal data including data leaks since AI models are trained on large datasets. | None |

Generative AI Risk Discovery Considerations Category Considerations User Input There are some dependencies around how Generative AI is used as the outputs generated are based on the inputs provided. This means that outputs generated in the Generative AI tool including recommendations may not be comprehensive and/or incorrect if the user does not prompt correctly. It may not be able to answer questions that are worded in a specific way and may require rewording or refining of the input to understand the ask. Quality & Generative AI could produce biased content that leads to misinformation and ‘fake news’. It is more difficult to know the source and provenance of Misinformation information as there may be a lack of quality in the responses it delivers which can lead to challenges in gaining public trust.Some industries (Finance, Legal, Education, Medical) may not be ready to use Generative AI and carry ‘a greater than normal risk’ of potential harm from misleading or incorrect Outputs. Ethics Consider ethical responsibilities when using Generative AI tools such as Generative AI. Outputs generated using Generative AI could be perceived as your ‘own work’ which is considered unethical and can have serious implications on reputation andbrand (yours and the Deloitte's). It is important that there is transparency around the use of Generative AI. Copyright Misuse of Generative AI can open the door to copyright and cheating. It can promote new kinds of plagiarism that ignore the rights of original content Infringement creators. Gen AI can write an essay within seconds, making it easier for students to cheat, or avoid the process of learning. Such concerns have lead to educational institutions banning or blocking the use of user access to Generative AI tools. Data Limitation Some systems (e.g. ChatGPT) limit AI training data to no later than 2021, so the Gen AI system may have limited knowledge of more recent information and not be able to generate responses utilising data since then. Outputs, answers, and recommendations are therefore not comprehensive and may not include the latest on-point matters/inputs. Misuse with intent to Whilst AI models are programmed to avoid specific content type that is harmful or illegal, such as text containing violence, explicit sex, or other potentially cause harm harmful content, there is potential for Gen AI to unintentionally generate such content. Generative AI systems need to detect harmful or offensive content and need to be constantly monitored in order to be able to detect such content, so it does not produce such output. Information Security Generative AI is opening the door to virtually infinite new ways of carrying out online crime as it could impersonate people formore social engineering cyber-attacks. It can also produce a higher quality of phishing emails than most hackers send today which are usually recognisable by the spelling and grammar errors they contain. In addition, there is the risk of harmful misuse of personal data including data leaks since AI models are trained on large datasets. Note: The above considerations are not exhaustive.

## 第 18 頁

### 表格 1

| Generative AI Risk Discovery Considerations (continued) Category Considerations Data Privacy & Client Confidential Data (that is internal to Deloitte or the client) could invertedly be uploaded into publicly available Generative AI tools. This could have serious Confidentiality consequences including; data privacy regulatory breaches, breaches in our client and third-party contractual obligations, and breaches of Deloitte confidentiality policies, and therefore may pose serious reputational, regulatory, and financial risks to the firm. IP Infringement Because Generative AI is a large language model that has been trained on a number of different datasets, there is a possibility that the responses generated from those datasets could infringe on already established works. Using copyrighted material to train the AI model can cause that model to excessively draw from another’s work, when providing a response to a user, which could lend itself to IP infringement claims. Independence There may be some independence implications regarding the use of Generative AI. For example, due to Microsoft’s significant investment, OpenAI is now subject to the same marketplace independence guidelines and policies as Microsoft. As Microsoft’s Independent Auditor, the independence rules of the SEC and other regulatory and oversight bodies apply to this relationship. Deloitte can provide OpenAI-related technology services to its clients and interact with OpenAI and perform certain activities provided that it meets the terms and conditions that are acceptable when describing the relationship between OpenAI and Deloitte. Ability to Replace Concerns about AI Chatbots ability to replace human intelligence may be justified. The chatbot can write an article on a topic, efficiently eliminating the Human Intelligence need for a human writer, with the potential to make some jobs or specific roles redundant as Generative AI rapidly evolves. Note: The above considerations are not exhaustive. © 2024 Deloitte Asia Pacific Services Limited AP Generative AI QRM Guidelines 18 | None | None | ntinued) could invertedly be uploaded into publicly available Generative AI tools. This could have serious breaches in our client and third-party contractual obligations, and breaches of Deloitte |
| --- | --- | --- | --- |
| None | Category | Considerations |  |
| None | Data Privacy & Client Confidentiality | Confidential Data (that is internal to Deloitte or the client) could invertedly be uploaded into publicly available Generative AI tools. This could have serious consequences including; data privacy regulatory breaches, breaches in our client and third-party contractual obligations, and breaches of Deloitte confidentiality policies, and therefore may pose serious reputational, regulatory, and financial risks to the firm. | could invertedly be uploaded into publicly available Generative AI tools. This could have serious breaches in our client and third-party contractual obligations, and breaches of Deloitte |
| None | IP Infringement | Because Generative AI is a large language model that has been trained on a number of different datasets, there is a possibility that the responses generated from those datasets could infringe on already established works. Using copyrighted material to train the AI model can cause that model to excessively draw from another’s work, when providing a response to a user, which could lend itself to IP infringement claims. | None |
| None | Independence | There may be some independence implications regarding the use of Generative AI. For example, due to Microsoft’s significant investment, OpenAI is now subject to the same marketplace independence guidelines and policies as Microsoft. As Microsoft’s Independent Auditor, the independence rules of the SEC and other regulatory and oversight bodies apply to this relationship. Deloitte can provide OpenAI-related technology services to its clients and interact with OpenAI and perform certain activities provided that it meets the terms and conditions that are acceptable when describing the relationship between OpenAI and Deloitte. | None |
| None | Ability to Replace Human Intelligence | Concerns about AI Chatbots ability to replace human intelligence may be justified. The chatbot can write an article on a topic, efficiently eliminating the need for a human writer, with the potential to make some jobs or specific roles redundant as Generative AI rapidly evolves. | None |

Generative AI Risk Discovery Considerations (continued) Category Considerations Data Privacy & Client Confidential Data (that is internal to Deloitte or the client) could invertedly be uploaded into publicly available Generative AI tools. This could have serious Confidentiality consequences including; data privacy regulatory breaches, breaches in our client and third-party contractual obligations, and breaches of Deloitte IP Infringement Because Generative AI is a large language model that has been trained on a number of different datasets, there is a possibility that the responses generated from those datasets could infringe on already established works. Using copyrighted material to train the AI model can cause that model to excessively draw from another’s work, when providing a response to a user, which could lend itself to IP infringement claims. Independence There may be some independence implications regarding the use of Generative AI. For example, due to Microsoft’s significant investment, OpenAI is now subject to the same marketplace independence guidelines and policies as Microsoft. As Microsoft’s Independent Auditor, the independence rules of the SEC and other regulatory and oversight bodies apply to this relationship. Deloitte can provide OpenAI-related technology services to its clients and interact with OpenAI and perform certain activities provided that it meets the terms and conditions that are acceptable when describing the relationship between OpenAI and Deloitte. Ability to Replace Concerns about AI Chatbots ability to replace human intelligence may be justified. The chatbot can write an article on a topic, efficiently eliminating the Human Intelligence need for a human writer, with the potential to make some jobs or specific roles redundant as Generative AI rapidly evolves. Note: The above considerations are not exhaustive.

## 第 19 頁

### 表格 1

|  | Conditions of Use (for Low Risk Use Cases) We encourage all Deloitte personnel to familiarize themselves with this Generative AI technology. However, Generative AI is a rapidly evolving technology that we strongly advise you to follow the below Conditions of Use for low risk use cases, even if it’s merely for experiment or non-production use purpose. CONDITIONS OF USE We encourage all Deloitte personnel to familiarize themselves with this emerging technology. However, Generative AI is a rapidly evolving technology that we strongly advise you to follow the below Conditions of Use when you use 1. You must follow the Generative AI data guardrail (by Data Type \| by Persona) for the use of the information. As a general rule, we are only permitted to use client confidential puinbfloircmlya taiovna foilra tbhele p uarpnodse/ oofr dDeleivleoriintgt eth-ed eenvgeagloempeendt foGr ewnhiechra thtaivt ein fAorIm taotoiolns w, aesv peronv iidfe idt (’sch mecke trheel ycl iefontr c oenxtpraectr itmo ideenntti foy ra nnyo rens-trpicrtoiodnsu ocnt itohen u uses oef client confidential information) and personal information for the purpose(s) communicated to the individuals for which the information was collected. purpose. Publicly available generative AI platforms or tools (e.g., Chat GPT) are not approved for use in client or internal works, as they do not have the security and confidentiality standards that we require. Deloitte or client confidential information should not be put into such platforms or tools to generate idea. 2. Betransparentabout the use of generative AI in your work. You must record your sources for any output generated using Generative AI tools. 3. You must fact check the informationyou receive when using Generative AI tools and be mindful that 'hallucination' in AI chatbots can lead to the generation ofincorrect answers. It is expected that appropriate “human-in-the-loop” and review is carried out on any outputs from Generative AI tools to ensure a suitable quality to be used in connection with your use case. 4. You must act in accordance with ourGlobal Principles of Business Conduct as well as this Guidelines (including Trustworthy AI) and consider your ethical responsibilities when using generative AI tools. 5. You must notuseGenerativeAItoolstocreateworkproductsusinginformationfromdifferentclients(e.g. do not reference documents related to different clients for summarization in a single document). Deloitte is required to keep the confidential information of different clients separate. 6. You must be mindful and adhere to any local relevant laws and regulations in related to data in your jurisdiction. Some countries may have specific requirements on data residency and prohibited certain data to transmit outside of its local jurisdiction. If you are unsure, check your contract with your client and consult with your BU QRM team. 7. In case there is any change in the use case that warrants additional risk assessment, you should consult with your BU QRM point of contact to initiate the re-evaluation. | None |
| --- | --- | --- |
| None | ©© 22002244 DDeellooiittttee AAssiiaa PPaacciiffiicc SSeerrvviicceess LLiimmiitteedd | AP Generative AI QRM Guidelines 19 |

Conditions of Use (for Low Risk Use Cases) We encourage all Deloitte personnel to familiarize themselves with this Generative AI technology. However, Generative AI is a rapidly evolving technology that we strongly advise you to follow the below Conditions of Use for low risk use cases, even if it’s merely for experiment or non-production use purpose. CONDITIONS OF USE We encourage all Deloitte personnel to familiarize themselves with this emerging technology. However, Generative AI is a rapidly evolving technology that we strongly advise you to follow the below Conditions of Use when you use 1. You must follow the Generative AI data guardrail (by Data Type | by Persona) for the use of the information. As a general rule, we are only permitted to use client confidential puinbfloircmlya taiovna foilra tbhele p uarpnodse/ oofr dDeleivleoriintgt eth-ed eenvgeagloempeendt foGr ewnhiechra thtaivt ein fAorIm taotoiolns w, aesv peronv iidfe idt (’sch mecke trheel ycl iefontr c oenxtpraectr itmo ideenntti foy ra nnyo rens-trpicrtoiodnsu ocnt itohen u uses oef client purpose. Publicly available generative AI platforms or tools (e.g., Chat GPT) are not approved for use in client or internal works, as they do not have the security and confidentiality 2. Betransparentabout the use of generative AI in your work. You must record your sources for any output generated using Generative AI tools. 3. You must fact check the informationyou receive when using Generative AI tools and be mindful that 'hallucination' in AI chatbots can lead to the generation ofincorrect answers. It is expected that appropriate “human-in-the-loop” and review is carried out on any outputs from Generative AI tools to ensure a suitable quality to be used in connection with your use case. 4. You must act in accordance with ourGlobal Principles of Business Conduct as well as this Guidelines (including Trustworthy AI) and consider your ethical responsibilities when using generative AI tools. 5. You must notuseGenerativeAItoolstocreateworkproductsusinginformationfromdifferentclients(e.g. do not reference documents related to different clients for 6. You must be mindful and adhere to any local relevant laws and regulations in related to data in your jurisdiction. Some countries may have specific requirements on data residency and prohibited certain data to transmit outside of its local jurisdiction. If you are unsure, check your contract with your client and consult with your BU QRM team. 7. In case there is any change in the use case that warrants additional risk assessment, you should consult with your BU QRM point of contact to initiate the re-evaluation.

## 第 20 頁

### Generative AI Data Guardrails - By Data Type

## 第 21 頁

### 表格 1

| Data type | Examples of data that are permitted to be used in an appropriate Generative AI tool* | Examples of data NOT permitted to be used | Key QRM considerations |
| --- | --- | --- | --- |

Generative AI Data Guardrails: What is and is not permissible Data type Examples of data that are permitted to be used in an Examples of data NOT permitted to be used Key QRM considerations appropriate Generative AI tool* Deloitte • Any information available on Deloitte’s external website • Employee personal information without specific • Does Deloitte own the intellectual property in, or have a license data • Deloitte employment opportunity listings approval from Quality & Risk and Talent. to use, the materials in connection with the Generative AI use • Deloitte developed templates and proposals, quals, and • Materials in which Deloitte owns the intellectual case? approaches (must exclude all Client Information) property but which contain client or third-party • Does the data include client or third-party names or other client • Internal Deloittebusiness requirements/code • If the data is personal information, have we considered our • Deloitte training materials obligations under the relevant privacy laws and regulations? Client • [Client Information where we have consent from the client in • Client Information not on the “Examples of data • Client Information should only be hosted and processed within a Information1 writing to use the information for the specific purpose related approved list” or that otherwise has been Deloitte approved secure environment (for an up to date to Generative AI.] specifically approved for use in connection with approved secure environment, please contact ITS or • Client deliverables or workpapers that were created in the Generative AI by Q&R (in consultation with the Clearinghouse. course of an engagement performed on Deloitte’s standard LCSP and OGC as appropriate). • What does our contract with the client say about how Deloitte is T&Cs (without amendment) and which have been cleansed of • Information in which the client owns the IP permitted to use Client Information and who owns the IP in any all Client Information (similar to KX materials). (including Deloitte deliverables in which the deliverables? Is the proposed use consistent with those terms? • Client Information that was provided to Deloitte under client owns the IP). • Even where we have the rights to use the client information, do Deloitte’s standard T&Cs (without amendment) and that has • Client Information that has be aggregated to a we think that the client would be comfortable with our proposed been aggregated to a point where it is not possible to identify point where the client is identifiable. use case and/or should we inform the client? the client that the information relates to and the ultimate • Client information containing personal • Are there any restrictions on the data being transferred outside purpose of using the information is for research or to deliver information. the local jurisdiction or geography (note some Generative AI further advice (e.g., an advisory engagement).2 models are not hosted within local jurisdiction)? Personal • Personal Information, where the processing is consistent with • All Personal Information where the proposed • Is the purpose for which the information will be processed by, or Information3 the purpose(s) for which the information was collected (i.e. purpose/s for which the information will be in connection with, the AI compatible with original purpose for the purpose communicated to the individuals to whom the processed by, or in connection with, the AI is which the information was collected? information relates). incompatible with the purpose for which the • If consent has been obtained in relation to the proposed • [It may be permissible to process personal information for a Personal Information was originally collected. secondary processing, does that consent meet the requirements secondary purpose] where express consent has been obtained for valid consent under the relevant privacy laws and regulations? from the individuals whose personal information will be processed by, or in connection with, the AI.

## 第 22 頁

### 表格 1

| Data type | Examples of data that are permitted to be used in an appropriate Generative AI tool* | Examples of data NOT permitted to be used | Key QRM considerations |
| --- | --- | --- | --- |

Generative AI Data Guardrails: What is and is not permissible (continued) Data type Examples of data that are permitted to be used in an Examples of data NOT permitted to be used Key QRM considerations appropriate Generative AI tool* Publicly • Information (open source or proprietary) that is not protected • Information (open source or proprietary) • Is the data source subject to copyright or trademark? available by trademark, copyright, license terms or other specific terms protected by trademark or copyright or • Are there terms of use limiting Deloitte’s ability to use the data of use that restrict the purposes for which the information can governed by specific license terms that restrict information? be used. the way in which the information can be used, • Is data being obtained from web scraping? • Information protected by trademark, copyright or specific use andfor which consent to use has not been obtained. • Is the collection, use or disclosure of the personal information terms (e.g., website terms) for which consent to use has been consistent with the relevant privacy laws and regulations? obtained. • Data obtained from web scraping.4 • Publicly available personal information and specific approval has not been obtained from Quality & Risk in consultation with OGC. Purchased • Third party data that has been procured or purchased from a • Other third-party data, unless specific approval • Does the procured data include any use restrictions (e.g., AI use, third party third party for Deloitteuse and the third-party contract allows has been obtained from Quality & Risk. combination with other data sets)? Data for the requested generative AI data use case (consistent with • Is the source data subject to click-through license terms or other any contract terms). online terms of use? Synthetic/ • Fake or mock data generated as a substitute for live or real • Third party proprietary data that has been • Is the data fake data and not simply anonymized? Dummy data data. Synthetic data does NOT include any real data, anonymized without owner consent to do so. • Consent may be needed before anonymizing any confidential or development or production environment data, including • Anonymized data is not the same as synthetic personal information for secondary purposes. *Deloitte and Client information must not be inputted into publicly available generative AI tools (including Chat GPT, Google Bard and Bing Chat or other locally available Chatbot). For more information on appropriate use of generative AI tools, contact the local Clearinghouse or Market Incubator teams. 1 “Client Information” refers as: “any information, documents, materials, facts, instructions or Confidential Information provided to us by or on behalf of the client. Client Information (also called ‘Client Data’) may be defined differently under specific client agreements. 2 An example of a use where the purpose is not for research or to deliver further advice might be where the information is being used to develop a product (i.e., an asset) that we intend to sell to a client. 3 Personal Information is information about an individual or from which an individual is able to be identified. The processing of personal information by Deloitte, including personal information that is publicly available, is regulated by the relevant privacy laws and regulations. Personal Information includes both client and Deloitte Personal Information. 4 As well as the potential to be inconsistent with website terms of use, web-scraping raises ethical risk that need to be considered.

## 第 23 頁

### Generative AI Data Guardrails - By Persona

## 第 24 頁

### 表格 1

| I am a Deloitte Practitioner seeking to use Deloitte approved AI Systems ... so that I can enhance how I work and perform in my role. I will I will not Upload Deloitte or client confidential1 information into Deloitte approved AI Upload any client confidential1 information into a Deloitte approved AI system if Systems (subject to any restrictions in my client contract). doing so would violate any client contractual commitments, unless specifically authorised by the client. Such contractual restrictions may Familiarise myself with any AI Systems’ specific usage guidelines or terms of use include: and comply with any additional usage guidelines from my business unit (e.g. A&A). • Using client confidential information for a secondary purpose Review and fact check any outputs (having reference to the source documents) (e.g. a purpose other than the delivery of the engagement for and be mindful that AI Systems may generate errors or make up information which the client provided the information to Deloitte); (called 'hallucinations’). • General restrictions on the use of AI Systems; and Ensure that my work is reviewed by a senior Deloitte professional before it is incorporated into any client work or deliverable and make the reviewer aware of • Restrictions on transferring or storing client confidential how I used AI Systems. information offshore (unless I have confirmed that the AI System is hosted in local geography). Be transparent about my use of AI outputs in my work, especially when Use AI systems to answer questions which require consultation with leadership or communicating with clients, and will consult with my Quality & Risk team where I subject matter specialists (i.e., Legal, Risk, Independence, etc.). am unsure of the appropriate level of transparency. Attempt to pass off content generated by AI Systems as my own. Notify people before using an AI system to capture or record their words or actions (e.g. an AI System to summarise a meeting). Use an AI System to infer or generate personal information about a person. Always act in accordance with our Global Principles of Business Conduct and consider my ethical responsibilities when using AI Systems, consulting my Quality & Risk team where I am unsure. 1 Confidential information means data not known to the public that relates to our business or that we receive in the course of business from other Deloitte personnel, our clients, or third parties. Client Confidential Information includes but may not be Proactively educate myself on the limitations of AI Systems and best practices, and limited to information that has any of the following characteristics: (a) It is not known by or available to the general public; (b) It routinely revisit these guidelines to help ensure my use of AI Systems is both is not useless or trivial but is capable of harming the Client’s or third parties’ interests if used or disclosed; (c) It has been effective and responsible. communicated in circumstances establishing an obligation of confidence; (d) It otherwise cannot be disclosed because of Local Laws. © 2024 Deloitte Asia Pacific Services Limited AP Generative AI QRM Guidelines 24 | if p or receive in the course of cludes but may not be o the general public; (b) It sed; (c) It has been isclosed because of Local |
| --- | --- |

I am a Deloitte Practitioner seeking to use Deloitte approved AI Systems ... so that I can enhance how I work and perform in my role. I will I will not Upload Deloitte or client confidential1 information into Deloitte approved AI Upload any client confidential1 information into a Deloitte approved AI system if Systems (subject to any restrictions in my client contract). doing so would violate any client contractual commitments, unless specifically authorised by the client. Such contractual restrictions may Familiarise myself with any AI Systems’ specific usage guidelines or terms of use include: and comply with any additional usage guidelines from my business unit (e.g. A&A). Review and fact check any outputs (having reference to the source documents) (e.g. a purpose other than the delivery of the engagement for and be mindful that AI Systems may generate errors or make up information which the client provided the information to Deloitte); (called 'hallucinations’). • General restrictions on the use of AI Systems; and Ensure that my work is reviewed by a senior Deloitte professional before it is how I used AI Systems. information offshore (unless I have confirmed that the AI System is hosted in local geography). Be transparent about my use of AI outputs in my work, especially when Use AI systems to answer questions which require consultation with leadership or communicating with clients, and will consult with my Quality & Risk team where I subject matter specialists (i.e., Legal, Risk, Independence, etc.). am unsure of the appropriate level of transparency. Attempt to pass off content generated by AI Systems as my own. Notify people before using an AI system to capture or record their words or actions (e.g. an AI System to summarise a meeting). Use an AI System to infer or generate personal information about a person. Always act in accordance with our Global Principles of Business Conduct and consider my ethical responsibilities when using AI Systems, consulting my Quality & Risk team where I am unsure. Proactively educate myself on the limitations of AI Systems and best practices, and limited to information that has any of the following characteristics: (a) It is not known by or available to the general public; (b) It routinely revisit these guidelines to help ensure my use of AI Systems is both is not useless or trivial but is capable of harming the Client’s or third parties’ interests if used or disclosed; (c) It has been effective and responsible. communicated in circumstances establishing an obligation of confidence; (d) It otherwise cannot be disclosed because of Local Laws.

## 第 25 頁

### 表格 1

| Data Guidance for Deloitte Practitioners using Deloitte approved AI Systems The following data is permitted to be used. Deloitte confidential information Use of Personal Information in line with the primary purpose Synthetic data of collection Deloitte information that you have access to (provided you Fake or mock data generated as a substitute for respect any restrictions on how it can be used), including: Personal information where the use is consistent with the production/real data. purpose for which the information was originally collected • Deloitte information accessible on the public internet (e.g. and for which you were given access (i.e. the purpose D.com, employment listings). communicated to the individuals to whom the information Guiding Questions • Internal policies, procedures and guidance documents. relates). Extra caution should be exercised whenever handling • Training materials & templates. personal information. If you want to use personal information To ensure compliance, please consider the for a secondary purpose, you must consult with your Quality following: Client data & Risk team. 1. What type of data am I including in my prompt? Client information provided that your use of that information Other types of non-confidential or 2. Where did I get that data from? is consistent with any restrictions imposed on Deloitte in our generic data 3. Am I confident that I am permitted to use the agreement with the client (as a general rule we are only data for this purpose? permitted to use client confidential information for the Data that does not contain any specific information or purpose of the engagement for which that information was personally identifiable details about individuals or clients. It is provided). typically general or non-specific data that can be used for various purposes without compromising privacy or Publicly available data (without restrictions) confidentiality. Publicly available information (open source or proprietary) Anonymised data that is not protected by trademark, copyright, license terms or other specific terms of use that restrict the purposes for Anonymised data includes real data that has been masked, which the information can be used. scrambled or otherwise deidentified. © 2024 Deloitte Asia Pacific Services Limited AP Generative AI QRM Guidelines 25 | nerated as a substitute for . please consider the am I including in my t data from? I am permitted to use the se? |
| --- | --- |

### 表格 2

| Synthetic data Fake or mock data ge production/real data Guiding Questions To ensure compliance, following: 1. What type of data prompt? 2. Where did I get tha 3. Am I confident that data for this purpo | nerated as a substitute for . please consider the am I including in my t data from? I am permitted to use the se? |
| --- | --- |

Data Guidance for Deloitte Practitioners using Deloitte approved AI Systems The following data is permitted to be used. of collection Deloitte information that you have access to (provided you Fake or mock data generated as a substitute for respect any restrictions on how it can be used), including: Personal information where the use is consistent with the production/real data. purpose for which the information was originally collected • Deloitte information accessible on the public internet (e.g. and for which you were given access (i.e. the purpose D.com, employment listings). communicated to the individuals to whom the information Guiding Questions • Internal policies, procedures and guidance documents. relates). Extra caution should be exercised whenever handling • Training materials & templates. personal information. If you want to use personal information To ensure compliance, please consider the for a secondary purpose, you must consult with your Quality following: Client data & Risk team. 1. What type of data am I including in my prompt? 2. Where did I get that data from? is consistent with any restrictions imposed on Deloitte in our generic data 3. Am I confident that I am permitted to use the agreement with the client (as a general rule we are only data for this purpose? purpose of the engagement for which that information was personally identifiable details about individuals or clients. It is provided). typically general or non-specific data that can be used for various purposes without compromising privacy or Publicly available information (open source or proprietary) Anonymised data that is not protected by trademark, copyright, license terms or other specific terms of use that restrict the purposes for Anonymised data includes real data that has been masked, which the information can be used. scrambled or otherwise deidentified.

## 第 26 頁

### 表格 1

| Data Guidance for Deloitte Practitioners using Deloitte approved AI Systems The following data isn’t permitted to be used. Deloitte confidential information Publicly available data (with restrictions) Data obtained by web scraping Employee personal information without consultation with Information (open source or proprietary) protected by As well as the potential to be inconsistent with website your Quality & Risk team. trademark, copyright, license terms or other specific terms terms of use, web-scraping raises ethical risks that need to of use that restrict the purposes for which the information be considered. Data scraped from the public internet Client data can be used, where are proposed use is inconsistent with should not be used without further consultation with your those restrictions. Quality & Risk team. Client confidential information or intellectual property for a purpose other than the delivery of the engagement for Use of Personal Information for a secondary purpose Data with residency restrictions (i.e. data which must which it was provided for, or in a way that is inconsistent reside in local geography) with any restrictions imposed by the client (unless we have • Personal information is only permitted to be used for Data that is subject to restrictions on the location where written consent from the client). If unsure consult with the purpose for which it was originally collected (i.e. the that data can be stored or processed. You must first consult your Quality & Risk team. purpose communicated to the individuals to whom the your Quality & Risk team before uploading such data into information relates). If unsure consult with your Quality an AI System. & Risk team. • Publicly available personal information obtained from the internet (e.g. Social media profiles) where consultation with your Quality & Risk team has not occurred. 26 © 2024 Deloitte Asia Pacific Services Limited AP Generative AI QRM Guidelines 26 | aping be inconsistent with website g raises ethical risks that need to ed from the public internet ut further consultation with your ictions (i.e. data which must ) trictions on the location where processed. You must first consult before uploading such data into |
| --- | --- |

### 表格 2

| Data obtained by web scr As well as the potential to terms of use, web-scrapin be considered. Data scrap should not be used witho Quality & Risk team. Data with residency restr reside in local geography Data that is subject to res that data can be stored or your Quality & Risk team an AI System. | aping be inconsistent with website g raises ethical risks that need to ed from the public internet ut further consultation with your ictions (i.e. data which must ) trictions on the location where processed. You must first consult before uploading such data into |
| --- | --- |

Data Guidance for Deloitte Practitioners using Deloitte approved AI Systems The following data isn’t permitted to be used. Employee personal information without consultation with Information (open source or proprietary) protected by As well as the potential to be inconsistent with website your Quality & Risk team. trademark, copyright, license terms or other specific terms terms of use, web-scraping raises ethical risks that need to of use that restrict the purposes for which the information be considered. Data scraped from the public internet Client data can be used, where are proposed use is inconsistent with should not be used without further consultation with your those restrictions. Quality & Risk team. purpose other than the delivery of the engagement for Use of Personal Information for a secondary purpose Data with residency restrictions (i.e. data which must which it was provided for, or in a way that is inconsistent reside in local geography) with any restrictions imposed by the client (unless we have • Personal information is only permitted to be used for Data that is subject to restrictions on the location where written consent from the client). If unsure consult with the purpose for which it was originally collected (i.e. the that data can be stored or processed. You must first consult your Quality & Risk team. purpose communicated to the individuals to whom the your Quality & Risk team before uploading such data into information relates). If unsure consult with your Quality an AI System. & Risk team. • Publicly available personal information obtained from the internet (e.g. Social media profiles) where consultation with your Quality & Risk team has not occurred. 26

## 第 27 頁

### 表格 1

| FAQs for Deloitte Practitioners using Deloitte approved AI Systems Are Deloitte approved AI Systems secure enough to use with I’m currently working for Client A. Can I use documents which What are some examples of permitted uses of client/Deloitte documents? came from Client B using an approved AI System for use as part Deloitte approved AI Systems? of my work with Client A? Yes, approved AI Systems are reviewed and approved for use in * IMPORTANT * These examples must follow connection with client/Deloitte documents. However, you must No is the starting point. However, the answer will depend on the guidelines. ensure that you have the appropriate rights to use the whether the documents contain confidential information or client/Deloitte documents (see the Guidelines on the previous intellectual property that belongs to Client B. If the answer to both • Summarising Deloitte internal meeting slides). those questions is that Deloitte owns the intellectual property and transcripts with the permission of that all client confidential information has been cleansed participants in the meeting. Do I need to tell anyone I'm using Deloitte approved AI Systems? appropriately (in consultation with your Quality & Risk team or the • Drafting internal communications to post on E.g. my manager? Partner? Client? LCSP, as appropriate), then it may be permissible to use the intranet. document for Client A. The rule you should always follow is ‘if • Generating a first draft a document, or You must be open and transparent with your engagement team unsure, consult!’ propose alternative wording in an existing and client about your use of AI Systems.When deciding whether document, which I then review and edit. the client needs to be informed about your use of an AI System, you and your engagement team should consider the client’s expectation, your contract with the client and the extent of your use of AI in your work. Incidental use (such as using a generative AI chatbot to help reword a sentence or for early-stage research) may not need to be disclosed. Can I use Deloitte approved AI Systems to help me answer legal, regulatory, and similar questions? No, it is essential that questions requiring specialist knowledge are answered by the appropriate professional. This is due to the potential significant impact of providing incorrect answers. 27 © 2024 Deloitte Asia Pacific Services Limited AP Generative AI QRM Guidelines 27 | examples of permitted uses of ved AI Systems? * These examples must follow g Deloitte internal meeting with the permission of in the meeting. ernal communications to post on a first draft a document, or ernative wording in an existing which I then review and edit. |
| --- | --- |

### 表格 2

| What are some Deloitte appro * IMPORTANT the guidelines. • Summarisin transcripts participants • Drafting int intranet. • Generating propose alt document, | examples of permitted uses of ved AI Systems? * These examples must follow g Deloitte internal meeting with the permission of in the meeting. ernal communications to post on a first draft a document, or ernative wording in an existing which I then review and edit. |
| --- | --- |

FAQs for Deloitte Practitioners using Deloitte approved AI Systems Are Deloitte approved AI Systems secure enough to use with I’m currently working for Client A. Can I use documents which What are some examples of permitted uses of client/Deloitte documents? came from Client B using an approved AI System for use as part Deloitte approved AI Systems? of my work with Client A? Yes, approved AI Systems are reviewed and approved for use in * IMPORTANT * These examples must follow connection with client/Deloitte documents. However, you must No is the starting point. However, the answer will depend on the guidelines. client/Deloitte documents (see the Guidelines on the previous intellectual property that belongs to Client B. If the answer to both • Summarising Deloitte internal meeting slides). those questions is that Deloitte owns the intellectual property and transcripts with the permission of Do I need to tell anyone I'm using Deloitte approved AI Systems? appropriately (in consultation with your Quality & Risk team or the • Drafting internal communications to post on E.g. my manager? Partner? Client? LCSP, as appropriate), then it may be permissible to use the intranet. You must be open and transparent with your engagement team unsure, consult!’ propose alternative wording in an existing and client about your use of AI Systems.When deciding whether document, which I then review and edit. the client needs to be informed about your use of an AI System, you and your engagement team should consider the client’s expectation, your contract with the client and the extent of your use of AI in your work. Incidental use (such as using a generative AI chatbot to help reword a sentence or for early-stage research) may not need to be disclosed. Can I use Deloitte approved AI Systems to help me answer legal, regulatory, and similar questions? No, it is essential that questions requiring specialist knowledge are answered by the appropriate professional. This is due to the potential significant impact of providing incorrect answers. 27

## 第 28 頁

### 表格 1

| I am a Deloitte Practitioner seeking to use Publicly Available AI Systems ... that have not been approved by Deloitte so that I can enhance how I work and perform in my role. I will I will not Review and fact check any outputs (having reference to the source documents) Upload any Deloitte or client confidential1 information or personal and be mindful that AI Systems may generate errors or make up information information into Deloitte non-approved (e.g. publicly available) AI (called 'hallucinations’). Systems. Ensure that my work is reviewed by a senior Deloitte professional before it is Use AI Systems to answer questions which require consultation with incorporated into any Deloitte or client work or deliverable and make the leadership or subject matter specialists (i.e. Legal, Risk, reviewer aware of how I used AI Systems. Independence, etc.) Be transparent about my use of AI outputs in my work, especially when Attempt to pass off content generated solely by AI Systems as my communicating with clients, and will consult with my Quality & Risk team own. where I am unsure of the appropriate level of transparency. Use any AI System if such use would violate any contractual Review the terms of use for any Deloitte non-approved (e.g. publicly available) commitment with a client, unless specifically authorised by the client. AI Systems to ensure that they are permitted to be used for commercial purposes and that Deloitte will own any outputs of the AI System. Always act in accordance with our Global Principles of Business Conduct and consider my ethical responsibilities when using AI Systems, consulting my Quality & Risk team where I am unsure. Proactively educate myself on the limitations of AI Systems and best practices, and routinely revisit these guidelines to help ensure my use of AI Systems is 1 Confidential information means data not known to the public that relates to our business or that we receive in the course of business both effective and responsible. from other Deloitte personnel, our clients, or third parties. Client Confidential Information includes but may not be limited to information that has any of the following characteristics: (a) It is not known by or available to the general public; (b) It is not useless or trivial but is capable of harming the Client’s or third parties’ interests if used or disclosed; (c) It has been communicated in circumstances establishing an obligation of confidence; (d) It otherwise cannot be disclosed because of Local Laws. © 2024 Deloitte Asia Pacific Services Limited AP Generative AI QRM Guidelines 28 | eceive in the course of business may not be limited to al public; (b) It is not useless or n communicated in circumstances |
| --- | --- |

I am a Deloitte Practitioner seeking to use Publicly Available AI Systems ... that have not been approved by Deloitte so that I can enhance how I work and perform in my role. I will I will not and be mindful that AI Systems may generate errors or make up information information into Deloitte non-approved (e.g. publicly available) AI (called 'hallucinations’). Systems. Ensure that my work is reviewed by a senior Deloitte professional before it is Use AI Systems to answer questions which require consultation with incorporated into any Deloitte or client work or deliverable and make the leadership or subject matter specialists (i.e. Legal, Risk, reviewer aware of how I used AI Systems. Independence, etc.) Be transparent about my use of AI outputs in my work, especially when Attempt to pass off content generated solely by AI Systems as my communicating with clients, and will consult with my Quality & Risk team own. where I am unsure of the appropriate level of transparency. Use any AI System if such use would violate any contractual Review the terms of use for any Deloitte non-approved (e.g. publicly available) commitment with a client, unless specifically authorised by the client. AI Systems to ensure that they are permitted to be used for commercial purposes and that Deloitte will own any outputs of the AI System. Always act in accordance with our Global Principles of Business Conduct and consider my ethical responsibilities when using AI Systems, consulting my Quality & Risk team where I am unsure. Proactively educate myself on the limitations of AI Systems and best practices, and routinely revisit these guidelines to help ensure my use of AI Systems is 1 Confidential information means data not known to the public that relates to our business or that we receive in the course of business both effective and responsible. from other Deloitte personnel, our clients, or third parties. Client Confidential Information includes but may not be limited to information that has any of the following characteristics: (a) It is not known by or available to the general public; (b) It is not useless or trivial but is capable of harming the Client’s or third parties’ interests if used or disclosed; (c) It has been communicated in circumstances establishing an obligation of confidence; (d) It otherwise cannot be disclosed because of Local Laws.

## 第 29 頁

### 表格 1

| Data Guidance for Deloitte Practitioners using Publicly Available AI Systems The following data is permitted to be used. Publicly available data (without restrictions) Guiding Questions To ensure compliance, please consider Publicly available information (open source or proprietary) that is not the following: protected by trademark, copyright, licence terms or other specific terms of use that restrict the purposes for which the information can be used. 1. What type of data am I including in my prompt? 2. Where did I get that data from? Non-confidential1 (e.g. publicly available) Deloitte data 3. Am I confident that I am permitted to use the data for this purpose? Deloitte information accessible on the public internet (e.g. Deloitte’s external website - D.com). Synthetic data Fake or mock data generated as a substitute for production/real data. Synthetic data does not include anonymised data. Other types of non-confidential1 or generic data Data that does not contain any specific information or personally identifiable details about individuals or clients. It is typically general or non-specific data that can be used for various purposes without compromising privacy or confidentiality. © 2024 Deloitte Asia Pacific Services Limited AP Generative AI QRM Guidelines 29 | ? |
| --- | --- |

Data Guidance for Deloitte Practitioners using Publicly Available AI Systems The following data is permitted to be used. Publicly available data (without restrictions) Guiding Questions To ensure compliance, please consider Publicly available information (open source or proprietary) that is not the following: protected by trademark, copyright, licence terms or other specific terms of use that restrict the purposes for which the information can be used. 1. What type of data am I including in my prompt? 2. Where did I get that data from? data for this purpose? Deloitte information accessible on the public internet (e.g. Deloitte’s external website - D.com). Synthetic data Fake or mock data generated as a substitute for production/real data. Synthetic data does not include anonymised data. Data that does not contain any specific information or personally identifiable details about individuals or clients. It is typically general or non-specific data that can be used for various purposes without compromising privacy or

## 第 30 頁

### 表格 1

| Data Guidance for Deloitte Practitioners using Publicly Available AI Systems The following data isn’t permitted to be used. Client and Deloitte confidential information1 Data obtained by web scraping 1 Confidential information means data not known to the Do not upload client or Deloitte confidential information1 As well as the potential to be inconsistent with website public that relates to our business or that we receive in the into publicly available AI Systems. This includes any terms of use, web-scraping raises ethical risks that need to course of business from other Deloitte personnel, our clients, information with client / Deloitte’s names in it or be considered. Data scraped from the public internet or third parties. Client Confidential Information includes but information which is not publicly available. should not be used without further consultation with your may not be limited to information that has any of the Quality & Risk team. following characteristics: (a) It is not known by or available to Personal information the general public; (b) It is not useless or trivial but is capable Personal information must not be uploaded to publicly Anonymised data of harming the Client’s or third parties’ interests if used or available AI systems. Anonymised data includes real data that has been masked, disclosed; (c) It has been communicated in circumstances scrambled or otherwise deidentified. Removing names, establishing an obligation of confidence; (d) It otherwise • Do not use AI Systems to infer or generate personal addresses, and other personal information from data cannot be disclosed because of Local Laws. information about a person. doesn't guarantee its safety for use. • This includes publicly available Personal information obtained from the internet (e.g. Social media Purchased third party data profiles)where consultation with your Quality & Risk Third party data that has been procured or purchased from team has not occurred. a third party for Deloitte use. Specific consultation with your Quality & Risk team is required (with reference to the Publicly available data (with restrictions) contract with the third party). Information (open source or proprietary) protected by trademark, copyright, license terms or other specific terms of use that restrict the purposes for which the information can be used. • Read any licence terms that attach to the data before using it in connection with AI Systems. 30 © 2024 Deloitte Asia Pacific Services Limited AP Generative AI QRM Guidelines 30 | eans data not known to the siness or that we receive in the er Deloitte personnel, our clients, idential Information includes but mation that has any of the ) It is not known by or available to ot useless or trivial but is capable hird parties’ interests if used or mmunicated in circumstances f confidence; (d) It otherwise e of Local Laws. |
| --- | --- |

### 表格 2

| 1 Confidential information m public that relates to our bu course of business from oth or third parties. Client Conf may not be limited to infor following characteristics: (a the general public; (b) It is n of harming the Client’s or t disclosed; (c) It has been co establishing an obligation o cannot be disclosed becaus | eans data not known to the siness or that we receive in the er Deloitte personnel, our clients, idential Information includes but mation that has any of the ) It is not known by or available to ot useless or trivial but is capable hird parties’ interests if used or mmunicated in circumstances f confidence; (d) It otherwise e of Local Laws. |
| --- | --- |

Data Guidance for Deloitte Practitioners using Publicly Available AI Systems The following data isn’t permitted to be used. public that relates to our business or that we receive in the into publicly available AI Systems. This includes any terms of use, web-scraping raises ethical risks that need to course of business from other Deloitte personnel, our clients, information with client / Deloitte’s names in it or be considered. Data scraped from the public internet information which is not publicly available. should not be used without further consultation with your may not be limited to information that has any of the Quality & Risk team. following characteristics: (a) It is not known by or available to Personal information the general public; (b) It is not useless or trivial but is capable Personal information must not be uploaded to publicly Anonymised data of harming the Client’s or third parties’ interests if used or available AI systems. Anonymised data includes real data that has been masked, disclosed; (c) It has been communicated in circumstances scrambled or otherwise deidentified. Removing names, establishing an obligation of confidence; (d) It otherwise • Do not use AI Systems to infer or generate personal addresses, and other personal information from data cannot be disclosed because of Local Laws. information about a person. doesn't guarantee its safety for use. • This includes publicly available Personal information obtained from the internet (e.g. Social media Purchased third party data profiles)where consultation with your Quality & Risk Third party data that has been procured or purchased from team has not occurred. a third party for Deloitte use. Specific consultation with your Quality & Risk team is required (with reference to the Publicly available data (with restrictions) contract with the third party). Information (open source or proprietary) protected by trademark, copyright, license terms or other specific terms of use that restrict the purposes for which the information can be used. • Read any licence terms that attach to the data before using it in connection with AI Systems. 30

## 第 31 頁

### 表格 1

| FAQs for Deloitte Practitioners using Publicly Available AI Systems What is a Deloitte Publicly Available (non-approved) AI System? information (i.e. information about Deloitte’s business that is not What are some examples of permitted uses of public) they cannot be inputted into non-approved AI Systems. non-approved publicly available AI Systems? Non-approved AI Systems include AI Systems I have signed up to Non-approved AI Systems should never be used with Deloitte using my Deloitte or personal email address on vendor click confidential information, as they have not undergone rigorous * IMPORTANT * These examples must follow through terms which have not been approved by Deloitte. This security assessments to ensure they meet required security the guidelines and must not involve any client can include both free and paid subscriptions. (e.g. I am wanting to standards. or Deloitte confidential information. use ChatGPT.) Can I use Deloitte non-approved AI Systems to help me answer • Summarising publicly available materials for Are non-approved AI Systems secure enough to use with client legal, regulatory, and similar questions? internal use using Chat GPT. E.g. Prompt - documents? “Please summarise this news article from No, it is essential that questions requiring specialist knowledge are the Wall Street Journal” No, Deloitte non-approved AI Systems have not undergone answered by the appropriate professional. This is due to the • Brainstorming or generating ideas using rigorous security assessments to ensure they meet the required potential significant impact of providing incorrect answers or Miro Assisted AI. E.g. Prompt: - “Brainstorm standards for handling client confidential information securely. acting in reliance of those answers ideas about a AI Induction Lab” • Helping to suggest suitable content for a Do I need to tell anyone I’m using an AI System? E.g., my Do Deloitte non-approved AI Systems process data offshore? news article using Grammarly AI Writing manager? Partner? Client? Assistance. E.g. Prompt: - “Make the tone of In most cases yes, if you have concerns about whether this is this generic news article about climate You must be open and transparent with your engagement team appropriate in the context of how you want to use the AI System month more friendly”. and client about your use of AI Systems.When deciding whether consult with your Quality & Risk team. the client needs to be informed about your use of an AI System, you and your engagement team should consider the client’s expectation, your contract with the client and the extent of your use of AI in your work. Incidental use (such as using a generative AI chatbot to help reword a sentence or for early-stage research) may not need to be disclosed. Can I use Deloitte documents with a Deloitte non-approved AI System? If the Deloitte documents contain Deloitte confidential 31 © 2024 Deloitte Asia Pacific Services Limited AP Generative AI QRM Guidelines 31 | examples of permitted uses of publicly available AI Systems? * These examples must follow nd must not involve any client fidential information. g publicly available materials for using Chat GPT. E.g. Prompt - marise this news article from eet Journal” ng or generating ideas using d AI. E.g. Prompt: - “Brainstorm a AI Induction Lab” uggest suitable content for a using Grammarly AI Writing E.g. Prompt: - “Make the tone of news article about climate e friendly”. |
| --- | --- |

### 表格 2

| What are some non-approved * IMPORTANT the guidelines a or Deloitte con • Summarisin internal use “Please sum the Wall Str • Brainstormi Miro Assiste ideas about • Helping to s news article Assistance. this generic month mor | examples of permitted uses of publicly available AI Systems? * These examples must follow nd must not involve any client fidential information. g publicly available materials for using Chat GPT. E.g. Prompt - marise this news article from eet Journal” ng or generating ideas using d AI. E.g. Prompt: - “Brainstorm a AI Induction Lab” uggest suitable content for a using Grammarly AI Writing E.g. Prompt: - “Make the tone of news article about climate e friendly”. |
| --- | --- |

FAQs for Deloitte Practitioners using Publicly Available AI Systems What is a Deloitte Publicly Available (non-approved) AI System? information (i.e. information about Deloitte’s business that is not What are some examples of permitted uses of public) they cannot be inputted into non-approved AI Systems. non-approved publicly available AI Systems? Non-approved AI Systems include AI Systems I have signed up to Non-approved AI Systems should never be used with Deloitte using my Deloitte or personal email address on vendor click confidential information, as they have not undergone rigorous * IMPORTANT * These examples must follow through terms which have not been approved by Deloitte. This security assessments to ensure they meet required security the guidelines and must not involve any client use ChatGPT.) Can I use Deloitte non-approved AI Systems to help me answer • Summarising publicly available materials for documents? “Please summarise this news article from No, it is essential that questions requiring specialist knowledge are the Wall Street Journal” No, Deloitte non-approved AI Systems have not undergone answered by the appropriate professional. This is due to the • Brainstorming or generating ideas using rigorous security assessments to ensure they meet the required potential significant impact of providing incorrect answers or Miro Assisted AI. E.g. Prompt: - “Brainstorm • Helping to suggest suitable content for a Do I need to tell anyone I’m using an AI System? E.g., my Do Deloitte non-approved AI Systems process data offshore? news article using Grammarly AI Writing manager? Partner? Client? Assistance. E.g. Prompt: - “Make the tone of In most cases yes, if you have concerns about whether this is this generic news article about climate You must be open and transparent with your engagement team appropriate in the context of how you want to use the AI System month more friendly”. and client about your use of AI Systems.When deciding whether consult with your Quality & Risk team. the client needs to be informed about your use of an AI System, you and your engagement team should consider the client’s expectation, your contract with the client and the extent of your use of AI in your work. Incidental use (such as using a generative AI chatbot to help reword a sentence or for early-stage research) may not need to be disclosed. Can I use Deloitte documents with a Deloitte non-approved AI System? 31

## 第 32 頁

### Generative AI Contracting Considerations

## 第 33 頁

### 表格 1

|  | Generative AI Contracting Considerations (1/4) The list below illustrates key client contracting issues and considerations in the context of Generative AI. It is not an exhaustive list and should be supplemented with other materials available to your local firm. Please contact your local OGC team for further guidance. Area Issues/Considerations Description of Issues and Considerations Definition of AI Systems • Consider the breadth of ‘AI Systems’ (or other similar terminology) as defined in the contract. terms • The breadth or narrowness of ‘AI Systems’ (and other similar terms) is important as the restrictions, rights, and obligationsunder the contract all tie back to this definition. Scope of AI systems • There are many back-end systems used at Deloitte that will use AI, some of which might have a tangential connection with client service and primarily have the function of improving productivity at Deloitte generally. • We may not be able to oblige with client-imposed restrictions for these back-end systems given the broad use of these systems bythe network. Customer Data Secondary use of data • Client contracts often restrict the use of any client data to ‘for the purpose of providing the Services’. • To the extent that there are any planned or anticipated secondary uses of client data by third-party vendors, this should be negotiated up front. Permissions / rights to use data • For an AI system, both prompts and raw data may be ingested. • Where the client is the provider of the ‘raw data’ input into the AI system then consider whether the client has the requisite permissions and authorizations to provide such data. Intellectual Ownership of Outputs • Arrangements with Generative AI vendors are a relevant consideration to the ownership of outputs. Property • There may be uncertainty around whether the output can be owned and ownership rights can be asserted against third parties. (see more in the Generative AI IP Ownership of Outputs- • AI Systems may produce outputs that are the same or similar across multiple users. Considerations Disclaimer • Clients should be made aware of the possibility of similar outputs. section) Open-Source Open-source or public content • Open-Source content provided by the client may not be captured by “Client Data” definition and therefore, any representations and Content may be provided by the Client warranties related to “Client Data” may not be sufficiently broad enough to capture the open-source license requirements Open-source content or • Where Deloitte provides open-source content or technology, clients may be sensitive to use of such materials. In addition, for open-source technology may be provided by content and technology, there are attribution requirements and separate terms governing the use of such open-source materials. Deloitte | None | None | None | None | None |
| --- | --- | --- | --- | --- | --- | --- |
| None | None | The list below illustrates key client contracting issues and considerations in the context of Generative AI. It is not an exhaustive list and should be supplemented with other materials available to your local firm. Please contact your local OGC team for further guidance. | None | None | None | None |
| None | None | None | Area | Issues/Considerations | Description of Issues and Considerations | None |
| None | None | None | Definition of terms | AI Systems | • Consider the breadth of ‘AI Systems’ (or other similar terminology) as defined in the contract. • The breadth or narrowness of ‘AI Systems’ (and other similar terms) is important as the restrictions, rights, and obligationsunder the contract all tie back to this definition. | None |
| None | None | None | None | Scope of AI systems | • There are many back-end systems used at Deloitte that will use AI, some of which might have a tangential connection with client service and primarily have the function of improving productivity at Deloitte generally. • We may not be able to oblige with client-imposed restrictions for these back-end systems given the broad use of these systems bythe network. | None |
| None | None | None | Customer Data | Secondary use of data | • Client contracts often restrict the use of any client data to ‘for the purpose of providing the Services’. • To the extent that there are any planned or anticipated secondary uses of client data by third-party vendors, this should be negotiated up front. | None |
| None | None | None | None | Permissions / rights to use data | • For an AI system, both prompts and raw data may be ingested. • Where the client is the provider of the ‘raw data’ input into the AI system then consider whether the client has the requisite permissions and authorizations to provide such data. | None |
| None | None | None | Intellectual Property (see more in the Generative AI IP Considerations section) | Ownership of Outputs | • Arrangements with Generative AI vendors are a relevant consideration to the ownership of outputs. • There may be uncertainty around whether the output can be owned and ownership rights can be asserted against third parties. | None |
| None | None | None | None | Ownership of Outputs- Disclaimer | • AI Systems may produce outputs that are the same or similar across multiple users. • Clients should be made aware of the possibility of similar outputs. | None |
| None | None | None | Open-Source Content | Open-source or public content may be provided by the Client | • Open-Source content provided by the client may not be captured by “Client Data” definition and therefore, any representations and warranties related to “Client Data” may not be sufficiently broad enough to capture the open-source license requirements | None |
| None | None | None | None | Open-source content or technology may be provided by Deloitte | • Where Deloitte provides open-source content or technology, clients may be sensitive to use of such materials. In addition, for open-source content and technology, there are attribution requirements and separate terms governing the use of such open-source materials. | None |
| None | Note: The above considerations are not exhaustive. ©© 22002244 DDeellooiittttee AAssiiaa PPaacciiffiicc SSeerrvviicceess LLiimmiitteedd | None | Note: The above considerations are not exhaustive. | None | None |  |

The list below illustrates key client contracting issues and considerations in the context of Generative AI. It is not an exhaustive list and should be supplemented with other materials available to your local firm. Please contact your local OGC team for further guidance. Area Issues/Considerations Description of Issues and Considerations Definition of AI Systems • Consider the breadth of ‘AI Systems’ (or other similar terminology) as defined in the contract. terms • The breadth or narrowness of ‘AI Systems’ (and other similar terms) is important as the restrictions, rights, and obligationsunder the contract all tie back to this definition. Scope of AI systems • There are many back-end systems used at Deloitte that will use AI, some of which might have a tangential connection with client service and primarily have the function of improving productivity at Deloitte generally. • We may not be able to oblige with client-imposed restrictions for these back-end systems given the broad use of these systems bythe network. Customer Data Secondary use of data • Client contracts often restrict the use of any client data to ‘for the purpose of providing the Services’. • To the extent that there are any planned or anticipated secondary uses of client data by third-party vendors, this should be negotiated up front. Permissions / rights to use data • For an AI system, both prompts and raw data may be ingested. • Where the client is the provider of the ‘raw data’ input into the AI system then consider whether the client has the requisite permissions and authorizations to provide such data. Intellectual Ownership of Outputs • Arrangements with Generative AI vendors are a relevant consideration to the ownership of outputs. Property • There may be uncertainty around whether the output can be owned and ownership rights can be asserted against third parties. (see more in the Generative AI IP Ownership of Outputs- • AI Systems may produce outputs that are the same or similar across multiple users. Considerations Disclaimer • Clients should be made aware of the possibility of similar outputs. section) Open-Source Open-source or public content • Open-Source content provided by the client may not be captured by “Client Data” definition and therefore, any representations and Content may be provided by the Client warranties related to “Client Data” may not be sufficiently broad enough to capture the open-source license requirements Open-source content or • Where Deloitte provides open-source content or technology, clients may be sensitive to use of such materials. In addition, for open-source technology may be provided by content and technology, there are attribution requirements and separate terms governing the use of such open-source materials. Deloitte Note: The above considerations are not exhaustive.

## 第 34 頁

### 表格 1

|  | Generative AI Contracting Considerations (2/4) Area Issues/Considerations Description of Issues and Considerations Warranties and Input • For an AI system, both prompts and raw data may be ingested. Indemnities • Consider who is the owner of the ‘raw data’ input into the AI system and the nature of the engagement. • Consider who is the owner of the ‘prompt’ to the AI system. • Consider Deloitte’s role in the development of the prompt and the value in the prompt. • Where using licensed ‘raw data’ provided by a third-party, consideration should be given to any applicable contractual restrictions Outputs –Disclaimer of Accuracy • Incomplete or inaccurate prompts may affect the accuracy and completeness of the output. • AI Systems are subject to hallucinations, which can be caused by a variety of factors outside of Deloitte’s control. Outputs –Deloitte Obligations - • Typically, vendors do not offer any representation or warranty around the output. Disclaimer for Use of Outputs Outputs -Disclaimers for end-users • Consider whether there is a disclaimer that should be included on the Outputs for the attention of the end-user (either as pass through requirement or a regulatory requirement) Outputs –Client Obligations -Client • As outputs produced by an AI System may incorporate components of a third party’s intellectual property, there is a risk thatthe outputs use of outputs produced could infringe third-party intellectual property rights. In addition, a client may use an output produced by an AI system in a way that is misleading or otherwise has an adverse impact on individuals. Client Certain regulations have • Where an output produced by an AI system is used outside of the geography in which it is produced, foreign regulations may bringthe AI Obligations extraterritorial application system that produced the output within the scope of such regulations. The use of the output may not have been contemplated by the provider/developer of the relevant AI system. Outputs produced by an AI system • The LLM and associated data underpinning the operation of an AI System may be valuable intellectual property of a developer/provider. may be used to extract info from the There are certain methods that can be utilized to extract information from an AI System that can then be used to effectively reconstruct underlying model the LLM. Deloitte deliverables could be used • Clients may wish to use Deloitte deliverables to train their own large language models which compete with Deloitte’s offerings or produce by a client as part of training their outputs that are based on Deloitte’s deliverable but do not reflect the substance of the deliverable or may infringe Deloitte’s intellectual own large language model property rights in the deliverable. Current and potential regulations • There are current and potential regulations (e.g. EU AI Act) that have transparency requirements where AI is used so that endusers are impose notice requirements aware that outputs produced by an AI system were generated using AI. pertaining to AI systems The underlying training data of an AI • Deloitte, in its advisory role, cannot necessarily assess the accuracy, completeness, and appropriateness of training data tothe same System can influence the outputs degree as a client can with respect to its own business needs. | None | None | None | None |
| --- | --- | --- | --- | --- | --- |
| None | None | Area | Issues/Considerations | Description of Issues and Considerations | None |
| None | None | Warranties and Indemnities | Input | • For an AI system, both prompts and raw data may be ingested. • Consider who is the owner of the ‘raw data’ input into the AI system and the nature of the engagement. • Consider who is the owner of the ‘prompt’ to the AI system. • Consider Deloitte’s role in the development of the prompt and the value in the prompt. • Where using licensed ‘raw data’ provided by a third-party, consideration should be given to any applicable contractual restrictions | None |
| None | None | None | Outputs –Disclaimer of Accuracy | • Incomplete or inaccurate prompts may affect the accuracy and completeness of the output. • AI Systems are subject to hallucinations, which can be caused by a variety of factors outside of Deloitte’s control. | None |
| None | None | None | Outputs –Deloitte Obligations - Disclaimer for Use of Outputs | • Typically, vendors do not offer any representation or warranty around the output. | None |
| None | None | None | Outputs -Disclaimers for end-users | • Consider whether there is a disclaimer that should be included on the Outputs for the attention of the end-user (either as pass through requirement or a regulatory requirement) | None |
| None | None | None | Outputs –Client Obligations -Client use of outputs | • As outputs produced by an AI System may incorporate components of a third party’s intellectual property, there is a risk thatthe outputs produced could infringe third-party intellectual property rights. In addition, a client may use an output produced by an AI system in a way that is misleading or otherwise has an adverse impact on individuals. | None |
| None | None | Client Obligations | Certain regulations have extraterritorial application | • Where an output produced by an AI system is used outside of the geography in which it is produced, foreign regulations may bringthe AI system that produced the output within the scope of such regulations. The use of the output may not have been contemplated by the provider/developer of the relevant AI system. | None |
| None | None | None | Outputs produced by an AI system may be used to extract info from the underlying model | • The LLM and associated data underpinning the operation of an AI System may be valuable intellectual property of a developer/provider. There are certain methods that can be utilized to extract information from an AI System that can then be used to effectively reconstruct the LLM. | None |
| None | None | None | Deloitte deliverables could be used by a client as part of training their own large language model | • Clients may wish to use Deloitte deliverables to train their own large language models which compete with Deloitte’s offerings or produce outputs that are based on Deloitte’s deliverable but do not reflect the substance of the deliverable or may infringe Deloitte’s intellectual property rights in the deliverable. | None |
| None | None | None | Current and potential regulations impose notice requirements pertaining to AI systems | • There are current and potential regulations (e.g. EU AI Act) that have transparency requirements where AI is used so that endusers are aware that outputs produced by an AI system were generated using AI. | None |
| None | None | None | The underlying training data of an AI System can influence the outputs | • Deloitte, in its advisory role, cannot necessarily assess the accuracy, completeness, and appropriateness of training data tothe same degree as a client can with respect to its own business needs. | None |
| None | Note: The above considerations are not exhaustive. ©© 22002244 DDeellooiittttee AAssiiaa PPaacciiffiicc SSeerrvviicceess LLiimmiitteedd |  |  |  |  |
| None | None | Note: The above considerations are not exhaustive. | None | None |  |

Area Issues/Considerations Description of Issues and Considerations Warranties and Input • For an AI system, both prompts and raw data may be ingested. Indemnities • Consider who is the owner of the ‘raw data’ input into the AI system and the nature of the engagement. • Consider who is the owner of the ‘prompt’ to the AI system. • Consider Deloitte’s role in the development of the prompt and the value in the prompt. • Where using licensed ‘raw data’ provided by a third-party, consideration should be given to any applicable contractual restrictions Outputs –Disclaimer of Accuracy • Incomplete or inaccurate prompts may affect the accuracy and completeness of the output. • AI Systems are subject to hallucinations, which can be caused by a variety of factors outside of Deloitte’s control. Outputs –Deloitte Obligations - • Typically, vendors do not offer any representation or warranty around the output. Disclaimer for Use of Outputs Outputs -Disclaimers for end-users • Consider whether there is a disclaimer that should be included on the Outputs for the attention of the end-user (either as pass through requirement or a regulatory requirement) Outputs –Client Obligations -Client • As outputs produced by an AI System may incorporate components of a third party’s intellectual property, there is a risk thatthe outputs use of outputs produced could infringe third-party intellectual property rights. In addition, a client may use an output produced by an AI system in a way that is misleading or otherwise has an adverse impact on individuals. Client Certain regulations have • Where an output produced by an AI system is used outside of the geography in which it is produced, foreign regulations may bringthe AI Obligations extraterritorial application system that produced the output within the scope of such regulations. The use of the output may not have been contemplated by the provider/developer of the relevant AI system. Outputs produced by an AI system • The LLM and associated data underpinning the operation of an AI System may be valuable intellectual property of a developer/provider. may be used to extract info from the There are certain methods that can be utilized to extract information from an AI System that can then be used to effectively reconstruct underlying model the LLM. Deloitte deliverables could be used • Clients may wish to use Deloitte deliverables to train their own large language models which compete with Deloitte’s offerings or produce by a client as part of training their outputs that are based on Deloitte’s deliverable but do not reflect the substance of the deliverable or may infringe Deloitte’s intellectual own large language model property rights in the deliverable. Current and potential regulations • There are current and potential regulations (e.g. EU AI Act) that have transparency requirements where AI is used so that endusers are impose notice requirements aware that outputs produced by an AI system were generated using AI. pertaining to AI systems The underlying training data of an AI • Deloitte, in its advisory role, cannot necessarily assess the accuracy, completeness, and appropriateness of training data tothe same System can influence the outputs degree as a client can with respect to its own business needs. Note: The above considerations are not exhaustive.

## 第 35 頁

### 表格 1

|  | Generative AI Contracting Considerations (3/4) Area Issues/Considerations Description of Issues and Considerations Fees Client’s may expect that • Given that AI can improve productivity on client service engagements, and could therefore reduce costs, clients may expect that cost savings are Deloitte passes through any passed through to them. cost savings attributable to use of AI Commercials Certain AI systems have • Certain AI Systems have consumption costs associated with their use (e.g. token costs) which, depending on the volume of databeing processed, consumption costs associated could become material quite quickly and impact the profitability of the engagement. Whether or not consumption costs should be passed through with their use to clients will necessarily depend on whether the anticipated consumption costs have already been built into the rates (e.g. productivity tools that form part of Deloitte’s general infrastructure and overhead costs), whether there has been a specific procurement for the clientengagement (at client’s request) or in connection with a specific offering, and the extent to which the AI tool is used. Privacy Compliance with data • The underlying client contract may have been prepared at a time where use of Generative AI was not contemplated and thereforethe contractual protection requirements provisions with respect to data protection may need to be revised to reflect the scope of the engagement. Security Data residency • In certain cases, it may be necessary to host or process data in specific countries or territories when using AI Systems. Forexample, there may be an internal operational requirement (e.g., architecture) or a client request for data to be hosted in the same location whereitis collected. Vendor Contracts Limitation of The limitation of liability in the Liability vendor contract may differ from • Where third-party AI systems are used, vendors are likely to limit their liability to Deloitte. Where there is a claim from a client in relation to the AI the client’s expectation system, or its outputs, Deloitte may be responsible for any difference between the limitation of liability in the vendor contract and the limitation of regarding Deloitte’s liability liability in the client contract. AI Systems are still relatively Uncertain state of AI new and their limitations are • AI technology is a new and evolving area. Accordingly, all of the risks in relation to the provision of AI Systems and related services are not yet still be identified known or fully understood. In certain jurisdictions, given the uncertain state of AI, there is a risk that the contract, or parts thereof, could fail its essential purpose. Foreign laws may regulate the use and development of AI Foreign laws systems • There are some laws which have extraterritorial application where there is a nexus with their territory. In addition, many of the regulatory regimes are new and therefore, their application and the associated obligations that Deloitte may be required to comply with uncertain. Where these regimes are applicable, MFs and/or clients could be subject to claims from foreign regulators who may interpret or apply the laws in an unexpected way. | None | None | None | None |
| --- | --- | --- | --- | --- | --- |
| None | None | Area | Issues/Considerations | Description of Issues and Considerations | None |
| None | None | Fees | Client’s may expect that Deloitte passes through any cost savings attributable to use of AI | • Given that AI can improve productivity on client service engagements, and could therefore reduce costs, clients may expect that cost savings are passed through to them. | None |
| None | None | Commercials | Certain AI systems have consumption costs associated with their use | • Certain AI Systems have consumption costs associated with their use (e.g. token costs) which, depending on the volume of databeing processed, could become material quite quickly and impact the profitability of the engagement. Whether or not consumption costs should be passed through to clients will necessarily depend on whether the anticipated consumption costs have already been built into the rates (e.g. productivity tools that form part of Deloitte’s general infrastructure and overhead costs), whether there has been a specific procurement for the clientengagement (at client’s request) or in connection with a specific offering, and the extent to which the AI tool is used. | None |
| None | None | Privacy | Compliance with data protection requirements | • The underlying client contract may have been prepared at a time where use of Generative AI was not contemplated and thereforethe contractual provisions with respect to data protection may need to be revised to reflect the scope of the engagement. | None |
| None | None | Security | Data residency | • In certain cases, it may be necessary to host or process data in specific countries or territories when using AI Systems. Forexample, there may be an internal operational requirement (e.g., architecture) or a client request for data to be hosted in the same location whereitis collected. | None |
| None | None | Limitation of Liability | The limitation of liability in the vendor contract may differ from the client’s expectation regarding Deloitte’s liability AI Systems are still relatively new and their limitations are still be identified Foreign laws may regulate the use and development of AI systems | Vendor Contracts • Where third-party AI systems are used, vendors are likely to limit their liability to Deloitte. Where there is a claim from a client in relation to the AI system, or its outputs, Deloitte may be responsible for any difference between the limitation of liability in the vendor contract and the limitation of liability in the client contract. Uncertain state of AI • AI technology is a new and evolving area. Accordingly, all of the risks in relation to the provision of AI Systems and related services are not yet known or fully understood. In certain jurisdictions, given the uncertain state of AI, there is a risk that the contract, or parts thereof, could fail its essential purpose. Foreign laws • There are some laws which have extraterritorial application where there is a nexus with their territory. In addition, many of the regulatory regimes are new and therefore, their application and the associated obligations that Deloitte may be required to comply with uncertain. Where these regimes are applicable, MFs and/or clients could be subject to claims from foreign regulators who may interpret or apply the laws in an unexpected way. | None |
| None | Note: The above considerations are not exhaustive. ©© 22002244 DDeellooiittttee AAssiiaa PPaacciiffiicc SSeerrvviicceess LLiimmiitteedd | Note: The above considerations are not exhaustive. | None | None |  |

Area Issues/Considerations Description of Issues and Considerations Fees Client’s may expect that • Given that AI can improve productivity on client service engagements, and could therefore reduce costs, clients may expect that cost savings are Deloitte passes through any passed through to them. cost savings attributable to use of AI Commercials Certain AI systems have • Certain AI Systems have consumption costs associated with their use (e.g. token costs) which, depending on the volume of databeing processed, consumption costs associated could become material quite quickly and impact the profitability of the engagement. Whether or not consumption costs should be passed through with their use to clients will necessarily depend on whether the anticipated consumption costs have already been built into the rates (e.g. productivity tools that form part of Deloitte’s general infrastructure and overhead costs), whether there has been a specific procurement for the clientengagement (at client’s request) or in connection with a specific offering, and the extent to which the AI tool is used. Privacy Compliance with data • The underlying client contract may have been prepared at a time where use of Generative AI was not contemplated and thereforethe contractual protection requirements provisions with respect to data protection may need to be revised to reflect the scope of the engagement. Security Data residency • In certain cases, it may be necessary to host or process data in specific countries or territories when using AI Systems. Forexample, there may be an internal operational requirement (e.g., architecture) or a client request for data to be hosted in the same location whereitis collected. Vendor Contracts Limitation of The limitation of liability in the Liability vendor contract may differ from • Where third-party AI systems are used, vendors are likely to limit their liability to Deloitte. Where there is a claim from a client in relation to the AI the client’s expectation system, or its outputs, Deloitte may be responsible for any difference between the limitation of liability in the vendor contract and the limitation of regarding Deloitte’s liability liability in the client contract. AI Systems are still relatively Uncertain state of AI new and their limitations are • AI technology is a new and evolving area. Accordingly, all of the risks in relation to the provision of AI Systems and related services are not yet still be identified known or fully understood. In certain jurisdictions, given the uncertain state of AI, there is a risk that the contract, or parts thereof, could fail its essential purpose. Foreign laws may regulate the use and development of AI Foreign laws systems • There are some laws which have extraterritorial application where there is a nexus with their territory. In addition, many of the regulatory regimes are new and therefore, their application and the associated obligations that Deloitte may be required to comply with uncertain. Where these regimes are applicable, MFs and/or clients could be subject to claims from foreign regulators who may interpret or apply the laws in an unexpected way. Note: The above considerations are not exhaustive.

## 第 36 頁

### 表格 1

|  | Generative AI Contracting Considerations (4/4) Area Issues/Considerations Description of Issues and Considerations Compliance with Changes in regulations or laws may • Given that the regulation of AI systems is evolving across the world, there may be regulations that did not exist at the timeofexecuting laws and affect Deloitte’s ability to provide the contract or may be modified after the execution of the contract. These new or modified regulations could impact the delivery of regulations the Services services or the provision of the AI system and may necessitate adjustment to the rights and obligations of the parties. Third Party Third party terms may be applicable • Where the Client and/or its end-users interact with the AI System used as part of the client engagement, terms of use or a EULA may Products to any use of the AI System by the need to be passed through to the Client and/or its end-users. This requirement may be prescribed by the third-party vendor, to assist Client Deloitte in complying with its own regulatory obligations, or to ensure that the Client’s access to and/or use of the AI System does not result in Deloitte breaching its own contractual obligations to the third-party vendor. The Client may require Deloitte to • As part of an advisory engagement, the Client may request that Deloitte access and/or use their own separately licensed instanceof an AI access their instance of an AI System System. Trade Controls Provision of AI Systems and related • Various governments have expressed concern about the development of AI Systems and its impact on their impact on their national and Sanctions services and release of export- security. These national security concerns may lead to the issuance of sanctions and export controls on generative AI Systemsand other controlled technology and technical related activities in the next few years. data to prohibited destinations, end • Deloitte firms are required to comply with applicable sanctions and export controls (“trade controls”), and Deloitte Global has issued users, or end uses guidance to the network that Deloitte firms should not use Deloitte Global or Deloitte US technology in any US-sanctioned activities. Deloitte Global has also issued other guidance concerning compliance with specific trade controls, such as Russia-related trade controls. • In addition, use of AI or other technology in a manner than contravenes applicable trade controls could create legal exposurefor Deloitte firms. Compliance with Clients may require Deloitte’s use of • Given that AI is a new and emerging area, and given the regulation of AI is still evolving, clients may seek to mitigate their risk by Policies AI complies with certain policies requiring Deloitte to comply with their own policies with respect to the use of AI on an engagement. However, where using third-party AI which may be onerous or not systems, Deloitte’s ability to ensure that such systems are compliant with client policies may be limited because: (1) the vendor contract possible due to the use of third- has not passed through the obligation to comply with such policies; (2) the use of the AI system spans multiple client engagements; (3) party systems vendors are likely to be unwilling to provide such assurances; and (4) vendors are likely to be unwilling to modify their products to meet Deloitte’s (and its clients) specific requirements. General Force majeure clauses typically do • The regulatory landscape relating to the use of AI is evolving, and AI systems may be unstable and produce hallucinations or other errors. Provisions not include change of laws or Where there are significant problems with an AI system, Deloitte may wish to cease providing the services as remedying such problems induced infringement if contract could be costly and time consuming or may be difficult to identify the source of the instability. requires performance despite IP risk. • In addition, given the changing landscape regarding IP, new case law may restrict Deloitte’s ability to deliver services withoutbeing AI systems may be unstable given subject to IP infringement claims. that the technology is new and evolving. | None | None | None | None |
| --- | --- | --- | --- | --- | --- |
| None | None | Area | Issues/Considerations | Description of Issues and Considerations | None |
| None | None | Compliance with laws and regulations | Changes in regulations or laws may affect Deloitte’s ability to provide the Services | • Given that the regulation of AI systems is evolving across the world, there may be regulations that did not exist at the timeofexecuting the contract or may be modified after the execution of the contract. These new or modified regulations could impact the delivery of services or the provision of the AI system and may necessitate adjustment to the rights and obligations of the parties. | None |
| None | None | Third Party Products | Third party terms may be applicable to any use of the AI System by the Client | • Where the Client and/or its end-users interact with the AI System used as part of the client engagement, terms of use or a EULA may need to be passed through to the Client and/or its end-users. This requirement may be prescribed by the third-party vendor, to assist Deloitte in complying with its own regulatory obligations, or to ensure that the Client’s access to and/or use of the AI System does not result in Deloitte breaching its own contractual obligations to the third-party vendor. | None |
| None | None | None | The Client may require Deloitte to access their instance of an AI System | • As part of an advisory engagement, the Client may request that Deloitte access and/or use their own separately licensed instanceof an AI System. | None |
| None | None | Trade Controls and Sanctions | Provision of AI Systems and related services and release of export- controlled technology and technical data to prohibited destinations, end users, or end uses | • Various governments have expressed concern about the development of AI Systems and its impact on their impact on their national security. These national security concerns may lead to the issuance of sanctions and export controls on generative AI Systemsand other related activities in the next few years. • Deloitte firms are required to comply with applicable sanctions and export controls (“trade controls”), and Deloitte Global has issued guidance to the network that Deloitte firms should not use Deloitte Global or Deloitte US technology in any US-sanctioned activities. Deloitte Global has also issued other guidance concerning compliance with specific trade controls, such as Russia-related trade controls. • In addition, use of AI or other technology in a manner than contravenes applicable trade controls could create legal exposurefor Deloitte firms. | None |
| None | None | Compliance with Policies | Clients may require Deloitte’s use of AI complies with certain policies which may be onerous or not possible due to the use of third- party systems | • Given that AI is a new and emerging area, and given the regulation of AI is still evolving, clients may seek to mitigate their risk by requiring Deloitte to comply with their own policies with respect to the use of AI on an engagement. However, where using third-party AI systems, Deloitte’s ability to ensure that such systems are compliant with client policies may be limited because: (1) the vendor contract has not passed through the obligation to comply with such policies; (2) the use of the AI system spans multiple client engagements; (3) vendors are likely to be unwilling to provide such assurances; and (4) vendors are likely to be unwilling to modify their products to meet Deloitte’s (and its clients) specific requirements. | None |
| None | None | General Provisions | Force majeure clauses typically do not include change of laws or induced infringement if contract requires performance despite IP risk. AI systems may be unstable given that the technology is new and evolving. | • The regulatory landscape relating to the use of AI is evolving, and AI systems may be unstable and produce hallucinations or other errors. Where there are significant problems with an AI system, Deloitte may wish to cease providing the services as remedying such problems could be costly and time consuming or may be difficult to identify the source of the instability. • In addition, given the changing landscape regarding IP, new case law may restrict Deloitte’s ability to deliver services withoutbeing subject to IP infringement claims. | None |
| None | Note: The above considerations are not exhaustive. ©© 22002244 DDeellooiittttee AAssiiaa PPaacciiffiicc SSeerrvviicceess LLiimmiitteedd |  |  |  |  |
| None | None | Note: The above considerations are not exhaustive. | None | None |  |

Area Issues/Considerations Description of Issues and Considerations Compliance with Changes in regulations or laws may • Given that the regulation of AI systems is evolving across the world, there may be regulations that did not exist at the timeofexecuting laws and affect Deloitte’s ability to provide the contract or may be modified after the execution of the contract. These new or modified regulations could impact the delivery of regulations the Services services or the provision of the AI system and may necessitate adjustment to the rights and obligations of the parties. Third Party Third party terms may be applicable • Where the Client and/or its end-users interact with the AI System used as part of the client engagement, terms of use or a EULA may Products to any use of the AI System by the need to be passed through to the Client and/or its end-users. This requirement may be prescribed by the third-party vendor, to assist Client Deloitte in complying with its own regulatory obligations, or to ensure that the Client’s access to and/or use of the AI System does not result in Deloitte breaching its own contractual obligations to the third-party vendor. The Client may require Deloitte to • As part of an advisory engagement, the Client may request that Deloitte access and/or use their own separately licensed instanceof an AI access their instance of an AI System System. Trade Controls Provision of AI Systems and related • Various governments have expressed concern about the development of AI Systems and its impact on their impact on their national and Sanctions services and release of export- security. These national security concerns may lead to the issuance of sanctions and export controls on generative AI Systemsand other controlled technology and technical related activities in the next few years. data to prohibited destinations, end • Deloitte firms are required to comply with applicable sanctions and export controls (“trade controls”), and Deloitte Global has issued users, or end uses guidance to the network that Deloitte firms should not use Deloitte Global or Deloitte US technology in any US-sanctioned activities. Deloitte Global has also issued other guidance concerning compliance with specific trade controls, such as Russia-related trade controls. • In addition, use of AI or other technology in a manner than contravenes applicable trade controls could create legal exposurefor Deloitte firms. Compliance with Clients may require Deloitte’s use of • Given that AI is a new and emerging area, and given the regulation of AI is still evolving, clients may seek to mitigate their risk by Policies AI complies with certain policies requiring Deloitte to comply with their own policies with respect to the use of AI on an engagement. However, where using third-party AI which may be onerous or not systems, Deloitte’s ability to ensure that such systems are compliant with client policies may be limited because: (1) the vendor contract possible due to the use of third- has not passed through the obligation to comply with such policies; (2) the use of the AI system spans multiple client engagements; (3) party systems vendors are likely to be unwilling to provide such assurances; and (4) vendors are likely to be unwilling to modify their products to meet Deloitte’s (and its clients) specific requirements. General Force majeure clauses typically do • The regulatory landscape relating to the use of AI is evolving, and AI systems may be unstable and produce hallucinations or other errors. Provisions not include change of laws or Where there are significant problems with an AI system, Deloitte may wish to cease providing the services as remedying such problems induced infringement if contract could be costly and time consuming or may be difficult to identify the source of the instability. requires performance despite IP risk. • In addition, given the changing landscape regarding IP, new case law may restrict Deloitte’s ability to deliver services withoutbeing AI systems may be unstable given subject to IP infringement claims. that the technology is new and evolving. Note: The above considerations are not exhaustive.

## 第 37 頁

### Generative AI IP Considerations

## 第 38 頁

### 表格 1

| Generative AI Intellectual Property Considerations Intellectual property is an area of law that wasn’t designed with Generative AI in mind, and it is going to take a while for the law to catch up. In the meantime, we need to be mindful of these uncertainties when we make decisions. Question Answer QRM Considerations What are the IP considerations • It is important that we ensure that we have the rights to use the data in connection • What is the source of the data that Deloitte wants to use to input when fine-tuning or providing with training or prompting generative AI. This applies even where the information is into or use to finetune the generative AI model and what context to a generative AI model? publicly available. Make sure you have read any license terms that attach to the data restrictions (if any) are placed on the use of the data? Are the before using it in connection with generative AI. restrictions consistent with the use of the data for this purpose? • See Generative AI Data Guardrails for more information. Are the outputs of generative AI • Most generative AI products assign ownership of any IP in the outputs to the end user • What do the generative AI product’s terms of use / license terms protected by Copyright? (the person inputting the prompt). say about who owns the copyright in any outputs generated? • Under current copyright law, a ’work’ (e.g., an image, a piece of writing or software • Are we using generative AI to create an output in which it is code) that does not have a human author is notprotected by copyright. And also, important that either Deloitte or the client owns the IP (e.g., usually there is a term of use / license term related to intellectual properties stated by imagery to be used in connection with creating a brand or visual the AI services providers that the outputs/deliverables produced by the AI tool belong identity)? to the AI services providers (“Protective Clause”). • This means that in order to be protected by copyright we must be able to show that there was a sufficient amount of human effort involved in the creation of the output and without the Protective Clause embedded in the use of that tool. Do the outputs of generative AI • Generative AI tools use complex probability calculations to generate ‘original’ content. • Have we sufficiently modified or added our own ‘intellectual models infringe third party While these tools are not designed to copy the materials on which they were trained, effort’ to the output to minimize the risk of any third-party IP copyright or other IP rights? there is still a chance that all or parts of a copyright work might be reproduced. infringement? • Most generative-AI platforms do not guarantee the accuracy, integrity or quality of the • Are we using prompts that increase the likelihood that the AI generated content and generally do not provide any warranty that the content will not model will reproduce elements of existing works (for example, infringe third party intellectual property rights. ‘create an image in the style of [insert existing artist]’)? • Some providers of generative AI tools (such as GitHub Copilot) have developed filters • Have we taken reasonable steps to verify the source of the which detects code suggestions that reproduce existing content in the training dataset. information or assess whether the output reproduces existing works (e.g., scanning code to identify known license terms that attach to the code)? © 2024 Deloitte Asia Pacific Services Limited AP Generative AI QRM Guidelines 38 | ns h Generative AI in mind, and it is going to take a while for the law to ertainties when we make decisions. QRM Considerations |
| --- | --- |

### 表格 2

| Intellectual property is an area of law that wasn’t designed wit catch up. In the meantime, we need to be mindful of these unc | None | h Generative AI in mind, and it is going to take a while for the law to ertainties when we make decisions. | None |
| --- | --- | --- | --- |
| Question | Answer |  | QRM Considerations |

Generative AI Intellectual Property Considerations Intellectual property is an area of law that wasn’t designed with Generative AI in mind, and it is going to take a while for the law to catch up. In the meantime, we need to be mindful of these uncertainties when we make decisions. Question Answer QRM Considerations What are the IP considerations • It is important that we ensure that we have the rights to use the data in connection • What is the source of the data that Deloitte wants to use to input when fine-tuning or providing with training or prompting generative AI. This applies even where the information is into or use to finetune the generative AI model and what context to a generative AI model? publicly available. Make sure you have read any license terms that attach to the data restrictions (if any) are placed on the use of the data? Are the before using it in connection with generative AI. restrictions consistent with the use of the data for this purpose? • See Generative AI Data Guardrails for more information. Are the outputs of generative AI • Most generative AI products assign ownership of any IP in the outputs to the end user • What do the generative AI product’s terms of use / license terms protected by Copyright? (the person inputting the prompt). say about who owns the copyright in any outputs generated? • Under current copyright law, a ’work’ (e.g., an image, a piece of writing or software • Are we using generative AI to create an output in which it is code) that does not have a human author is notprotected by copyright. And also, important that either Deloitte or the client owns the IP (e.g., usually there is a term of use / license term related to intellectual properties stated by imagery to be used in connection with creating a brand or visual the AI services providers that the outputs/deliverables produced by the AI tool belong identity)? to the AI services providers (“Protective Clause”). • This means that in order to be protected by copyright we must be able to show that there was a sufficient amount of human effort involved in the creation of the output and without the Protective Clause embedded in the use of that tool. Do the outputs of generative AI • Generative AI tools use complex probability calculations to generate ‘original’ content. • Have we sufficiently modified or added our own ‘intellectual models infringe third party While these tools are not designed to copy the materials on which they were trained, effort’ to the output to minimize the risk of any third-party IP copyright or other IP rights? there is still a chance that all or parts of a copyright work might be reproduced. infringement? • Most generative-AI platforms do not guarantee the accuracy, integrity or quality of the • Are we using prompts that increase the likelihood that the AI generated content and generally do not provide any warranty that the content will not model will reproduce elements of existing works (for example, infringe third party intellectual property rights. ‘create an image in the style of [insert existing artist]’)? • Some providers of generative AI tools (such as GitHub Copilot) have developed filters • Have we taken reasonable steps to verify the source of the which detects code suggestions that reproduce existing content in the training dataset. information or assess whether the output reproduces existing works (e.g., scanning code to identify known license terms that attach to the code)?

## 第 39 頁

### Generative AI Independence Considerations

## 第 40 頁

### 表格 1

|  | Generative AI Independence Considerations Common independence considerations for Generative AI activities or services include: • Contracting and use of external data sets • Co-developed solutions or creating commercial solutions for clients • Alliances and marketplace business relationships within ecosystems • Permissible interactions with restricted entity vendors (e.g. Microsoft and Open AI) • Developing and licensing assets • Conducting Deloitte Greenhouse labs • Services to restricted entities The Global Generative AI Independence Considerations could be found here. Independence guidance already available for AI: OpenAI/ChatGPT FAQs* *A global version of this Microsoft/OpenAI FAQs can be accessed here | None |
| --- | --- | --- |
| None | ©© 22002244 DDeellooiittttee AAssiiaa PPaacciiffiicc SSeerrvviicceess LLiimmiitteedd | AP Generative AI QRM Guidelines 40 |

Generative AI Independence Considerations Common independence considerations for Generative AI activities or services include: • Contracting and use of external data sets • Co-developed solutions or creating commercial solutions for clients • Alliances and marketplace business relationships within ecosystems • Permissible interactions with restricted entity vendors (e.g. Microsoft and Open AI) • Developing and licensing assets • Conducting Deloitte Greenhouse labs • Services to restricted entities The Global Generative AI Independence Considerations could be found here. Independence guidance already available for AI: OpenAI/ChatGPT FAQs* *A global version of this Microsoft/OpenAI FAQs can be accessed here

## 第 41 頁

### Deloitte’s Trustworthy AI Framework Guidance

## 第 42 頁

### 表格 1

|  | Applying the Trustworthy AI Framework to Generative AI use cases The following slides explore the seven dimensions of the Trustworthy AI Framework in practice. Use these slides to explore how to use the framework at work and in Generative AI use cases. Each following slide is aligned to one of the seven Trustworthy AI dimensions and explores: ILLUSTRATIVE SCENARIO TRUSTWORTHY AI CONSIDERATIONS On each slide, there is an example of a use case scenario, On each slide, there is a series of Trustworthy AI considerations mapped to one of the different TAI dimensions. These have been relevant to each use case scenario such as goals/benefits, crafted to enhance comprehension of the framework based on a affected stakeholders, a selection of TAI questions* relevant to real-world scenario. Please note that each use case has one TAI the selected TAI dimension to consider and mitigation strategies. dimension explored against it, but in reality, many dimensions These considerations are meant to foster a deeper understanding will apply to the respective scenarios, and all should be of the Trustworthy AI dimensions. explored in practice. * Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To see a more comprehensive TAI question guide that can be used as an additional resource, please click here for Deloitte Trustworthy AI Questions Guide. Trustworthy AI considerations should be integrated throughout the lifecycle of AI use cases, both internally and in market-facing technology. Please note, that these considerations serve as guidance and are not exhaustive. They should be viewed as a starting point to align Generative AI use cases with ethical, legal/regulatory and trustworthy principles for the responsible use of AI. | None |
| --- | --- | --- |
| None | ©© 22002244 DDeellooiittttee AAssiiaa PPaacciiffiicc SSeerrvviicceess LLiimmiitteedd | AP Generative AI QRM Guidelines 42 |

### 表格 2

|  |
| --- |
| ILLUSTRATIVE SCENARIO |

### 表格 3

|  |
| --- |
| TRUSTWORTHY AI CONSIDERATIONS |

Applying the Trustworthy AI Framework to Generative AI use cases The following slides explore the seven dimensions of the Trustworthy AI Framework in practice. Use these slides to explore how to use the framework at work and in Generative AI use cases. Each following slide is aligned to one of the seven Trustworthy AI dimensions and explores: ILLUSTRATIVE SCENARIO TRUSTWORTHY AI CONSIDERATIONS On each slide, there is an example of a use case scenario, On each slide, there is a series of Trustworthy AI considerations mapped to one of the different TAI dimensions. These have been relevant to each use case scenario such as goals/benefits, crafted to enhance comprehension of the framework based on a affected stakeholders, a selection of TAI questions* relevant to real-world scenario. Please note that each use case has one TAI the selected TAI dimension to consider and mitigation strategies. dimension explored against it, but in reality, many dimensions These considerations are meant to foster a deeper understanding will apply to the respective scenarios, and all should be of the Trustworthy AI dimensions. explored in practice. * Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To see a more comprehensive TAI question guide that can be used as an additional resource, please click here for Deloitte Trustworthy AI Questions Guide. Trustworthy AI considerations should be integrated throughout the lifecycle of AI use cases, both internally and in market-facing technology. Please note, that these considerations serve as guidance and are not exhaustive. They should be viewed as a starting point to align Generative AI use cases with ethical, legal/regulatory and trustworthy principles for the responsible use of AI.

## 第 43 頁

### 表格 1

|  |
| --- |
| TRUSTWORTHY AI CONSIDERATIONS |

### 表格 2

| “PRIVATE" TAI QUESTIONS* TO CONSIDER INCLUDE | None |
| --- | --- |
| AUTONOMOUS | DISCRETIONAL |
| • Could the use (or misuse) of the technology shape or influence human behavior? | • Is the use of personal information, in both training and live data, compatible with the original purpose for which it was collected/obtained? |
| CONFIDENTIAL | CONSENSUAL |
| • What measures have been taken to help ensure personal and/or confidential data is safely and appropriately stored, transferred, and used? Are these measures aligned with regulatory andcontractual obligations? | • Have you consulted with your Deloitte firm Data Privacy team and adhered to privacy and confidentiality guidance? |

SEEING IS BELIEVING PRIVATE (Virtual Try-On Application) A major clothing retailer is getting ready to launch a new mobile app which allows customers to see a digital rendering of their clothes and other products on their own bodies, in their homes, and elsewhere to help with online shopping. By analyzing images or videos of the customer, the app can create realistic representations of how the clothing or product would look in the real world. Additionally, by considering factors such as body shape, skin tone, and personal style, the app can suggest suitable products that align with the customer’s preferences. The app will allow users to create a custom profile with their preferences built in, and it will also allow customers to make in app purchases from the online store. TRUSTWORTHY AI CONSIDERATIONS GOALS / BENEFITS “PRIVATE" TAI QUESTIONS* TO CONSIDER INCLUDE • Visualize products in real-world settings AUTONOMOUS DISCRETIONAL • Provide personalized recommendations • Could the use (or misuse) of the technology shape or influence human • Is the use of personal information, in both training and live data, behavior? compatible with the original purpose for which it was • Increase customer engagement and satisfaction improving its collected/obtained? shopping experience • Reduce product returns • What measures have been taken to help ensure personal and/or • Have you consulted with your Deloitte firm Data Privacy team and used? Are these measures aligned with regulatory andcontractual obligations? AFFECTED STAKEHOLDERS MITIGATION STRATEGIES INCLUDE • Customers • Users are presented with a privacy notice that explains what personal information will be collected, how it could be used, and obtain explicit consent from the customer • Marketing and sales teams • Establish user support channels, such as a dedicated helpdesk or live chat, to address concerns that users may encounter while using the app • Operations and logistics teams • Provide users with visibility and control over their data. This includes allowing users to access, edit, or delete their data, as well as providing • Customer service and support teams options to opt-out of certain data processing activities • Data privacy and security regulators • Competitors • Reference Global Data Protection Considerations for Generative AI * Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To see a more © 2024 Deloitte Asia Pacific Services Limited comprehensive TAI question guide that can be used as an additional resource, please click here for Deloitte Trustworthy AI Questions Guide. AP Generative AI QRM Guidelines 43

## 第 44 頁

### 表格 1

|  |
| --- |
| TRUSTWORTHY AI CONSIDERATIONS |

### 表格 2

| "TRANSPARENT AND EXPLAINABLE" TAI QUESTIONS* TO CONSIDER INCLUDE | None |
| --- | --- |
| JUSTIFIABLE | AUDITABLE |
| • What are the main inputs that influence the technology’s output or recommendation? • How does each input influence the technology’s output or recommendation? | • What processes will be in place to help enable traceability of responses? • How are faults or feedback from users captured and actioned to investigate and/or remediate concerns? |
| INTERPETABLE | VISIBLE |
| • What level of transparency and explainability is appropriate to help ensure users are adequately informed and understand the technology is AI-driven? • Are users able toadequately understand the technology, its data, and its algorithms enough to trust them? | • How can customers and clients understand how their inputs and information are stored, accessed, and used by the technology? • What are the procedures to communicate if something does go wrong? |
|  | None |

A VIRTUAL BANK EXPERIENCE TRANSPARENT AND EXPLAINABLE (Virtual Reality-Enabled Retail Banking Centers) A retail banking company, in its continuous pursuit of improving client experience and providing customers with multiple methods of interacting with their accounts, services, and offerings, has implemented a "Virtual Reality (VR) Customer Agents" Program. This program enables customers to use a VR headset to conduct business with the financial institution and interact with a GenAI powered virtual service representative from the comfort of their own home, at their convenience. These virtual agents are designed to provide conversational, tailored responses to questions about customer accounts and financial needs. By this, the company aims to deliver a personalized experience to customers while also reducing the costs associated with hiring and training additional human customer service workers. One of the key advantages of this virtual space is the ability to access customer data in real-time, allowing the conversational agent to provide faster and higher-quality service and offerings. TRUSTWORTHY AI CONSIDERATIONS GOALS / BENEFITS "TRANSPARENT AND EXPLAINABLE" TAI QUESTIONS* TO CONSIDER INCLUDE • Provide tailored responses to questions about customer accounts JUSTIFIABLE AUDITABLE and financial needs, enhancing clients’ experience • What are the main inputs that influence the technology’s output or • What processes will be in place to help enable traceability of responses? • Provide access to customer data in real-time. recommendation? • How are faults or feedback from users captured and actioned to • How does each input influence the technology’s output or • Provide faster and higher-quality service and offerings recommendation? investigate and/or remediate concerns? • Reduce costs associated with hiring and training additional human INTERPETABLE VISIBLE customer service workers • What level of transparency and explainability is appropriate to help • Free up human customer service operatives to concentrate on the ensure users are adequately informed and understand the • How can customers and clients understand how their inputs and delivery of more value-enhancing elements of their role technology is AI-driven? information are stored, accessed, and used by the technology? • Are users able toadequately understand the technology, its data, • What are the procedures to communicate if something does go wrong? and its algorithms enough to trust them? AFFECTED STAKEHOLDERS MITIGATION STRATEGIES INCLUDE • Clients • Define clear policies that outline how customer or client data is collected, stored, and used within the virtual reality customer agents' program • Human Customer Service Workers • Decide on the level of transparency and explainability required for users and other stakeholders (e.g. system/product owners) and take • Government Agencies related to the retail banking industry measures to achieve this requirement and update it as needed. • Service or Product owner • Use simple language that provides explanations or justifications for the responses given by the virtual agent and disclose upfront that the technology is AI-driven * Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To see a more © 2024 Deloitte Asia Pacific Services Limited comprehensive TAI question guide that can be used as an additional resource, please click here for Deloitte Trustworthy AI Questions Guide. AP Generative AI QRM Guidelines 44

## 第 45 頁

### 表格 1

|  |
| --- |
| TRUSTWORTHY AI CONSIDERATIONS |

### 表格 2

| "FAIR AND IMPARTIAL" TAI QUESTIONS* TO CONSIDER INCLUDE | None |
| --- | --- |
| ACCESSIBLE | INCLUSIVE |
| • How has accessibility been considered when designing the technology? | • How does the technology factor in inclusivity in its decisions? |
| EQUITABLE | UNBIASED |
| • Is differential treatment of groups justified by underlying factors? Under what situations is partiality justified for the technology? • What would stakeholders (including individuals affected by the technology) consider to be equitable in relation to the technology's outputs? | • How has the potential impact of bias in the data been considered? How are we minimizing bias in our data, algorithms and output? • Can discriminatory bias, if any, be removed with data adjustments? |
|  | None |

TALENT FINDER FAIR AND IMPARTIAL (Resume screening application) A large oil and gas company has developed a resume screening application to use in their Human Resources department. The application is built on historical data, and it scans large volumes of resumes at once and then recommends the best-matched resumes for specific roles to be interviewed. The application is intended to reduce human bias and save the human resources department significant time helping the company fill roles faster than they have been able to before. Additionally, the company can use the application to help them satisfy immediate workforce needs. TRUSTWORTHY AI CONSIDERATIONS GOALS / BENEFITS "FAIR AND IMPARTIAL" TAI QUESTIONS* TO CONSIDER INCLUDE • Reduce human bias in recommendations for the best-matched ACCESSIBLE INCLUSIVE resumes for specific roles • How has accessibility been considered when designing the • How does the technology factor in inclusivity in its decisions? • Accelerates the overall hiring process, reducing costs and technology? improving the responsiveness of the process for applicants EQUITABLE UNBIASED • Is differential treatment of groups justified by underlying factors? Under what situations is partiality justified for the technology? • How has the potential impact of bias in the data been considered? • What would stakeholders (including individuals affected by the How are we minimizing bias in our data, algorithms and output? technology) consider to be equitable in relation to the technology's • Can discriminatory bias, if any, be removed with data adjustments? outputs? AFFECTED STAKEHOLDERS MITIGATION STRATEGIES INCLUDE • Job Applicants • Aim to use historical data that is representative of a diverse range of candidates such as gender, race, ethnicity, and educational background when developing the application. Evaluate and address potential historic bias in data sets. • Hiring Managers • Test the algorithms for bias • HR Department • Review the recommendations made by the application to add a human in the loop • Current Employees • Communicate to job applicants that their resumes will be screened using AI technology • Government Agencies related to employment laws • Consult with Risk, Privacy and OGC teams to align functionality with applicable regulations and employment laws • Non-governmental organizations focused on employment rights, data privacy, etc. * Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To see a more © 2024 Deloitte Asia Pacific Services Limited comprehensive TAI question guide that can be used as an additional resource, please click here for Deloitte Trustworthy AI Questions Guide. AP Generative AI QRM Guidelines 45

## 第 46 頁

### 表格 1

|  |
| --- |
| TRUSTWORTHY AI CONSIDERATIONS |

### 表格 2

| GOALS/ BENEFITS • Provide the students with personalized learning experiences, optimizing their educational outcomes • Supports teachers enabling them to focus on tasks that require their expertise and personal interaction such as planning, interacting with students, evaluation, and student support |
| --- |
| AFFECTED STAKEHOLDERS • Students • Teachers • School Administrators • Parents/Guardians • Researchers or individuals with experience in the field of education • Education Policy Makers |

### 表格 3

| “RESPONSIBLE" TAI QUESTIONS TO CONSIDER INCLUDE | None |
| --- | --- |
| VALUE ADDING | COMMON AND SOCIAL GOOD |
| • What difference will implementation of the technology make, and is it enough to counter potential harms? • Are there alternative or lower-risk ways to achieve the same aims? | • Who is checking that the technology is behaving in a way that is positive towards humanity on a routine basis? • Are there potential adverse consequences of the technology and how could these be mitigated? • Could the technology be misused and how could this be mitigated? |
| SUSTAINABILITY FOCUSED | HUMANE |
| • Does it align with our values? • Could its purpose be misrepresented? • Do you understand the environmental impact of the technology's development, deployment and/or use? What have we considered to help reduce that impact? | • How can we take a responsible approach to integrating virtual teachers/assistants/aids without losing the human element? |
| MITIGATION STRATEGIES INCLUDE • Monitor the app's performance for alignment with responsible educational practices • Develop training for students and teachers to use the app, including responsible use, privacy and ethical considerations, as well as the knowledge teachers will need to guide the student in using the app responsibly • Conduct consequence scanning to identify how the technology might have adverse or undesired impacts (including potential for misuse), evaluate potential risks and apply mitigations | None |

EDUCATION 2.0 RESPONSIBLE (Hyper-Personalized Education) A public school organization has developed an app called “Education 2.0”. This app aims to provide hyper-personalized digital teachers to students, that can adapt to different learning needs and curricula. With this app, students can receive a customized learning experience that is tailored to their strengths, weaknesses, and preferences. The Education 2.0 App works by checking the student's work and comprehension and then adapting the lessons and learning strategies accordingly. This means that students can receive one-on-one attention from their personalized digital teacher to master new skills and knowledge. As a result, the human instructor can focus on higher-level planning, interacting with students, evaluation, and student support. TRUSTWORTHY AI CONSIDERATIONS GOALS/ BENEFITS “RESPONSIBLE" TAI QUESTIONS TO CONSIDER INCLUDE • Provide the students with personalized learning experiences, VALUE ADDING COMMON AND SOCIAL GOOD optimizing their educational outcomes • What difference will implementation of the technology make, and is • Who is checking that the technology is behaving in a way that is positive • Supports teachers enabling them to focus on tasks that require their it enough to counter potential harms? towards humanity on a routine basis? expertise and personal interaction such as planning, interacting with • Are there alternative or lower-risk ways to achieve the same aims? • Are there potential adverse consequences of the technology and how students, evaluation, and student support could these be mitigated? • Could the technology be misused and how could this be mitigated? SUSTAINABILITY FOCUSED HUMANE • Does it align with our values? • How can we take a responsible approach to integrating virtual • Could its purpose be misrepresented? teachers/assistants/aids without losing the human element? • Do you understand the environmental impact of the technology's development, deployment and/or use? What have we considered to help reduce that impact? AFFECTED STAKEHOLDERS MITIGATION STRATEGIES INCLUDE • Students • Monitor the app's performance for alignment with responsible educational practices • Teachers • Develop training for students and teachers to use the app, including responsible use, privacy and ethical considerations, as well as the knowledge teachers will need to guide the student in using the app responsibly • School Administrators • Conduct consequence scanning to identify how the technology might have adverse or undesired impacts (including potential for misuse), • Parents/Guardians evaluate potential risks and apply mitigations • Researchers or individuals with experience in the field of education • Education Policy Makers * Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To see a more © 2024 Deloitte Asia Pacific Services Limited comprehensive TAI question guide that can be used as an additional resource, please click here for Deloitte Trustworthy AI Questions Guide. AP Generative AI QRM Guidelines 46

## 第 47 頁

### 表格 1

|  |
| --- |
| TRUSTWORTHY AI CONSIDERATIONS |

### 表格 2

| "ACCOUNTABLE" TAI QUESTIONS TO CONSIDER INCLUDE | None |
| --- | --- |
| OWNERSHIP | ANSWERABLE |
| • Who is accountable for the technology throughout its lifecycle? Are they equipped/enabled to fulfil their role for the technology? • How is the system/use case monitored and how frequently throughout its lifecycle? • Who is accountable for protecting against model drift and ensuring quality is maintained? | • Who are the dedicated ‘first responders’ in the event of an emergency, issue, or mistake originated by the technology? How are they equipped to take on oversight responsibilities? • Is there a dedicated team for users or other stakeholders to reach out to with doubts or concerns about the technology? |
| RESOLVABLE |  |
| • Are there processes in place for when inconsistencies or concerns with the technology are discovered? |  |

A HELPING HAND IN THE FIELD ACCOUNTABLE (Virtual Field Assistant for Engineers) A mining company has recently implemented a new tool called "A Helping Hand in the Field." This virtual field assistant provides engineers with on-demand access to engineering knowledge and support in problem-solving, improving efficiency, productivity, and decision-making capabilities. On a day-to-day basis, this virtual field assistant serves as a reference tool, providing quick access to vast amounts of technical information. It also delivers relevant information and directs engineers to appropriate resources. In addition to this, it helps with problem-solving by responding to questions about specific engineering concepts, principles, or calculations. When encountering concerns or challenges in the field, engineers can describe the problem to the virtual field assistant, and it will return appropriate questions to identify the cause or provide step-by-step guidance for resolution. This feature allows engineers to quickly troubleshoot concerns and minimize downtime. TRUSTWORTHY AI CONSIDERATIONS GOALS / BENEFITS "ACCOUNTABLE" TAI QUESTIONS TO CONSIDER INCLUDE • Empower engineers with the knowledge and resources they need to OWNERSHIP ANSWERABLE make accurate and effective decisions in the field • Who is accountable for the technology throughout its lifecycle? Are • Who are the dedicated ‘first responders’ in the event of an emergency, • Improve efficiency, productivity, and problem-solving capabilities they equipped/enabled to fulfil their role for the technology? issue, or mistake originated by the technology? How are they equipped • How is the system/use case monitored and how frequently throughout to take on oversight responsibilities? its lifecycle? • Is there a dedicated team for users or other stakeholders to reach out to • Who is accountable for protecting against model drift and ensuring with doubts or concerns about the technology? quality is maintained? RESOLVABLE • Are there processes in place for when inconsistencies or concerns with the technology are discovered? AFFECTED STAKEHOLDERS MITIGATION STRATEGIES INCLUDE • Engineers • Establish clear guidelines and policies for the use of the virtual field assistant tool • Supervisors • Provide comprehensive training to engineers on the responsible use of the virtual field assistant tool • Field Technicians • Implement access controls to help ensure that only authorized engineers can use the tool • Occupational Risk Prevention Team • Help ensure processes are established to monitor for faults, remediate and learn • Government Agencies related to the mining industry • Help ensure accountability is defined for relevant stakeholders throughout the lifecycle of the technology * Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To see a more © 2024 Deloitte Asia Pacific Services Limited comprehensive TAI question guide that can be used as an additional resource, please click here for Deloitte Trustworthy AI Questions Guide. AP Generative AI QRM Guidelines 47

## 第 48 頁

### 表格 1

|  |
| --- |
| TRUSTWORTHY AI CONSIDERATIONS |

### 表格 2

| "ROBUST AND RELIABLE" TAI QUESTIONS TO CONSIDER INCLUDE | None |
| --- | --- |
| CONSISTENT | ADAPTABLE |
| • What are feedback loops looking to achieve and how will their impact be assessed and monitored? What process will be in place when inconsistencies or concerns are discovered? • How stable is the model and how susceptible to model drift is it over time? | • How frequently will models be validated or retrained? • Will the model produce reliable results with new data sets? |
| ACCURATE | PREDICTABLE |
| • Are the outputs and recommendations stable and reliable over time, how has this been assured in the design and testing and what ongoing quality controls may be required through the operational life of the system? • What is the consequence/impact of lower quality or inaccurate outputs? • Are there situations where the technology should not be used, or where a factor of safety should be applied to its recommendations? | • What processes will you have to check the model for incorrect, manipulated, or unauthorized output? |
|  |  |
| MITIGATION STRATEGIES INCLUDE • Perform human-in-the-loop controls to review the outputs, helping to ensure accuracy and quality • Identify key business processes that will be impacted by the new technology system, and thoroughly assess the risks of harm that may occur in the execution of those business processes • Conduct testing and validation of the new technology system before its full implementation • Establish a system for continuous monitoring and maintenance of the technology • Realize recurrent meetings with laboratory staff to provide feedback on the system's performance and suggestions for improvement | None |

OPTIMIZING LAB PROCEDURES ROBUST AND RELIABLE (Experimental Design) A mid-size laboratory is set to implement a new technology system aimed at creating procedural templates, as well as recommendations on best practices (e.g., techniques, equipment, etc.), to enhance laboratory processes. Leveraging historical data and scientific principles, this new system has the potential to suggest novel experimental designs, more efficient processes, or alternate uses of equipment, thereby stimulating innovation in laboratory procedures. The system will analyze data from lab protocols, equipment specifications, previous experimental designs, and techniques, providing a holistic understanding of laboratory procedures and principles. With this system, the laboratory will be able to identify areas of improvement in its processes and make data-driven decisions to optimize efficiency and productivity. TRUSTWORTHY AI CONSIDERATIONS GOALS / BENEFITS "ROBUST AND RELIABLE" TAI QUESTIONS TO CONSIDER INCLUDE • Enhance laboratory processes CONSISTENT ADAPTABLE • Stimulate innovation in laboratory procedures • What are feedback loops looking to achieve and how will their impact be assessed and • How frequently will models be validated or retrained? monitored? What process will be in place when inconsistencies or concerns are • Will the model produce reliable results with new data • Enable the laboratory to identify areas of improvement and make discovered? sets? data-driven decisions to optimize resource use • How stable is the model and how susceptible to model drift is it over time? ACCURATE PREDICTABLE • Are the outputs and recommendations stable and reliable over time, how has this been • What processes will you have to check the model for assured in the design and testing and what ongoing quality controls may be required incorrect, manipulated, or unauthorized output? through the operational life of the system? • What is the consequence/impact of lower quality or inaccurate outputs? • Are there situations where the technology should not be used, or where a factor of safety should be applied to its recommendations? AFFECTED STAKEHOLDERS MITIGATION STRATEGIES INCLUDE • Laboratory Staff • Perform human-in-the-loop controls to review the outputs, helping to ensure accuracy and quality • Laboratory Management Team • Identify key business processes that will be impacted by the new technology system, and thoroughly assess the risks of harm that may occur in the execution of those business processes • Patients • Conduct testing and validation of the new technology system before its full implementation • Regulatory Bodies • Establish a system for continuous monitoring and maintenance of the technology • Quality Assurance and Accreditation Bodies • Realize recurrent meetings with laboratory staff to provide feedback on the system's performance and suggestions for improvement * Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To see a more © 2024 Deloitte Asia Pacific Services Limited comprehensive TAI question guide that can be used as an additional resource, please click here for Deloitte Trustworthy AI Questions Guide. AP Generative AI QRM Guidelines 48

## 第 49 頁

### 表格 1

|  |
| --- |
| TRUSTWORTHY AI CONSIDERATIONS |

### 表格 2

| "SAFE AND SECURE" TAI QUESTIONS TO CONSIDER INCLUDE | None |
| --- | --- |
| USER FRIENDLY | INVULNERABLE |
| • Is the technology intuitive for its intended users? | • Is the technology aligned with the relevant global Technology Operating Model (TOM) and cyber security standards and processes? |
| USER PROTECTION |  |
| • How are you ensuring that the AI solution is safe for its intended users and society at large? • Have you considered consequence scanning to anticipate potential, physical or psychological harms that could arise due to the intended use or misuse of the AI system? |  |
|  | None |

MITIGATING RISK AS IT ARISES SAFE AND SECURE (Real-Time Risk Management) A financial services company is in the process of adopting a new technology system to support corporate risk management activities, including those related to credit, investment, fraud, and cybersecurity. This system enables real-time monitoring and verification for risk and fraud identification, which has a direct impact on operational efficiency and cost savings. The ability to access relevant data and contextual information in real time supports compliance with regulations and industry standards. By evaluating risks based on customer data, industry data, and real-time updates, the organization can conduct better risk assessments with greater accuracy and impact. The new system creates synthetic data that mirrors fraudulent transactions, which can help train models to better identify risky scenarios, predict fraudulent patterns, and ultimately reduce the overall fraud rate. TRUSTWORTHY AI CONSIDERATIONS GOALS / BENEFITS "SAFE AND SECURE" TAI QUESTIONS TO CONSIDER INCLUDE • Enable real-time monitoring and verification for risk and fraud USER FRIENDLY INVULNERABLE identification • Is the technology intuitive for its intended users? • Is the technology aligned with the relevant global Technology • Conduct more accurate risk assessments Operating Model (TOM) and cyber security standards and processes? • Reduce the overall fraud rate by enhancing the company's ability USER PROTECTION to detect and prevent fraudulent activities • How are you ensuring that the AI solution is safe for its intended users and society at large? • Have you considered consequence scanning to anticipate potential, physical or psychological harms that could arise due to the intended use or misuse of the AI system? AFFECTED STAKEHOLDERS MITIGATION STRATEGIES INCLUDE • Risk Management Team • Implement strong data encryption measures to protect sensitive information within the system • Clients • Implement multi-factor authentication for system access to enhance security • Tech Team • Develop training for system users about safe and secure practices such as identifying phishing attempts, avoiding suspicious links or attachments, and adhering to security protocols • Regulatory bodies • Regularly perform human controls to identify bias or potential harms and mitigate any discriminatory outcomes • Include consequence scanning to anticipate potential, physical or psychological harms that could arise due to the intended use or misuse of the AI system * Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To see a more © 2024 Deloitte Asia Pacific Services Limited comprehensive TAI question guide that can be used as an additional resource, please click here for Deloitte Trustworthy AI Questions Guide. AP Generative AI QRM Guidelines 49

## 第 50 頁

### Contacts and References

## 第 51 頁

### 表格 1

|  | Contacts and References Contacts • For any questions or needs regarding Generative AI application, please reach out to local Generative AI Clearinghouse, AI Institute or Market Incubator teams. • For risk triage process and subsequent deep-dive assessment for market activation or service delivery transformation, each AP PF and BU has identified the relevant BU QRM or risk point of contact as set out in the next slide to facilitate the process. Resources and Additional Information • Explore Deloitte Asia Pacific AI Institute: Asia Pacific AI Institute • Explore Global Generative AI materials: Global Generative AI KX Page • Explore Deloitte’s Trustworthy AI Framework: Deloitte Trustworthy AI Framework Guidance and Questions Guide • Explore Generative AI learning resources: Generative AI Fluency and AP Gen AI Cura Group. • Explore the globally available environments here: Generative AI Environments and Platforms Page • Explore the Vendor Guardrails that have been put in place here: Generative AI • Download the guide for Generative AI Independence Considerations here: Generative AI Independence | None |
| --- | --- | --- |
| None | ©© 22002244 DDeellooiittttee AAssiiaa PPaacciiffiicc SSeerrvviicceess LLiimmiitteedd | AP Generative AI QRM Guidelines 51 |

Contacts and References Contacts • For any questions or needs regarding Generative AI application, please reach out to local Generative AI Clearinghouse, AI Institute or Market Incubator teams. • For risk triage process and subsequent deep-dive assessment for market activation or service delivery transformation, each AP PF and BU has identified the relevant BU QRM or risk point of contact as set out in the next slide to facilitate the process. Resources and Additional Information • Explore Deloitte Asia Pacific AI Institute: Asia Pacific AI Institute • Explore Global Generative AI materials: Global Generative AI KX Page • Explore Deloitte’s Trustworthy AI Framework: Deloitte Trustworthy AI Framework Guidance and Questions Guide • Explore Generative AI learning resources: Generative AI Fluency and AP Gen AI Cura Group. • Explore the globally available environments here: Generative AI Environments and Platforms Page • Explore the Vendor Guardrails that have been put in place here: Generative AI • Download the guide for Generative AI Independence Considerations here: Generative AI Independence

## 第 52 頁

### 表格 1

| PF | A&A | TL | RA | FA | CON |
| --- | --- | --- | --- | --- | --- |
| AU | Amanda Terry Alicia Johnson (Audit) Chloe Meyer | Jacqui Hill | Samantha Butler | Aisling Ruane | Jake Carnovale |
| CN | Tony Wong | Jenny Tsang Cherie Chen | Yiyi Li | David Tan | Ma Jing |
| JP | Akiko Kato | Kawase Tetsuya Ono Nozomi | Tetsuya Ito | Masanori Kinamari | Noriko Matsumori Toshiya Hakoshima |
| KR | Min Kyung Kim | None | None | None | None |
| NZ | Mike Hawken | Ian Fay | Rhys Hermansson | Scott McClay | David Lovatt |
| SA | Chandrika Sridhar | Samir Gandhi | Murthi Kalaiselvi | Krishna Chaturvedi | Varun Rastogi |
| None | Sitaramprasad Sharma Raghavapudi | None | None | None | None |
| SEA | None | Wisesasari, Wisesasari (Seysi) | Fazlin Hanoum Ilhan | Christine Leong | Lai Weng Yew |
| TW | Vincent Cheng | Gilbert Chiang | Mike Chang | Alan Wong | Ann Lai |

### 表格 2

| PF | A&A | TL | RA | FA | CON | Enabling Areas |
| --- | --- | --- | --- | --- | --- | --- |
| AU | Amanda Terry Alicia Johnson (Audit) Chloe Meyer | Jacqui Hill Areeba Wazahat | Samantha Butler | Aisling Ruane | Jake Carnovale | John Green NinaYiannopoulos |
| CN | Tony Wong | Jenny Tsang Cherie Chen | Yiyi Li | David Tan | Ma Jing | Joanne Shuk Yee Lee |
| JP | Akiko Kato | Kawase, Tetsuya Ono, Nozomi | Tetsuya Ito | Masanori Kinamari | Noriko Matsumori Toshiya Hakoshima | Tetsuya Ito |
| KR | Kyung Jae Lee | Shin Young Kang | Sun Ah Choi | Hyun Jeong Choi | Seo Jeong Hong | Jun Soo Kim |
| NZ | Mike Hawken | Ian Fay | Rhys Hermansson | Scott McClay | David Lovatt | Douglas Ah Poe |
| SA | Chandrika Sridhar | Samir Gandhi | Murthi Kalaiselvi | Krishna Chaturvedi | Varun Rastogi Kaushik Dasgupta | Debashish Banerjee |
| None | Sitaramprasad Sharma Raghavapudi | None | None | None | None | None |
| SEA | None | Wisesasari, Wisesasari (Seysi) | Fazlin Hanoum Ilhan | Christine Leong | Lai Weng Yew | None |
| TW | Ricky Lin/Vita Yu | None | None | None | None | None |

Generative AI Risk Triage Process - BU QRM Point of Contact (as of May 2024, please refer to Generative AI under AP Risk Hub for the latest list Market Activation PF A&A TL RA FA CON Amanda Terry AU Alicia Johnson (Audit) Jacqui Hill Samantha Butler Aisling Ruane Jake Carnovale Chloe Meyer Jenny Tsang CN Tony Wong Yiyi Li David Tan Ma Jing Cherie Chen Akiko Kato Kawase Tetsuya Noriko Matsumori JP Tetsuya Ito Masanori Kinamari Ono Nozomi Toshiya Hakoshima KR Min Kyung Kim NZ Mike Hawken Ian Fay Rhys Hermansson Scott McClay David Lovatt Chandrika Sridhar Samir Gandhi Murthi Kalaiselvi Krishna Chaturvedi Varun Rastogi SA Sitaramprasad Sharma Raghavapudi SEA None Wisesasari, Wisesasari (Seysi) Fazlin Hanoum Ilhan Christine Leong Lai Weng Yew TW Vincent Cheng Gilbert Chiang Mike Chang Alan Wong Ann Lai Service Delivery Transformation PF A&A TL RA FA CON Enabling Areas Amanda Terry Jacqui Hill John Green AU Alicia Johnson (Audit) Samantha Butler Aisling Ruane Jake Carnovale Areeba Wazahat NinaYiannopoulos Chloe Meyer Tony Wong Jenny Tsang Joanne Shuk Yee CN Yiyi Li David Tan Ma Jing Cherie Chen Lee Akiko Kato Kawase, Tetsuya Noriko Matsumori JP Tetsuya Ito Masanori Kinamari Tetsuya Ito Ono, Nozomi Toshiya Hakoshima KR Kyung Jae Lee Shin Young Kang Sun Ah Choi Hyun Jeong Choi Seo Jeong Hong Jun Soo Kim NZ Mike Hawken Ian Fay Rhys Hermansson Scott McClay David Lovatt Douglas Ah Poe Varun Rastogi Chandrika Sridhar Samir Gandhi Murthi Kalaiselvi Krishna Chaturvedi Debashish Banerjee Kaushik Dasgupta SA Sitaramprasad Sharma Raghavapudi SEA None Wisesasari, Wisesasari (Seysi) Fazlin Hanoum Ilhan Christine Leong Lai Weng Yew None TW Ricky Lin/Vita Yu Note: AP OGC contact persons for legal support (Enabling Area) - Constance Xin (AP OGC – China) and Hannah Szto (AP OGC – Australia)

## 第 53 頁

Deloitte refers to one or more of Deloitte Touche Tohmatsu Limited (“DTTL”), its global network of member firms, and their related entities (collectively, the “Deloitte organization”). DTTL (also referred to as “Deloitte Global”) and each of its member firms and related entities are legally separate and independent entities, which cannot obligate or bind each other in respect of third parties. DTTL and each DTTL member firm and related entity is liable only for its own acts and omissions, and not those of each other. DTTL does not provide services to clients. Please see www.deloitte.com/about to learn more. Deloitte Asia Pacific Limited is a company limited by guarantee and a member firm of DTTL. Members of Deloitte Asia Pacific Limited and their related entities, each of which is a separate and independent legal entity, provide services from more than 100 cities across the region, including Auckland, Bangkok, Beijing, Bengaluru, Hanoi, Hong Kong, Jakarta, Kuala Lumpur, Manila, Melbourne, Mumbai, New Delhi, Osaka, Seoul, Shanghai, Singapore, Sydney, Taipei and Tokyo. This communication and any attachment to it is for internal distribution among personnel of the Deloitte organization. addressed. If you are not the intended recipient, please notify us immediately by replying to this email and then please delete this communication and all copies of it on your system. Please do not use this communication in any way. None of DTTL, its member firms, related entities, employees or agents shall be responsible for any loss or damage whatsoever arising directly or indirectly in connection with any person relying on this communication.

@echo off
chcp 65001 >nul
echo ========================================
echo 增強版文件轉換工具 - DOCX/PDF/PPTX to Markdown
echo ========================================
echo.

REM 檢查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [錯誤] 找不到Python，請確認已安裝Python 3.6+
    pause
    exit /b 1
)

REM 檢查是否需要安裝依賴套件
echo [資訊] 檢查依賴套件...
python install_dependencies_enhanced.py

if %errorlevel% neq 0 (
    echo [警告] 依賴套件安裝可能有問題
    echo.
)

REM 創建必要的目錄
if not exist "input" (
    mkdir input
    echo [資訊] 已創建 input 目錄
)

if not exist "output" (
    mkdir output
    echo [資訊] 已創建 output 目錄
)

echo.
echo [資訊] 增強版功能特色:
echo - ✨ 智能表格處理（合併儲存格、格式化）
echo - 🎯 進階標題處理（自動層級、樣式）
echo - 🧹 頁頭頁尾自動識別與移除
echo - 💎 文字格式保留（粗體、斜體、超連結）
echo - 📊 更好的PDF文字提取
echo - 🔍 智能內容過濾
echo - 📋 詳細轉換報告
echo.

REM 檢查input目錄是否有文件
set file_count=0
for %%f in (input\*.docx input\*.pdf input\*.pptx) do (
    set /a file_count+=1
)

if %file_count% equ 0 (
    echo [提示] input 目錄中沒有找到支援的文件
    echo 請將 DOCX、PDF 或 PPTX 文件放入 input 目錄中
    echo.
    echo 按任意鍵打開 input 目錄...
    pause >nul
    start input
    exit /b 0
)

echo [資訊] 找到 %file_count% 個文件待轉換
echo.

REM 詢問轉換選項
echo 轉換選項:
echo 1. 轉換所有文件 (增量模式)
echo 2. 強制重新轉換所有文件
echo 3. 清理臨時文件後轉換
echo 4. 只清理臨時文件
echo 5. 詳細模式轉換 (顯示更多信息)
echo.
set /p choice="請選擇 (1-5): "

echo.
echo [資訊] 開始處理...

if "%choice%"=="1" (
    echo [模式] 增量轉換
    python document_converter_enhanced.py
) else if "%choice%"=="2" (
    echo [模式] 強制重新轉換
    python document_converter_enhanced.py --force
) else if "%choice%"=="3" (
    echo [模式] 清理後轉換
    python document_converter_enhanced.py --cleanup --force
) else if "%choice%"=="4" (
    echo [模式] 只清理臨時文件
    python document_converter_enhanced.py --cleanup
) else if "%choice%"=="5" (
    echo [模式] 詳細模式轉換
    python document_converter_enhanced.py --verbose
) else (
    echo [預設] 增量轉換
    python document_converter_enhanced.py
)

if %errorlevel% equ 0 (
    echo.
    echo [成功] 轉換完成！
    echo.
    echo 📁 轉換結果保存在 output 目錄中
    echo 📋 詳細報告: output\conversion_summary.md
    echo 📊 轉換記錄: output\conversion_report.json
    echo.
    echo 增強版特色:
    echo ✅ 智能表格處理 - 保留表格結構和格式
    echo ✅ 標題自動識別 - 正確的Markdown標題層級
    echo ✅ 頁頭頁尾移除 - 自動過濾無關內容
    echo ✅ 格式保留 - 粗體、斜體等文字格式
    echo ✅ 內容優化 - 智能段落分割和清理
    echo.
    set /p open_output="是否要打開 output 目錄查看結果？(y/n): "
    if /i "%open_output%"=="y" (
        start output
    )
    
    set /p open_report="是否要查看轉換報告？(y/n): "
    if /i "%open_report%"=="y" (
        if exist "output\conversion_summary.md" (
            start output\conversion_summary.md
        )
    )
) else (
    echo.
    echo [錯誤] 轉換過程中發生錯誤
    echo 請檢查 converter_enhanced.log 文件查看詳細錯誤信息
    echo.
    echo 常見問題解決:
    echo 1. 確認文件沒有被其他程式開啟
    echo 2. 檢查文件是否損壞
    echo 3. 確認有足夠的磁碟空間
    echo 4. 重新安裝依賴套件
)

echo.
echo 按任意鍵退出...
pause >nul

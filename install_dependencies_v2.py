#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件轉換工具 v2.0 依賴套件安裝程式
"""

import subprocess
import sys
import importlib
import platform

def check_python_version():
    """檢查Python版本"""
    version = sys.version_info
    if version < (3, 6):
        print(f"❌ Python版本過低: {version.major}.{version.minor}")
        print("   需要Python 3.6或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True

def install_package(package_name, import_name=None, description=""):
    """安裝Python套件"""
    if import_name is None:
        import_name = package_name.replace('-', '_')
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安裝 {description}")
        return True
    except ImportError:
        print(f"📦 正在安裝 {package_name}... {description}")
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package_name, "--upgrade"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.PIPE)
            print(f"✅ {package_name} 安裝成功")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ {package_name} 安裝失敗: {e}")
            return False

def main():
    """主函數"""
    print("🔧 文件轉換工具 v2.0 依賴套件安裝程式")
    print("=" * 60)
    
    # 檢查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 檢查作業系統
    os_name = platform.system()
    print(f"✅ 作業系統: {os_name}")
    print()
    
    # 需要安裝的套件
    packages = [
        ("python-docx", "docx", "- DOCX文件處理"),
        ("pdfplumber", "pdfplumber", "- PDF文件處理（推薦）"),
        ("PyPDF2", "PyPDF2", "- PDF文件處理（備用）"),
        ("python-pptx", "pptx", "- PPTX文件處理"),
        ("Pillow", "PIL", "- 圖片處理"),
    ]
    
    print("📦 開始安裝依賴套件...")
    print()
    
    success_count = 0
    total_count = len(packages)
    
    for package_name, import_name, description in packages:
        if install_package(package_name, import_name, description):
            success_count += 1
        print()
    
    print("=" * 60)
    print(f"📊 安裝結果: {success_count}/{total_count} 套件安裝成功")
    
    if success_count == total_count:
        print("🎉 所有依賴套件安裝完成！")
        print()
        print("🚀 使用方法:")
        print("python document_converter_v2.py --help")
        print()
        print("✨ 主要功能:")
        print("- 📄 支援 PDF、DOCX、PPTX 格式")
        print("- 🎯 格式結構完整還原")
        print("- 🧹 智能頁眉頁腳處理")
        print("- 📊 表格轉Markdown格式")
        print("- 🖼️  圖片另存並引用")
        print("- 📋 詳細轉換報告")
        print("- 🔄 批次處理支援")
        print("- 🌏 UTF-8中文支援")
    else:
        print("⚠️  部分套件安裝失敗，請手動安裝:")
        print()
        for package_name, import_name, description in packages:
            try:
                importlib.import_module(import_name)
            except ImportError:
                print(f"pip install {package_name}")
        
        print()
        print("💡 故障排除:")
        print("- 確保網路連接正常")
        print("- 嘗試使用管理員權限運行")
        print("- 升級pip: python -m pip install --upgrade pip")
        print("- 檢查防火牆設定")

if __name__ == "__main__":
    main()

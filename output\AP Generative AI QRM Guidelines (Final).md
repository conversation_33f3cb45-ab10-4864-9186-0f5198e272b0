## 第 1 頁

May 2024 (Version 1.0)AP Generative AI
QRM Guidelines

## 第 2 頁

©  2024 Deloitte Asia Pacific Services Limited 2 AP Generative AI QRM Guidelines
Contents
1.Objectives
2.Understanding Generative AI and Trustworthy AI
3.Generative AI Risks and Limitations
4.Use Case Evaluation and Approval Process
5.Generative AI Data Guardrails -By Data Type and Persona
6.Generative AI Contracting Considerations
7.Generative AI IP Considerations
8.Generative AI Independence Considerations
9.Deloitte’s Trustworthy AI Framework Guidance
10.Contacts and References

## 第 3 頁

©  2024 Deloitte Asia Pacific Services Limited 3 AP Generative AI QRM Guidelines
©  2024 Deloitte Asia Pacific Services LimitedThe objectives of this AP Generative AI QRM guidelines are to:
•Provide a high -level introduction of Generative AI.
•Introduce Deloitte’s Trustworthy AI Framework that underpins the QRM assessment approach for Generative AI.
•Explain the risks and limitations of Generative AI.
•Establish the risk triage process to assess risk level of Generative AI use cases at the start and apply appropriate risk
management strategy and process to enable speed to the market.
•Set out the QRM considerations and provide guardrails for key risk areas, including data security, contracting, IP,
independence and Trustworthy AI.
•Provide contacts and tangible tools to facilitate the QRM assessment during the Generative AI development.
Please note that these guidelines may be refined and updated as it is used in practice.To help prepare for a bold and successful future with Generative AI, to capture the rapid growth of
Generative AI market capabilities, and to transform services to be augmented by Generative AI.  It is
important to understand the nature and scale of the risks, as well as the governance strategies that can
help mitigate them.

## 第 4 頁

©  2024 Deloitte Asia Pacific Services Limited 4 AP Generative AI QRM Guidelines
Understanding Generative AI

## 第 5 頁

©  2024 Deloitte Asia Pacific Services Limited 5 AP Generative AI QRM Guidelines Understanding Generative AI
What is Generative AI?
The ability of machines to create outputs across various modalities
Who is involved?
Technology leaders and start -ups are developing user applications on these
underlying modelsHow does it work?
Uses Foundation Models trained on massive amounts of data to understand human
communication and natural language
Why now?
Innovations in technology allows for the realization of an autonomous creation economy
EXAMPLE MODALITIES
Text Generation
Image GenerationCode Generation
Video Generation  Audio Generation
Note: To learn more about Generative AI technology, refer to Generative AI Fluency  for learning pathways and resources.

## 第 6 頁

©  2024 Deloitte Asia Pacific Services Limited 6 AP Generative AI QRM Guidelines Broad Categories of Value Capture from Generative AI
There are six (6) categories of usage that are emerging for our clients :
ACCELERATING INNOVATION
(PRODUCTS/SERVICES)
Increase the pace of new product or new service
development and speedier go -to-market
NEW DISCOVERY & INSIGHTS
Uncover new ideas, insights, questions and generally
unleash creativity
GOVERNMENT CITIZEN SERVICES
Increase accuracy of various government  and local
programs and create easier access for at -risk populations GROWTH
Increase revenue generation through hyper -personalized
marketing for target customers PROCESS EFFICIENCY
Create process efficiencies through automating
standard tasks and reducing manual interventions COST REDUCTION
Reduce cost, typically by 30% or greater primarily
through automating job functions and then undertaking
job substitutions
.
Call Center Operations
(Cross -Industry)
Claims Processing
(Insurance)
Content Generation
(Marketing/Advertisement)
Drug Discovery
(Life Sciences)
AI Augmented Financial Advisor
(Financial Services)
Welfare Distribution for Citizens
(Government)
Refer to Deloitte Generative AI Dossier  for details Generative AI Use Case

## 第 7 頁

©  2024 Deloitte Asia Pacific Services Limited 7 AP Generative AI QRM Guidelines Trusted AI and Trustworthy AI capability is a market differentiator for Deloitte
Delivering the AI Premium and Lead In the AI Ecosystem
AI-Led Transformation AI Strategy
Clients with the aspirations and roadmap to
develop Generative AI capability:
• Prioritization of Generative AI use cases
• Enhance existing CoE & Delivery Centers
• Develop use cases and MVPClients who wants to adopt / enable Generative
AI
• Implement transformation program with AI
• Scale Generative AI minimum viable
product (MVP) / assets into production
environmentsDeloitte has the Multi -Disciplinary Model to lead with the clients through their entire AI transformation journey, with AI Gover nance playing a key role in
differentiating its Generative AI solutions by advising clients based in this QRM Guidelines for Generative AI.
AI Governance is key for industries such as FSI and GPS, where Generative AI is adopted, where there is emphasis on AI regula tions and proper management
of client data in AI applications.
AI Governance
Clients are emphasizing to de -risk and adopt
safe practices in AI initiatives:
• Improve Governance baseline for
Generative AI adoption / usage that align
with regulations
• Leverage Trustworthy AI Framework to
build trust with AI applications
(see Deloitte’s Trustworthy AI Framework in
the next slide)
• Ensure adequate policies and controls on
the use of sensitive business and customer
data

## 第 8 頁

©  2024 Deloitte Asia Pacific Services Limited 8 AP Generative AI QRM Guidelines Deloitte’s Trustworthy AI Framework helps us to develop ethical safeguards across seven key dimensions —a crucial step in managin g the risks and capitalizing on the
returns associated with AI. It assists team members and clients in building and using AI -powered systems while promoting trustworthy and responsible use of AI.
Deloitte’s Trustworthy AI Framework Guidance and Questions GuideDeloitte’s Trustworthy AI Framework
Note: See details of the Evaluation Questions/Considerations in the “Trustworthy AI Framework” Section.
Trustworthy
AI FrameworkPrivacy and confidentiality are
respected in accordance with
contractual and regulatory
obligations, and data is not used
beyond its intended and stated
purposePRIVATE
Participants can understand how
their data is being used and how AI
systems make decisions; algorithms,
attributes, and correlations are open
to inspectionTRANSPARENT / EXPLAINABLE
AI applications include internal and
external checks to help ensure
equitable application across
participantsFAIR / IMPARTIALAI systems can be protected from
risks (including Cyber) that may
cause physical and/or digital harmSAFE AND SECURE
AI systems can learn from humans
and other systems, where
acceptable, and produce consistent
and reliable outputsROBUST / RELIABLE
Policies are in place to determine
who is responsible for the decisions
made or derived with the use of
technologyACCOUNTABLE
The technology is created and
operated in a socially responsible
mannerRESPONSIBLE

## 第 9 頁

©  2024 Deloitte Asia Pacific Services Limited 9 AP Generative AI QRM Guidelines
Generative AI Risks and Limitations

## 第 10 頁

©  2024 Deloitte Asia Pacific Services Limited 10 AP Generative AI QRM Guidelines There are risks and limitations to consider when using Generative AIGenerative AI Risks and Limitations
Generative AI models can exhibit
unintended behaviours, reinforce
existing biases, or generate content
that promotes harmful stereotypes
or discriminatory practices, leading
to negative social, economic, or
culture impacts
Unintended ConsequenceModels might output statements
that are factually false. Sources and
citations are unavailable for most
models. Users should be conscious
that outputs could be inaccurate
and perform due diligence to
validate generated content
Hallucination Bias in; bias out. If the training data
is biased (e.g., over/under -
representation of a population
cohort, sexism, racism), then the
outputs generated could also
exhibit biases. Bias reductions in
the training data and/or human
supervision during model training is
needed
Bias
Generative AI systems can be
vulnerable to adversarial attaches,
to maintain operations and
customer trust, proactively
minimizing risk from malicious
behavior on the network is critical.
For example, a customer service
bot revealing confidential
information to a hacker either by
prompt or unintentionally
Malicious BehaviorThese risks pertain to compliance
with existing laws, regulations
(including independence
requirements, see “ GAI
Independence Considerations ”),
and standards governing the use of
AI technologies which may vary in
different jurisdictions (see
legislation tracker )
Legal & Regulatory
Transparency & Explainability The opacity of Generative AI
systems can hinder understanding
and accountability.  Lack of
transparency and explainability
may raise concerns about decision -
making processes, bias and
adherence to ethical and legal
standardsSaaS -AI companies may use prompt
payloads to train future versions of
the base model, potentially
including confidential data that
could expose the user to IP
infringement claims, including
copyrights, trademarks, or patents.
See further information from “ GAI
IP Considerations ” section
IP Protection & Infringement
The deployment of Generative AI
can have wide -ranging social and
economic consequences, including
job replacement, economic
inequality, changes in labor
markets, and shifts in power
dynamics, which may require
careful consideration and
mitigation strategies
Social and Economic ImpactsIs the AI being used in a manner
consistent with the purpose of the
overall exercise? Is a human being
brought into the loop to decide
whether the AI’s suggestion needs
adjustment before actual use or
whether the use of AI is ethical
(e.g., submitting an AI -generated
essay as your own)?
Ethical Use
Generative AI Models are built on
data sharing. Consent for data used
(confidential information ,
personally identifiable information)
is necessary, and the residency of
data in geo -locations should also be
compliant with legal and
contractual requirements. See
further information from “ GAI Data
Guardrails ” section.
Confidentiality & Privacy

## 第 11 頁

©  2024 Deloitte Asia Pacific Services Limited 11 AP Generative AI QRM Guidelines In addition to Generative AI specific risks and limitations, there are additional risks we need to consider as we leverage Ge nerative AI to transform our
service delivery as well as helping our clients to harness the potential of Generative AI.Generative AI –Other risk considerations
•Data privacy, confidentiality & use rights need to be considered with the data inputted into Generative AI applications as we ll
as how the data is processed, whether it is stored or potentially used and shared by the Generative AI applications (e.g.: to
train third party models)Data privacy, confidentiality,
use & protection
•Ineffective security of Generative AI tools may expose Deloitte data, Deloitte client data and Deloitte IP to unauthorized
users, malicious activity, phishing attacks and other cyber breaches.
•IP rights arising in both the materials used to train the AI (input) and the results created by the AI (output) needs to be
considered.  Copyright laws as they relate to Generative AI created content are complex and evolving.
•Emerging global AI / Generative AI regulation will impact the way in which Generative AI tools and models are developed and
deployed. Sovereignty of data will impact jurisdictional location and application of Generative AI.
•Current client contracts terms need to be considered, including whether data can be used within a Generative AI system.
Our current contractual positions may need to evolve to enable our Generative AI strategy, as well as the risk considerations
of Generative AI (e.g.: liability, privacy & confidentiality, AI regulations and business continuity)
•Independence considerations for Generative AI activities or services include: contracting and use of external data sets, co -
developed solutions for clients, alliances and marketplace business relationships within ecosystems, conducting labs for
restricted entities and permissible interactions with restricted vendors (e.g. Open AI, Microsoft etc.)
•The internal use of Generative AI and the delivery of Generative AI services to clients are complex and require effective
governance and oversight, including consideration of business value, legal and regulatory obligations, data use, human
intervention, system integrations, and potential for harm.Cyber Security
Intellectual Property (IP)
Regulatory
Client contractual terms
Independence
Governance

## 第 12 頁

©  2024 Deloitte Asia Pacific Services Limited 12 AP Generative AI QRM Guidelines
Use Case Evaluation and Approval Process

## 第 13 頁

©  2024 Deloitte Asia Pacific Services Limited 13 AP Generative AI QRM Guidelines Generative AI Risk Triage Process
Examples of High or Medium -Risk use case:
•Use Gen AI to develop a Deloitte service offering
•Use Gen AI (including Deloitte approved Gen AI Platforms2) to enhance existing or new
client engagement delivery beyond the guardrail or prescribed permitted use
•Apply  Gen AI solution developed from experiment or non -production to a client service
or Deloitte internal useClient or
Production
useApproval by PF:
AI Trust Council/ Public
Interest Committee/
RRL before proceed*Gen AI Use Case
Deep -Dive process
with relevant SMEs
(See slide 16 )RISK TRIAGE
Does the use case have any
high -risk or medium -risk
indicators?
(Refer to next 2 slides for k ey
considerations from Threshold
Risk Assessment Form )
Examples of Low -Risk use cases:
•Experiment with Gen AI to learn more about its capabilities
•Use Gen AI to develop a Proof -of-Concept (PoC) for a client within the client’s environment
•Use Gen AI to explore the way to transform Deloitte works internally
•Use Gen AI to develop or support development of a bid response or proposal (e.g. to generate
content for a presentation)Experiment
or Non -
Production
UseApproved
Use Case1 “YES” to
High -risk
indicator
“YES” to
Medium -risk
indicatorHIGH -RISK
MEDIUM -RISK
LOW -RISKFollow relevant
“Conditions of use ”
1. A use case to be developed and endorsed by local GAI Clearing House/ Market Incubator before further proceeding of risk
evaluation.
2. For Deloitte approved Gen AI Platform, please refer to AI Genesis  and contact your local ITS.Approval requirements:
• In the event of “Yes” response questions with high -risk indicator (i.e. red), the
business case is required to be endorsed by local AI Trust Council/ Public
Interest Committee/ RRL before further proceeding.
• In the event of more than 1 “Yes”  response to questions with medium -risk
indicator (i.e. yellow ), it should be considered whether the overall risk level
may be escalated to high -risk.
• For both high -risk and medium -risk use case, a Gen AI Use Case Review Form
should be completed as submission to relevant SMEs for further review and
evaluation (after business case is approved by GAI Clearing house or Market
incubator).NO “High -risk”
or “Medium -
risk” indicatorsStart
here!
*Approval may require an accelerated deep -dive process with relevant BU QRM and SMEs to inform the “go/no -go” decisionIn order to enable speed to the market, a Generative AI risk triage process is adopted to stratify use cases into different r isklevels with corresponding
evaluation process, if any as follows:

## 第 14 頁

©  2024 Deloitte Asia Pacific Services Limited 14 AP Generative AI QRM Guidelines Generative AI Use Cases Threshold Risk Assessment Form (1/2)
Overall Rating according to the Threshold Risk Assessment Form (please refer to the next page) :  High   / Medium   / Low  
The form should be completed by the proposed Gen AI use case team and reviewed by designated BU QRM point of contact (see “Contact and Reference” section).
The approval requirements are:
• In the event of “Yes” response to questions with high -risk indicator (i.e. red), the business case is required to be endorsed by local AI Trust Council/ Public Interest Committee/
RRL before further proceeding.
• In the event of more than 1 “Yes”  response to questions with medium -risk indicator (i.e. yellow ), it should be considered whether the overall risk level may be escalated to
high -risk.
• For both high -risk and medium -risk use case, a Gen AI Use Case Form should be completed as submission to BU QRM and relevant SMEs for further review and evaluation (after
business case is approved by GAI Clearing house or Market incubator).Basic Project Information
Client Name/
Internal Use Details
Requestor Name
Engagement Lead
Service line / Function
Estimated timeframe
Expected Fee
Short description of the
use case and how
Generative AI is leveraged
to achieve it

## 第 15 頁

©  2024 Deloitte Asia Pacific Services Limited 15 AP Generative AI QRM Guidelines Generative AI Use Cases Threshold Risk Assessment Form (2/2)
Generative AI Triage Risk Questions Yes No Risk-level
1 Does your use case relate to any of the following? (Select all that apply)
•Safety
•Biometric identification or verification
•Critical infrastructure
•AI systems that impact access to service or opportunities (e.g. employment, education, social security, essential services su ch as healthcare)
•Justice/law enforcement
•Immigration
•Clinical decision making
•Asystematic monitoring of a publicly accessible area, such as video surveillance data and network behavioral tracking ⚫
2 Is there a potential regulatory impact, public scrutiny, and/or litigation that needs to be considered as part of your use -case?  ⚫
3 Is our client operating in a heavily regulated industry (e.g. financial services, healthcare and pharmaceuticals, aerospace & defence, etc) and the use case
involve the use of client’s confidential data? (i.e. you’re operating in a heavily regulated industry; your use case involves transferring confidential/sensitive data
outside of your jurisdiction)? ⚫
4 Does your use case relate to any of the following purpose?
•Use Gen AI to develop a Deloitte service offering
•Use Gen AI (including Deloitte approved Gen AI Platforms*) to enhance existing or new client engagement delivery beyond the g uardrails or permitted use
•Apply Gen AI solution developed from experiment or non -production to a client service ⚫
5 Is the Gen AI application applied in your use case a non -publicly available source of Gen AI tool or new application which not i n the list of Deloitte or your local
firm’s approved Gen AI platforms*?  If the answer is “yes”, who is the provider/subscriber of the Gen AI application?  ⚫
6 Does any of the following apply?
•The Gen AI application/system will integrate with other systems containing confidential/sensitive data, or personal informati on
•The Gen AI application/system will be used by public directly, e.g. an online consumer -facing platform
•There is an element of automated decision making (even if there is human review following the automated decision process)
•There is decision to be made in reliance on the output of the application
•The use case relates to children or vulnerable groups (either from a data input perspective or as the beneficiaries)?
•We are scraping publicly available data to generate or obtain insights
•We are training/fine -tuning a Large Language Model (LLM) ⚫
7 Are you required to handle client’s confidential data within Deloitte’s network environment?  ⚫
8 Are you required to keep any client’s confidential data within Deloitte’s storage repository during the course you rendering your services?  ⚫⚫ High / ⚫ Medium
* For Deloitte approved Gen AI Platform, please refer to AI Genesis  and contact your local ITS.

## 第 16 頁

©  2024 Deloitte Asia Pacific Services Limited 16 AP Generative AI QRM Guidelines Generative AI Use Case Deep -Dive Approval Process (for Medium/High Risk Use Cases)
PHASE Identify Consult DeploymentKey ActivitiesUse Case Submission and Risk Triage
•Engagement partner/LCSP and team contacts GAI
Clearinghouse or Market Incubator Team or BU
QRM and submits idea into the Threshold Risk
Assessment Form and pass to GAI Clearinghouse/
Market Incubator Team for risk triage process .
•The GAI Clearinghouse/ Market Incubator team
would involve BU QRM to review the Threshold
Risk Assessment form and inform engagement
team which risk level of the use case is in (High/
Medium/ Low) according to the triage.
•If the use case is Low risk level, team can proceed
by following the “ Conditions of Use (for Low Risk
Use Cases) ”
•If the use case is in High/ Medium risk level, use
case has to be further prepared and endorsed by
GAI Clearinghouse/Market Incubator team, before
further deep -dive evaluation process.Deep -Dive Risk Consultation
•After the use case is endorsed, engagement team
should complete a Gen AI Use Case Review Form
and submit to BU QRM.
•BU QRM perform a deep -dive risk discovery (see
next two slides for discovery considerations )
(together with other enabling teams including the
GAI Clearinghouse/ Market Incubator, OGC, BSO,
and Independence) to discuss risk considerations
including mitigations and plan how to best provide
support.Risk Support
•Prior to bringing a solution to real -world
production and live data use, the engagement
team must complete all applicable GAI testing
and evaluation.
•Standard BU QRM processes including DRB, O2E
applies to GAI engagements.
•Engagement team conducts continuous
monitoring and application validation throughout
the project lifecycle and escalation to BU QRM as
necessary.Tools/
TemplatesThreshold Risk Assessment Form
Threshold Risk Assessment Form.docxGen AI Use Case Review Form
Generative AI Use Case Review Form.docxN/A
Note:  If the Generative AI development involves an asset, the above approval process should be embedded as part of the overa ll Asset Certification process .

## 第 17 頁

©  2024 Deloitte Asia Pacific Services Limited 17 AP Generative AI QRM Guidelines
Note:  The above considerations are not exhaustive.Category Considerations
User Input There are some dependencies around how Generative AI is used as the outputs generated are based on the inputs provided. This means that outputs
generated in the Generative AI tool including recommendations may not be comprehensive and/or incorrect if the user does not prompt correctly. It may
not be able to answer questions that are worded in a specific way and may require rewording or refining of the input to under stand the ask.
Quality &
MisinformationGenerative AI could produce biased content that leads to misinformation and ‘fake news’. It is more difficult to know the source and provenance of
information as there may be a lack of quality in the responses it delivers which can lead to challenges in gaining public tru st.Some industries (Finance,
Legal, Education, Medical) may not be ready to use Generative AI and carry ‘a greater than normal risk’ of potential harm from misleading or incorrect
Outputs.
Ethics Consider ethical responsibilities when using Generative AI tools such as Generative AI. Outputs generated using Generative AI could be perceived as your
‘own work’ which is considered unethical and can have serious implications on reputation and brand (yours and the Deloitte's). It is important that there is
transparency around the use of Generative AI.
Copyright
InfringementMisuse of Generative AI can open the door to copyright and cheating. It can promote new kinds of plagiarism that ignore the rights of original conten t
creators. Gen AI can write an essay within seconds, making it easier for students to cheat, or avoid the process of learning. Such concerns have lead to
educational institutions banning or blocking the use of user access to G enerative AI tools.
Data Limitation Some systems (e.g. ChatGPT) limit AI training data to no later than 2021, so the Gen AI system may have limited knowledge of more recent information
and not be able to generate responses utilising data since then. Outputs, answers, and recommendations are therefore not comp rehensive and may not
include the latest on -point matters/inputs.
Misuse with intent to
cause harmWhilst AI models are programmed to avoid specific content type that is harmful or illegal, such as text containing violence, explicit sex, or other potentially
harmful content, there is potential for G en AI to unintentionally generate such content. Generative AI systems need to detect harmful or offensive content
and need to be constantly monitored in order to be able to detect such content, so it does not produce such output.
Information Security Generative AI is opening the door to virtually infinite new ways of carrying out online crime as it could impersonate people formore social engineering
cyber -attacks. It can also produce a higher quality of phishing emails than most hackers send today which are usually recognisab le by the spelling and
grammar errors they contain. In addition, there is the risk of harmful misuse of personal data including data leaks since AI models are trained on large
datasets.Generative AI Risk Discovery Considerations

## 第 18 頁

©  2024 Deloitte Asia Pacific Services Limited 18 AP Generative AI QRM Guidelines
Generative AI Risk Discovery Considerations (continued)
Note:  The above considerations are not exhaustive.Category Considerations
Data Privacy & Client
ConfidentialityConfidential Data (that is internal to Deloitte or the client) could invertedly be uploaded into publicly available Generativ e AI tools. This could have serious
consequences including; data privacy regulatory breaches, breaches in our client and third -party contractual obligations, and br eaches of Deloitte
confidentiality policies, and therefore may pose serious reputational, regulatory, and financial risks to the firm.
IP Infringement Because Generative AI is a large language model that has been trained on a number of different datasets, there is a possibili ty that the responses
generated from those datasets could infringe on already established works. Using copyrighted material to train the AI model c an cause that model to
excessively draw from another’s work, when providing a response to a user, which could lend itself to IP infringement claims.
Independence There may be some independence implications regarding the use of Generative AI. For example, due to Microsoft’s significant i nvestment, OpenAI is now
subject to the same marketplace independence guidelines and policies as Microsoft. As Microsoft’s Independent Auditor, the in dependence rules of the
SEC and other regulatory and oversight bodies apply to this relationship. Deloitte can provide OpenAI -related technology service s to its clients and interact
with OpenAI and perform certain activities provided that it meets the terms and conditions that are acceptable when describin g the relationship between
OpenAI and Deloitte.
Ability to Replace
Human IntelligenceConcerns about AI Chatbots ability to replace human intelligence may be justified. The chatbot can write an article on a topi c, efficiently eliminating the
need for a human writer, with the potential to make some jobs or specific roles redundant as Generative AI rapidly evolves.

## 第 19 頁

©  2024 Deloitte Asia Pacific Services Limited 19 AP Generative AI QRM Guidelines
©  2024 Deloitte Asia Pacific Services LimitedConditions of Use (for Low Risk Use Cases)
We encourage all Deloitte personnel to familiarize themselves with this emerging technology. However, Generative AI
is a rapidly evolving technology that we strongly advise you to follow the below Conditions of Use when you use
publicly available and/or Deloitte -developed Generative AI tools, even if it’s merely for experiment or non -production use
purpose. CONDITIONS OF USE
2. Betransparent about the use of generative AI in your work. You must record your sources for any output generated using Generative AI tools.
4. You must act in accordance with our Global Principles of Business Conduct as well as this Guidelines (including Trustworthy AI ) and consider your ethical responsibilities when
using generative AI tools.
7. In case there is any change in the use case that warrants additional risk assessment, you should consult with your BU QRM point of contact to initiate the re -evaluation.3. You must fact check the information you receive when using Generative AI  tools and be mindful that 'hallucination' in AI chatbots can lead to the generation of incorrect
answers. It is expected that appropriate “human -in-the-loop” and review is carried out on any outputs from Generative AI tools t o ensure a suitable quality to be used in
connection with your use case. 1. You must follow the Generative AI data guardrail (by Data Type | by Persona ) for the use of the information.  As a general rule, we are only permitted to use client confidential
information for the purpose of delivering the engagement for which that information was provided (check the client contract t o identify any restrictions on the use of client
confidential information) and personal information for the purpose (s) communicated to the individuals for which the information was collected .
Publicly available generative AI platforms or tools (e.g., Chat GPT) are not approved for use in client or internal works, as  they do not have the security and confidentiality
standards that we require. Deloitte or client confidential information should not be put into such platforms or tools to generate idea.
5. You must notuseGenerative AItools tocreate work products using information from different clients (e.g. do not reference documents related to different clients for
summarization in a single document). Deloitte is required to keep the confidential information of different clients separate.
6. You must be mindful and adhere to any local relevant laws and regulations in related to data in your jurisdiction. Some countries may have specific requirements on data
residency and prohibited certain data to transmit outside of its local jurisdiction. If you are unsure, check your contract w ith your client and consult with your BU QRM team.
We encourage all Deloitte personnel to familiarize themselves with this Generative AI technology. However, Generative AI
is a rapidly evolving technology that we strongly advise you to follow the below Conditions of Use for low risk use cases, even if it’s merely for
experiment or non -production use purpose.

## 第 20 頁

©  2024 Deloitte Asia Pacific Services Limited 20 AP Generative AI QRM Guidelines
Generative AI Data Guardrails - By Data Type

## 第 21 頁

©  2024 Deloitte Asia Pacific Services Limited 21 AP Generative AI QRM Guidelines Generative AI Data Guardrails: What is and is not permissible
Data type Examples of data that are permitted to be used in an
appropriate Generative AI tool*Examples of data NOT permitted to be used Key QRM considerations
Deloitte
data•Any information available on Deloitte’s external website
•Deloitte employment opportunity listings
•Deloitte developed templates and proposals, quals, and
approaches (must exclude all Client Information)
•Deloitte policies, procedures and guidance documents
•Internal Deloitte business requirements/code
•Deloitte training materials•Employee personal information without specific
approval from Quality & Risk and Talent.
•Materials in which Deloitte owns the intellectual
property but which contain client or third -party
confidential information.•Does Deloitte own the intellectual property in, or have a license
to use, the materials in connection with the Generative AI use
case?
•Does the data include client or third -party names or other client
confidential information?
•If the data is personal information, have we considered our
obligations under the relevant privacy laws and regulations?
Client
Information1•[Client Information where we have consent from the client in
writing to use the information for the specific purpose related
to Generative AI.]
•Client deliverables or workpapers that were created in the
course of an engagement performed on Deloitte’s standard
T&Cs (without amendment) and which have been cleansed of
all Client Information (similar to KX materials).
•Client Information that was provided to Deloitte under
Deloitte’s standard T&Cs (without amendment) and that has
been aggregated to a point where it is not possible to identify
the client that the information relates to and the ultimate
purpose of using the information is for research or to deliver
further advice (e.g., an advisory engagement).2•Client Information not on the “Examples of data
approved list” or that otherwise has been
specifically approved for use in connection with
Generative AI by Q&R (in consultation with the
LCSP and OGC as appropriate).
•Information in which the client owns the IP
(including Deloitte deliverables in which the
client owns the IP).
•Client Information that has be aggregated to a
point where the client is identifiable.
•Client information containing personal
information. •Client Information should only be hosted and processed within a
Deloitte approved secure environment (for an up to date
approved secure environment, please contact ITS or
Clearinghouse.
•What does our contract with the client say about how Deloitte is
permitted to use Client Information and who owns the IP in any
deliverables? Is the proposed use consistent with those terms?
•Even where we have the rights to use the client information, do
we think that the client would be comfortable with our proposed
use case and/or should we inform the client?
•Are there any restrictions on the data being transferred outside
the local jurisdiction or geography (note some Generative AI
models are not hosted within local jurisdiction)?
Personal
Information3•Personal Information, where the processing is consistent with
the purpose(s) for which the information was collected (i.e.
the purpose communicated to the individuals to whom the
information relates).
•[It may be permissible to process personal information for a
secondary purpose] where express consent has been obtained
from the individuals whose personal information will be
processed by, or in connection with, the AI. •All Personal Information where the proposed
purpose/s for which the information will be
processed by, or in connection with, the AI is
incompatible with the purpose for which the
Personal Information was originally collected. •Is the purpose for which the information will be processed by, or
in connection with, the AI compatible with original purpose for
which the information was collected?
•If consent has been obtained in relation to the proposed
secondary processing, does that consent meet the requirements
for valid consent under the relevant privacy laws and regulations?

## 第 22 頁

©  2024 Deloitte Asia Pacific Services Limited 22 AP Generative AI QRM Guidelines Generative AI Data Guardrails: What is and is not permissible (continued)
Data type Examples of data that are permitted to be used in an
appropriate Generative AI tool*Examples of data NOT permitted to be used Key QRM considerations
Publicly
available
data•Information (open source or proprietary) that is not protected
by trademark, copyright, license terms or other specific terms
of use that restrict the purposes for which the information can
be used.
•Information protected by trademark, copyright or specific use
terms (e.g., website terms) for which consent to use has been
obtained.•Information (open source or proprietary)
protected by trademark or copyright or
governed by specific license terms that restrict
the way in which the information can be used,
and for which consent to use has not been
obtained.
•Data obtained from web scraping.4
•Publicly available personal information and
specific approval has not been obtained from
Quality & Risk in consultation with OGC.•Is the data source subject to copyright or trademark?
•Are there terms of use limiting Deloitte’s ability to use the
information?
•Is data being obtained from web scraping?
•Is the collection, use or disclosure of the personal information
consistent with the relevant privacy laws and regulations?
Purchased
third party
Data•Third party data that has been procured or purchased from a
third party for Deloitte use and the third -party contract allows
for the requested generative AI data use case (consistent with
any contract terms). •Other third -party data, unless specific approval
has been obtained from Quality & Risk.•Does the procured data include any use restrictions (e.g., AI use,
combination with other data sets)?
•Is the source data subject to click -through license terms or other
online terms of use?
Synthetic/
Dummy data•Fake or mock data generated as a substitute for live or real
data. Synthetic data does NOT include any real data,
development or production environment data, including
confidential or personal information.•Third party proprietary data that has been
anonymized without owner consent to do so.
•Anonymized data is not the same as synthetic
data.•Is the data fake data and not simply anonymized?
•Consent may be needed before anonymizing any confidential or
personal information for secondary purposes.
*Deloitte and Client information must not be inputted into publicly available generative AI tools (including Chat GPT, Google Bard and Bing Chat or other locally available Chatbot ). For more information on appropriate use of generative AI
tools, contact the local Clearinghouse or Market Incubator teams.
1 “Client Information” refers as:  “any information, documents, materials, facts, instructions or Confidential Information provided to us by or on behalf of the client.  Client Information (also called ‘Client Data’) may be defined differently
under specific client agreements.
2 An example of a use where the purpose is not for research or to deliver further advice might be where the information is being used to develop a product (i.e., an asset) that we intend to sell to a client.
3 Personal Information is information about an individual or from which an individual is able to be identified. The processing of personal information by Deloitte, including personal information that is publicly available, is regulated by the
relevant privacy laws and regulations. Personal Information includes both  client and Deloitte Personal Information.
4 As well as the potential to be inconsistent with website terms of use, web -scraping raises ethical risk that need to be consider ed.

## 第 23 頁

©  2024 Deloitte Asia Pacific Services Limited 23 AP Generative AI QRM Guidelines
Generative AI Data Guardrails -By Persona

## 第 24 頁

©  2024 Deloitte Asia Pacific Services Limited 24 AP Generative AI QRM Guidelines
I am a Deloitte Practitioner seeking to use
Deloitte approved AI Systems ...
Upload Deloitte or client confidential1 information into Deloitte approved AI
Systems (subject to any restrictions in my client contract).
Familiarise myself with any AI Systems’ specific usage guidelines or terms of use
and comply with any additional usage guidelines from my business unit (e.g. A&A).
Review and fact check any outputs (having reference to the source documents)
and be mindful that AI Systems may generate errors or make up information
(called 'hallucinations’).
Ensure that my work is reviewed by a senior Deloitte professional before it is
incorporated into any client work or deliverable and make the reviewer aware of
how I used AI Systems.
Be transparent about my use of AI outputs in my work, especially when
communicating with clients, and will consult with my Quality & Risk team where I
am unsure of the appropriate level of transparency.
Notify people before using an AI system to capture or record their words or
actions (e.g. an AI System to summarise a meeting).
Always act in accordance with our Global Principles of Business Conduct and
consider my ethical responsibilities when using AI Systems, consulting my Quality
& Risk team where I am unsure.
Proactively educate myself on the limitations of AI Systems and best practices, and
routinely revisit these guidelines to help ensure my use of AI Systems is both
effective and responsible.Upload any client confidential1 information into a Deloitte approved AI system if
doing so would violate any client contractual commitments,
unless specifically authorised by the client. Such contractual restrictions may
include:
•Using client confidential information for a secondary purpose
(e.g. a purpose other than the delivery of the engagement for
which the client provided the information to Deloitte);
•General restrictions on the use of AI Systems; and
•Restrictions on transferring or storing client confidential
information offshore (unless I have confirmed that the AI System
is hosted in local geography).
Use AI systems to answer questions which require consultation with leadership or
subject matter specialists (i.e., Legal, Risk, Independence, etc.).
Attempt to pass off content generated by AI Systems as my own.
Use an AI System to infer or generate personal information about a person .I will I will not
1 Confidential information means data not known to the public that relates to our business or that we receive in the course of
business from other Deloitte personnel, our clients, or third parties. Client Confidential Information includes but may not b e
limited to information that has any of the following characteristics: (a) It is not known by or available to the general publ ic; (b) It
is not useless or trivial but is capable of harming the Client’s or third parties’ interests if used or disclosed; (c) It has  been
communicated in circumstances establishing an obligation of confidence; (d) It otherwise cannot be disclosed because of Local
Laws.so that I can enhance how I work and perform in my role .

## 第 25 頁

©  2024 Deloitte Asia Pacific Services Limited 25 AP Generative AI QRM Guidelines
Data Guidance for Deloitte Practitioners using Deloitte approved AI Systems
The following data ispermitted to be used.
Deloitte confidential information
Deloitte information that you have access to (provided you
respect any restrictions on how it can be used), including:
•Deloitte information accessible on the public internet (e.g.
D.com, employment listings).
•Internal policies, procedures and guidance documents.
•Training materials & templates.
Client data
Client information provided that your use of that information
is consistent with any restrictions imposed on Deloitte in our
agreement with the client (as a general rule we are only
permitted to use client confidential information for the
purpose of the engagement for which that information was
provided).
Publicly available data (without restrictions)
Publicly available information (open source or proprietary)
that is not protected by trademark, copyright, license terms
or other specific terms of use that restrict the purposes for
which the information can be used.Use of Personal Information in line with the primary purpose
of collection
Personal information where the use is consistent with the
purpose for which the information was originally collected
and for which you were given access (i.e. the purpose
communicated to the individuals to whom the information
relates). Extra caution should be exercised whenever handling
personal information. If you want to use personal information
for a secondary purpose, you must consult with your Quality
& Risk team.
Other types of non -confidential or
generic data
Data that does not contain any specific information or
personally identifiable details about individuals or clients. It is
typically general or non -specific data that can be used for
various purposes without compromising privacy or
confidentiality.
Anonymised data
Anonymised data includes real data that has been masked,
scrambled or otherwise deidentified.Synthetic data
Fake or mock data generated as a substitute for
production/real data.
Guiding Questions
To ensure compliance, please consider the
following:
1. What type of data am I including in my
prompt?
2. Where did I get that data from?
3. Am I confident that I am permitted to use the
data for this purpose?

## 第 26 頁

©  2024 Deloitte Asia Pacific Services Limited 26 AP Generative AI QRM Guidelines 26
Data Guidance for Deloitte Practitioners using Deloitte approved AI Systems
The following data isn’t permitted to be used.
Deloitte confidential information
Employee personal information without consultation with
your Quality & Risk team.
Client data
Client confidential information or intellectual property for a
purpose other than the delivery of  the engagement for
which it was provided for, or in a way that is inconsistent
with any restrictions imposed by the client (unless we have
written consent from the client). If unsure consult with
your Quality & Risk team. Publicly available data (with restrictions)
Information (open source or proprietary) protected by
trademark, copyright, license terms or other specific terms
of use that restrict the purposes for which the information
can be used, where are proposed use is inconsistent with
those restrictions.
Use of Personal Information for a secondary purpose
•Personal information is only permitted to be used for
the purpose for which it was originally collected (i.e. the
purpose communicated to the individuals to whom the
information relates). If unsure consult with your Quality
& Risk team.
•Publicly available personal information obtained from
the internet (e.g. Social media profiles) where
consultation with your Quality & Risk team has not
occurred.Data obtained by web scraping
As well as the potential to be inconsistent with website
terms of use, web -scraping raises ethical risks that need to
be considered. Data scraped from the public internet
should not be used without further consultation with your
Quality & Risk team.
Data with residency restrictions (i.e. data which must
reside in local geography)
Data that is subject to restrictions on the location where
that data can be stored or processed. You must first consult
your Quality & Risk team before uploading such data into
an AI System.

## 第 27 頁

©  2024 Deloitte Asia Pacific Services Limited 27 AP Generative AI QRM Guidelines
27
Are Deloitte approved AI Systems secure enough to use with
client/Deloitte documents?
Yes, approved AI Systems are reviewed and approved for use in
connection with client/Deloitte documents. However, you must
ensure that you have the appropriate rights to use the
client/Deloitte documents (see the Guidelines on the previous
slides).
Do I need to tell anyone I'm using Deloitte approved AI Systems?
E.g. my manager? Partner? Client?
You must be open and transparent with your engagement team
and client about your use of AI Systems. When deciding whether
the client needs to be informed about your use of an AI System,
you and your engagement team should consider the client’s
expectation, your contract with the client and the extent of your
use of AI in your work. Incidental use (such as using a generative
AI chatbot to help reword a sentence or for early -stage research)
may not need to be disclosed.
Can I use Deloitte approved AI Systems to help me answer legal,
regulatory, and similar questions?
No, it is essential that questions requiring specialist knowledge are
answered by the appropriate professional. This is due to the
potential significant impact of providing incorrect answers.I’m currently working for Client A. Can I use documents which
came from Client B using an approved AI System for use as part
of my work with Client A?
No is the starting point. However, the answer will depend on
whether the documents contain confidential information or
intellectual property that belongs to Client B. If the answer to both
those questions is that Deloitte owns the intellectual property and
that all client confidential information has been cleansed
appropriately (in consultation with your Quality & Risk team or the
LCSP, as appropriate), then it may be permissible to use the
document for Client A. The rule you should always follow is ‘if
unsure, consult!’FAQs for Deloitte Practitioners using Deloitte approved AI Systems
What are some examples of permitted uses of
Deloitte approved AI Systems?
* IMPORTANT * These examples must follow
the guidelines.
•Summarising Deloitte internal meeting
transcripts with the permission of
participants in the meeting.
•Drafting internal communications to post on
intranet.
•Generating a first draft a document, or
propose alternative wording in an existing
document, which I then review and edit.

## 第 28 頁

©  2024 Deloitte Asia Pacific Services Limited 28 AP Generative AI QRM Guidelines
I am a Deloitte Practitioner seeking to use
Publicly Available AI Systems ...
Review and fact check any outputs (having reference to the source documents)
and be mindful that AI Systems may generate errors or make up information
(called 'hallucinations’).
Ensure that my work is reviewed by a senior Deloitte professional before it is
incorporated into any Deloitte or client work or deliverable and make the
reviewer aware of how I used AI Systems.
Be transparent about my use of AI outputs in my work, especially when
communicating with clients, and will consult with my Quality & Risk team
where I am unsure of the appropriate level of transparency.
Review the terms of use for any Deloitte non -approved (e.g. publicly available)
AI Systems to ensure that they are permitted to be used for commercial
purposes and that Deloitte will own any outputs of the AI System.
Always act in accordance with our Global Principles of Business Conduct and
consider my ethical responsibilities when using AI Systems, consulting my
Quality & Risk team where I am unsure.
Proactively educate myself on the limitations of AI Systems and best practices,
and routinely revisit these guidelines to help ensure my use of AI Systems is
both effective and responsible.Upload any Deloitte or client confidential1 information or personal
information into Deloitte non -approved (e.g. publicly available) AI
Systems.
Use AI Systems to answer questions which require consultation with
leadership or subject matter specialists (i.e. Legal, Risk,
Independence, etc.)
Attempt to pass off content generated solely by AI Systems as my
own.
Use any AI System if such use would violate any contractual
commitment with a client, unless specifically authorised by the client. I will I will notthat have not been approved by Deloitte so that I can enhance how I work and perform in my role .
1 Confidential information means data not known to the public that relates to our business or that we receive in the course of business
from other Deloitte personnel, our clients, or third parties. Client Confidential Information includes but may not be limited  to
information that has any of the following characteristics: (a) It is not known by or available to the general public; (b) It is not useless or
trivial but is capable of harming the Client’s or third parties’ interests if used or disclosed; (c) It has been communicated  in circumstances
establishing an obligation of confidence; (d) It otherwise cannot be disclosed because of Local Laws.

## 第 29 頁

©  2024 Deloitte Asia Pacific Services Limited 29 AP Generative AI QRM Guidelines
Data Guidance for Deloitte Practitioners using Publicly Available AI Systems
The following data ispermitted to be used.
Publicly available data (without restrictions)
Publicly available information (open source or proprietary) that is not
protected by trademark, copyright, licence terms or other specific terms of
use that restrict the purposes for which the information can be used.
Non -confidential1 (e.g. publicly available) Deloitte data
Deloitte information accessible on the public internet (e.g. Deloitte’s external
website - D.com).
Synthetic data
Fake or mock data generated as a substitute for production/real data.
Synthetic data does not include anonymised data.
Other types of non -confidential1 or generic data
Data that does not contain any specific information or personally identifiable
details about individuals or clients. It is typically general or non -specific data
that can be used for various purposes without compromising privacy or
confidentiality.Guiding Questions
To ensure compliance, please consider
the following:
1. What type of data am I including in my prompt?
2. Where did I get that data from?
3. Am I confident that I am permitted to use the
data for this purpose?

## 第 30 頁

©  2024 Deloitte Asia Pacific Services Limited 30 AP Generative AI QRM Guidelines 30
Data Guidance for Deloitte Practitioners using Publicly Available AI Systems
The following data isn’t permitted to be used.
1 Confidential information means data not known to the
public that relates to our business or that we receive in the
course of business from other Deloitte personnel, our clients,
or third parties. Client Confidential Information includes but
may not be limited to information that has any of the
following characteristics: (a) It is not known by or available to
the general public; (b) It is not useless or trivial but is capable
of harming the Client’s or third parties’ interests if used or
disclosed; (c) It has been communicated in circumstances
establishing an obligation of confidence; (d) It otherwise
cannot be disclosed because of Local Laws.Client and Deloitte confidential information1
Do not upload client or Deloitte confidential information1
into publicly available AI Systems. This includes any
information with client / Deloitte’s names in it or
information which is not publicly available.
Personal information
Personal information must not be uploaded to publicly
available AI systems.
•Do not use AI Systems to infer or generate personal
information about a person.
•This includes publicly available Personal information
obtained from the internet (e.g. Social media
profiles) where consultation with your Quality & Risk
team has not occurred.
Publicly available data (with restrictions)
Information (open source or proprietary) protected by
trademark, copyright, license terms or other specific terms
of use that restrict the purposes for which the information
can be used.
•Read any licence terms that attach to the data before
using it in connection with AI Systems.Data obtained by web scraping
As well as the potential to be inconsistent with website
terms of use, web -scraping raises ethical risks that need to
be considered. Data scraped from the public internet
should not be used without further consultation with your
Quality & Risk team.
Anonymised data
Anonymised data includes real data that has been masked,
scrambled or otherwise deidentified. Removing names,
addresses, and other personal information from data
doesn't guarantee its safety for use.
Purchased third party data
Third party data that has been procured or purchased from
a third party for Deloitte use. Specific consultation with
your Quality & Risk team is required (with reference to the
contract with the third party).

## 第 31 頁

©  2024 Deloitte Asia Pacific Services Limited 31 AP Generative AI QRM Guidelines
31
What is a Deloitte Publicly Available ( non-approved) AI System?
Non-approved AI Systems include AI Systems I have signed up to
using my Deloitte or personal email address on vendor click
through terms which have not been approved by Deloitte. This
can include both free and paid subscriptions. (e.g. I am wanting to
use ChatGPT.)
Are non -approved AI Systems secure enough to use with client
documents?
No, Deloitte non -approved AI Systems have not undergone
rigorous security assessments to ensure they meet the required
standards for handling client confidential information securely.
Do I need to tell anyone I’m using an AI System? E.g., my
manager? Partner? Client?
You must be open and transparent with your engagement team
and client about your use of AI Systems. When deciding whether
the client needs to be informed about your use of an AI System,
you and your engagement team should consider the client’s
expectation, your contract with the client and the extent of your
use of AI in your work. Incidental use (such as using a generative
AI chatbot to help reword a sentence or for early -stage research)
may not need to be disclosed.
Can I use Deloitte documents with a Deloitte non -approved AI
System?
If the Deloitte documents contain Deloitte confidential information (i.e. information about Deloitte’s business that is not
public) they cannot be inputted into non -approved AI Systems.
Non-approved AI Systems should never be used with Deloitte
confidential information, as they have not undergone rigorous
security assessments to ensure they meet required security
standards.
Can I use Deloitte non -approved AI Systems to help me answer
legal, regulatory, and similar questions?
No, it is essential that questions requiring specialist knowledge are
answered by the appropriate professional. This is due to the
potential significant impact of providing incorrect answers or
acting in reliance of those answers
Do Deloitte non -approved AI Systems process data offshore?
In most cases yes, if you have concerns about whether this is
appropriate in the context of how you want to use the AI System
consult with your Quality & Risk team.
FAQs for Deloitte Practitioners using Publicly Available AI Systems
What are some examples of permitted uses of
non-approved publicly available AI Systems?
* IMPORTANT * These examples must follow
the guidelines and must not involve any client
or Deloitte confidential information.
•Summarising publicly available materials for
internal use using Chat GPT. E.g. Prompt -
“Please summarise this news article from
the Wall Street Journal”
•Brainstorming or generating ideas using
Miro Assisted AI. E.g. Prompt: - “Brainstorm
ideas about a AI Induction Lab”
•Helping to suggest suitable content for a
news article using Grammarly AI Writing
Assistance. E.g. Prompt: - “Make the tone of
this generic news article about climate
month more friendly”.

## 第 32 頁

©  2024 Deloitte Asia Pacific Services Limited 32 AP Generative AI QRM Guidelines
Generative AI Contracting Considerations

## 第 33 頁

©  2024 Deloitte Asia Pacific Services Limited 33 AP Generative AI QRM Guidelines
©  2024 Deloitte Asia Pacific Services LimitedGenerative AI Contracting Considerations (1/4)
Area Issues/Considerations Description of Issues and Considerations
Definition of
termsAI Systems •Consider the breadth of ‘AI Systems’ (or other similar terminology) as defined in the contract.
•The breadth or narrowness of ‘AI Systems’ (and other similar terms) is important as the restrictions, rights, and obligations under the contract
all tie back to this definition.
Scope of AI systems •There are many back -end systems used at Deloitte that will use AI, some of which might have a tangential connection with client service and
primarily have the function of improving productivity at Deloitte generally.
•We may not be able to oblige with client -imposed restrictions for these back -end systems given the broad use of these systems by the
network.
Customer Data Secondary use of data •Client contracts often restrict the use of any client data to ‘for the purpose of providing the Services’.
•To the extent that there are any planned or anticipated secondary uses of client data by third -party vendors, this should be neg otiated up
front.
Permissions / rights to use data •For an AI system, both prompts and raw data may be ingested.
•Where the client is the provider of the ‘raw data’ input into the AI system then consider whether the client has the requisit e permissions and
authorizations to provide such data.
Intellectual
Property
(see more in the
Generative AI IP
Considerations
section)Ownership of Outputs •Arrangements with Generative AI vendors are a relevant consideration to the ownership of outputs.
•There may be uncertainty around whether the output can be owned and ownership rights can be asserted against third parties.
Ownership of Outputs -
Disclaimer•AI Systems may produce outputs that are the same or similar across multiple users.
•Clients should be made aware of the possibility of similar outputs.
Open -Source
ContentOpen -source or public content
may be provided by the Client•Open -Source content provided by the client may not be captured by “Client Data” definition and therefore, any representations an d
warranties related to “Client Data” may not be sufficiently broad enough to capture the open -source license requirements
Open -source content or
technology may be provided by
Deloitte•Where Deloitte provides open -source content or technology, clients may be sensitive to use of such materials.  In addition, for open -source
content and technology, there are attribution requirements and separate terms governing the use of such open -source materials.The list below illustrates key client contracting issues and considerations in the context of Generative AI. It is not an exh austive list and
should be supplemented with other materials available to your local firm. Please contact your local OGC team for further guid ance.
Note:  The above considerations are not exhaustive.

## 第 34 頁

©  2024 Deloitte Asia Pacific Services Limited 34 AP Generative AI QRM Guidelines
©  2024 Deloitte Asia Pacific Services LimitedGenerative AI Contracting Considerations (2/4)
Area Issues/Considerations Description of Issues and Considerations
Warranties and
IndemnitiesInput •For an AI system, both prompts and raw data may be ingested.
•Consider who is the owner of the ‘raw data’ input into the AI system and the nature of the engagement.
•Consider who is the owner of the ‘prompt’ to the AI system.
•Consider Deloitte’s role in the development of the prompt and the value in the prompt.
•Where using licensed ‘raw data’ provided by a third -party, consideration should be given to any applicable contractual restricti ons
Outputs –Disclaimer of Accuracy •Incomplete or inaccurate prompts may affect the accuracy and completeness of the output.
•AI Systems are subject to hallucinations, which can be caused by a variety of factors outside of Deloitte’s control.
Outputs –Deloitte Obligations -
Disclaimer for Use of Outputs•Typically, vendors do not offer any representation or warranty around the output.
Outputs -Disclaimers for end -users •Consider whether there is a disclaimer that should be included on the Outputs for the attention of the end -user (either as pass through
requirement or a regulatory requirement)
Outputs –Client Obligations -Client
use of outputs•As outputs produced by an AI System may incorporate components of a third party’s intellectual property, there is a risk that the outputs
produced could infringe third -party intellectual property rights.  In addition, a client may use an output produced by an AI sys tem in a way
that is misleading or otherwise has an adverse impact on individuals.
Client
ObligationsCertain regulations have
extraterritorial application•Where an output produced by an AI system is used outside of the geography in which it is produced, foreign regulations may br ingthe AI
system that produced the output within the scope of such regulations.  The use of the output may not have been contemplated b y the
provider/developer of the relevant AI system.
Outputs produced by an AI system
may be used to extract info from the
underlying model•The LLM and associated data underpinning the operation of an AI System may be valuable intellectual property of a developer/p rovider.
There are certain methods that can be utilized to extract information from an AI System that can then be used to effectively reconstruct
the LLM.
Deloitte deliverables could be used
by a client as part of training their
own large language model•Clients may wish to use Deloitte deliverables to train their own large language models which compete with Deloitte’s offering s or produce
outputs that are based on Deloitte’s deliverable but do not reflect the substance of the deliverable or may infringe Deloitte ’s intellectual
property rights in the deliverable.
Current and potential regulations
impose notice requirements
pertaining to AI systems•There are current and potential regulations (e.g. EU AI Act) that have transparency requirements where AI is used so that end users are
aware that outputs produced by an AI system were generated using AI.
The underlying training data of an AI
System can influence the outputs•Deloitte, in its advisory role, cannot necessarily assess the accuracy, completeness, and appropriateness of training data to the same
degree as a client can with respect to its own business needs.
Note:  The above considerations are not exhaustive.

## 第 35 頁

©  2024 Deloitte Asia Pacific Services Limited 35 AP Generative AI QRM Guidelines
©  2024 Deloitte Asia Pacific Services LimitedGenerative AI Contracting Considerations (3/4)
Area Issues/Considerations Description of Issues and Considerations
Fees Client’s may expect that
Deloitte passes through any
cost savings attributable to use
of AI•Given that AI can improve productivity on client service engagements, and could therefore reduce costs, clients may expect th at cost savings are
passed through to them.
Commercials Certain AI systems have
consumption costs associated
with their use•Certain AI Systems have consumption costs associated with their use (e.g. token costs) which, depending on the volume of data being processed,
could become material quite quickly and impact the profitability of the engagement. Whether or not consumption costs should b e passed through
to clients will necessarily depend on whether the anticipated consumption costs have already been built into the rates (e.g. productivity tools that
form part of Deloitte’s general infrastructure and overhead costs), whether there has been a specific procurement for the cli entengagement (at
client’s request) or in connection with a specific offering, and the extent to which the AI tool is used.
Privacy Compliance with data
protection requirements•The underlying client contract may have been prepared at a time where use of Generative AI was not contemplated and therefore the contractual
provisions with respect to data protection may need to be revised to reflect the scope of the engagement.
Security Data residency •In certain cases, it may be necessary to host or process data in specific countries or territories when using AI Systems. For example, there may be
an internal operational requirement (e.g., architecture) or a client request for data to be hosted in the same location where itis collected.
Limitation of
LiabilityThe limitation of liability in the
vendor contract may differ from
the client’s expectation
regarding Deloitte’s liability
AI Systems are still relatively
new and their limitations are
still be identified
Foreign laws may regulate the
use and development of AI
systemsVendor Contracts
•Where third -party AI systems are used, vendors are likely to limit their liability to Deloitte.  Where there is a claim from a c lient in relation to the AI
system, or its outputs, Deloitte may be responsible for any difference between the limitation of liability in the vendor cont ract and the limitation of
liability in the client contract.
Uncertain state of AI
•AI technology is a new and evolving area.  Accordingly, all of the risks in relation to the provision of AI Systems and relat ed services are not yet
known or fully understood.  In certain jurisdictions, given the uncertain state of AI, there is a risk that the contract, or parts thereof, could fail its
essential purpose.
Foreign laws
•There are some laws which have extraterritorial application where there is a nexus with their territory.  In addition, many o f the regulatory regimes
are new and therefore, their application and the associated obligations that Deloitte may be required to comply with uncertai n. Where these
regimes are applicable, MFs and/or clients could be subject to claims from foreign regulators who may interpret or apply the laws in an unexpected
way.
Note:  The above considerations are not exhaustive.

## 第 36 頁

©  2024 Deloitte Asia Pacific Services Limited 36 AP Generative AI QRM Guidelines
©  2024 Deloitte Asia Pacific Services LimitedGenerative AI Contracting Considerations (4/4)
Area Issues/Considerations Description of Issues and Considerations
Compliance with
laws and
regulationsChanges in regulations or laws may
affect Deloitte’s ability to provide
the Services •Given that the regulation of AI systems is evolving across the world, there may be regulations that did not exist at the time ofexecuting
the contract or may be modified after the execution of the contract.  These new or modified regulations could impact the deli very of
services or the provision of the AI system and may necessitate adjustment to the rights and obligations of the parties.
Third Party
ProductsThird party terms may be applicable
to any use of the AI System by the
Client•Where the Client and/or its end -users interact with the AI System used as part of the client engagement, terms of use or a EULA may
need to be passed through to the Client and/or its end -users. This requirement may be prescribed by the third -party vendor, to a ssist
Deloitte in complying with its own regulatory obligations, or to ensure that the Client’s access to and/or use of the AI Syst em does not
result in Deloitte breaching its own contractual obligations to the third -party vendor.
The Client may require Deloitte to
access their instance of an AI System•As part of an advisory engagement, the Client may request that Deloitte access and/or use their own separately licensed insta nceof an AI
System.
Trade Controls
and SanctionsProvision of AI Systems and related
services and release of export -
controlled technology and technical
data to prohibited destinations, end
users, or end uses •Various governments have expressed concern about the development of AI Systems and its impact on their impact on their nation al
security. These national security concerns may lead to the issuance of sanctions and export controls on generative AI Systems and other
related activities in the next few years.
•Deloitte firms are required to comply with applicable sanctions and export controls (“trade controls”), and Deloitte Global h as issued
guidance to the network that Deloitte firms should not use Deloitte Global or Deloitte US technology in any US -sanctioned activi ties.
Deloitte Global has also issued other guidance concerning compliance with specific trade controls, such as Russia -related trade controls.
•In addition, use of AI or other technology in a manner than contravenes applicable trade controls could create legal exposure for Deloitte
firms.
Compliance with
PoliciesClients may require Deloitte’s use of
AI complies with certain policies
which may be onerous or not
possible due to the use of third -
party systems•Given that AI is a new and emerging area, and given the regulation of AI is still evolving, clients may seek to mitigate thei r risk by
requiring Deloitte to comply with their own policies with respect to the use of AI on an engagement.  However, where using th ird-party AI
systems, Deloitte’s ability to ensure that such systems are compliant with client policies may be limited because: (1) the ve ndor contract
has not passed through the obligation to comply with such policies; (2) the use of the AI system spans multiple client engage men ts; (3)
vendors are likely to be unwilling to provide such assurances; and (4) vendors are likely to be unwilling to modify their pro ducts to meet
Deloitte’s (and its clients) specific requirements.
General
ProvisionsForce majeure clauses typically do
not include change of laws or
induced infringement if contract
requires performance despite IP risk.
AI systems may be unstable given
that the technology is new and
evolving.•The regulatory landscape relating to the use of AI is evolving, and AI systems may be unstable and produce hallucinations or other errors.
Where there are significant problems with an AI system, Deloitte may wish to cease providing the services as remedying such p roblems
could be costly and time consuming or may be difficult to identify the source of the instability.
•In addition, given the changing landscape regarding IP, new case law may restrict Deloitte’s ability to deliver services with outbeing
subject to IP infringement claims.
Note:  The above considerations are not exhaustive.

## 第 37 頁

©  2024 Deloitte Asia Pacific Services Limited 37 AP Generative AI QRM Guidelines
Generative AI IP Considerations

## 第 38 頁

©  2024 Deloitte Asia Pacific Services Limited 38 AP Generative AI QRM Guidelines
Generative AI Intellectual Property Considerations
Intellectual property is an area of law that wasn’t designed with Generative AI in mind, and it is going to take a while for thelaw to
catch up. In the meantime, we need to be mindful of these uncertainties when we make decisions.
Question Answer QRM Considerations
What are the IP considerations
when fine -tuning or providing
context to a generative AI model?•It is important that we ensure that we have the rights to use the data in connection
with training or prompting generative AI. This applies even where the information is
publicly available. Make sure you have read any license terms that attach to the data
before using it in connection with generative AI.
•See Generative AI Data Guardrails for more information.•What is the source of the data that Deloitte wants to use to input
into or use to finetune the generative AI model and what
restrictions (if any) are placed on the use of the data? Are the
restrictions consistent with the use of the data for this purpose?
Are the outputs of generative AI
protected by Copyright?•Most generative AI products assign ownership of any IP in the outputs to the end user
(the person inputting the prompt).
•Under current copyright law, a ’work’ (e.g., an image, a piece of writing or software
code) that does not have a human author is notprotected by copyright. And also,
usually there is a term of use / license term related to intellectual properties stated by
the AI services providers that the outputs/deliverables produced by the AI tool belong
to the AI services providers (“Protective Clause”).
•This means that in order to be protected by copyright we must be able to show that
there was a sufficient amount of human effort involved in the creation of the output
and without the Protective Clause embedded in the use of that tool.  •What do the generative AI product’s terms of use / license terms
say about who owns the copyright in any outputs generated?
•Are we using generative AI to create an output in which it is
important that either Deloitte or the client owns the IP (e.g.,
imagery to be used in connection with creating a brand or visual
identity)?
Do the outputs of generative AI
models infringe third party
copyright or other IP rights?•Generative AI tools use complex probability calculations to generate ‘original’ content.
While these tools are not designed to copy the materials on which they were trained,
there is still a chance that all or parts of a copyright work might be reproduced.
•Most generative -AI platforms do not guarantee the accuracy, integrity or quality of the
generated content and generally do not provide any warranty that the content will not
infringe third party intellectual property rights.
•Some providers of generative AI tools (such as GitHub Copilot) have developed filters
which detects code suggestions that reproduce existing content in the training dataset.  •Have we sufficiently modified or added our own ‘intellectual
effort’ to the output to minimize the risk of any third -party IP
infringement?
•Are we using prompts that increase the likelihood that the AI
model will reproduce elements of existing works (for example,
‘create an image in the style of [insert existing artist]’)?
•Have we taken reasonable steps to verify the source of the
information or assess whether the output reproduces existing
works (e.g., scanning code to identify known license terms that
attach to the code)?

## 第 39 頁

©  2024 Deloitte Asia Pacific Services Limited 39 AP Generative AI QRM Guidelines
Generative AI Independence Considerations

## 第 40 頁

©  2024 Deloitte Asia Pacific Services Limited 40 AP Generative AI QRM Guidelines
©  2024 Deloitte Asia Pacific Services LimitedGenerative AI Independence Considerations
Common independence considerations for Generative AI activities or services include:
•Contracting and use of external data sets
•Co-developed solutions or creating commercial solutions for clients
•Alliances and marketplace business relationships within ecosystems
•Permissible interactions with restricted entity vendors (e.g. Microsoft and Open AI)
•Developing and licensing assets
•Conducting Deloitte Greenhouse labs
•Services to restricted entities
The Global Generative AI Independence Considerations could be found here .
Independence guidance already available for AI:
OpenAI/ChatGPT FAQs*
*A global version of this Microsoft/OpenAI FAQs can be accessed here

## 第 41 頁

©  2024 Deloitte Asia Pacific Services Limited 41 AP Generative AI QRM Guidelines
Deloitte’s Trustworthy AI Framework Guidance

## 第 42 頁

©  2024 Deloitte Asia Pacific Services Limited 42 AP Generative AI QRM Guidelines
©  2024 Deloitte Asia Pacific Services LimitedApplying the Trustworthy AI Framework to Generative AI use cases
On each slide, there is an example of a use case scenario,
mapped to one of the  different TAI dimensions . These have been
crafted to enhance comprehension of the framework based on a
real-world scenario. Please note that each use case has one TAI
dimension explored against it, but in reality, many dimensions
will apply to the respective scenarios, and all should be
explored in practice . ILLUSTRATIVE SCENARIO
On each slide, there is a series of Trustworthy AI considerations
relevant to each use case scenario such as goals/benefits,
affected stakeholders, a selection of TAI questions* relevant to
the selected TAI dimension to consider and mitigation strategies.
These considerations are meant to foster a deeper understanding
of the Trustworthy AI  dimensions . TRUSTWORTHY AI CONSIDERATIONS
* Please note that included in this deck are a few TAI questions to consider; however, these questions
are not exhaustive. To see a more comprehensive TAI question guide that can be used as an
additional resource, please click here  for Deloitte Trustworthy AI Questions Guide.The following slides explore the seven dimensions of the Trustworthy AI Framework in practice . Use these slides to explore how to use the framework
at work and in Generative AI use cases. Each following slide is aligned to one of the seven Trustworthy AI dimensions and explores:
Trustworthy AI considerations should be integrated throughout the lifecycle of AI use cases , both internally and in market -facing technology. Please
note, that these considerations serve as guidance and are not exhaustive . They should be viewed as a starting point to align Generative AI use cases
with ethical, legal/regulatory and trustworthy principles for the responsible use of AI.

## 第 43 頁

©  2024 Deloitte Asia Pacific Services Limited 43 AP Generative AI QRM Guidelines PRIVATE SEEING IS BELIEVING
(Virtual Try -On Application)
GOALS / BENEFITS
•Visualize products in real -world settings
•Provide personalized recommendations
•Increase customer engagement and satisfaction improving its
shopping experience
•Reduce  product  returns
•Customers
•Marketing and sales teams
•Operations and logistics teams
•Customer service and support teams
•Data privacy and security regulators
•CompetitorsAFFECTED STAKEHOLDERS“PRIVATE" TAI QUESTIONS* TO CONSIDER INCLUDE
MITIGATION STRATEGIES INCLUDE
•Users are presented with a privacy notice that explains what personal information will be collected, how it could be used, an d obtain explicit
consent from the customer
•Establish user support channels, such as a dedicated helpdesk or live chat, to address concerns that users may encounter whil e using the app
•Provide users with visibility and control over their data. This includes allowing users to access, edit, or delete their data , as well as providing
options to opt -out of certain data processing activities
•Consult with your Deloitte firm Privacy, Confidentiality or OGC teams and follow processes as required.
•Reference Global Data Protection Considerations for Generative AI
TRUSTWORTHY AI CONSIDERATIONS
AUTONOMOUS DISCRETIONAL
•Could the use (or misuse) of the technology shape or influence human
behavior? •Is the use of personal information, in both training and live data,
compatible with the original purpose for which it was
collected/obtained?
CONFIDENTIAL CONSENSUAL
•What measures have been taken to help ensure personal and/or
confidential data is safely and appropriately stored, transferred, and
used? Are these measures aligned with regulatory and contractual
obligations?•Have you consulted with your Deloitte firm Data Privacy team and
adhered to privacy and confidentiality guidance? A major clothing retailer is getting ready to launch a new mobile app which allows customers to see a digital rendering of th eir clothes and other products on their own bodies, in their
homes, and elsewhere to help with online shopping. By analyzing images or videos of the customer, the app can create realisti c representations of how the clothing or product would look
in the real world. Additionally, by considering factors such as body shape, skin tone, and personal style, the app can sugges t suitable products that align with the customer’s preferences.
The app will allow users to create a custom profile with their preferences built in, and it will also allow customers to make  in app purchases from the online store.
* Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To  see a more
comprehensive TAI question guide that can be used as an additional resource, please click here  for Deloitte Trustworthy AI Questions Guide.

## 第 44 頁

©  2024 Deloitte Asia Pacific Services Limited 44 AP Generative AI QRM Guidelines TRANSPARENT AND EXPLAINABLE
A retail banking company, in its continuous pursuit of improving client experience and providing customers with multiple meth ods of interacting with their accounts, services, and
offerings, has implemented a "Virtual Reality (VR) Customer Agents" Program. This program enables customers to use a VR heads et to conduct business with the financial institution and
interact with a GenAI powered virtual service representative from the comfort of their own home, at their convenience. These virtual agents are designed to provide conversational,
tailored responses to questions about customer accounts and financial needs. By this, the company aims to deliver a personali zed experience to customers while also reducing  the costs
associated with hiring and training additional human customer service workers. One of the key advantages of this virtual spac e is the ability to access customer data in real -time, allowing
the conversational agent to provide faster and higher -quality service and offerings.A VIRTUAL BANK EXPERIENCE
(Virtual Reality -Enabled Retail Banking Centers)
GOALS / BENEFITS
AFFECTED STAKEHOLDERS"TRANSPARENT AND EXPLAINABLE" TAI QUESTIONS* TO CONSIDER INCLUDE
MITIGATION STRATEGIES INCLUDE
TRUSTWORTHY AI CONSIDERATIONS
•Provide tailored responses to questions about customer accounts
and financial needs, enhancing clients’ experience
•Provide access to customer data in real -time.
•Provide faster and higher -quality service and offerings
•Reduce  costs associated with hiring and training additional human
customer service workers
•Free  up human customer service operatives to concentrate on the
delivery of more value -enhancing elements of their role
•Clients
•Human Customer Service Workers
•Government Agencies related to the retail banking industry
•Service or Product owner•Define clear policies that outline how customer or client data is collected, stored, and used within the virtual reality cust ome r agents'
program
•Decide on the level of transparency and explainability required for users and other stakeholders (e.g. system/product owners)  and take
measures to achieve this requirement  and update it as needed.
•Use simple language that provides explanations or justifications for the responses given by the virtual agent and disclose up front that the
technology is AI -drivenJUSTIFIABLE AUDITABLE
•What are the main inputs that influence the technology’s output or
recommendation?
•How does each input influence the technology’s output or
recommendation?•What processes will be in place to help enable traceability of responses?
•How are faults or feedback from users captured and actioned to
investigate and/or remediate concerns?
INTERPETABLE VISIBLE
•What level of transparency and explainability is appropriate to help
ensure users are adequately informed and understand the
technology is AI -driven?
•Are users able to adequately understand the technology, its data,
and its algorithms enough to trust them?•How can customers and clients understand how their inputs and
information are stored, accessed, and used by the technology?
•What are the procedures to communicate if something does go wrong?
* Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To  see a more
comprehensive TAI question guide that can be used as an additional resource, please click here  for Deloitte Trustworthy AI Questions Guide.

## 第 45 頁

©  2024 Deloitte Asia Pacific Services Limited 45 AP Generative AI QRM Guidelines FAIR AND IMPARTIAL
A large oil and gas company has developed a resume screening application to use in their Human Resources department. The appl ication is built on historical data, and it scans large
volumes of resumes at once and then recommends the best -matched resumes for specific roles to be interviewed. The application is intended to reduce human bias and save the human
resources department significant time helping the company fill roles faster than they have been able to before. Additionally,  the company can use the application to help them satisfy
immediate workforce needs. TALENT FINDER
(Resume screening application)
GOALS / BENEFITS
AFFECTED STAKEHOLDERS"FAIR AND IMPARTIAL" TAI QUESTIONS* TO CONSIDER INCLUDE
MITIGATION STRATEGIES INCLUDE
TRUSTWORTHY AI CONSIDERATIONS
•Reduce human bias in recommendations for the best -matched
resumes for specific roles
•Accelerates the overall hiring process , reducing costs and
improving the responsiveness of the process for applicants
•Job Applicants
•Hiring Managers
•HR Department
•Current Employees
•Government Agencies related to employment laws
•Non-governmental organizations focused on employment rights,
data privacy, etc. •Aim to use historical data that is representative of a diverse range of candidates such as gender, race, ethnicity, and educational background
when developing  the application. Evaluate  and address potential historic bias in data sets.
•Test the algorithms for bias
•Review  the recommendations made by the application to add a human in the loop
•Communicate to job applicants that their resumes will be screened using AI technology
•Consult with Risk, Privacy and OGC teams to align functionality with applicable regulations and employment lawsACCESSIBLE INCLUSIVE
•How has accessibility been considered when designing the
technology?•How does the technology factor in inclusivity in its decisions?
EQUITABLE UNBIASED
•Is differential treatment of groups justified by underlying factors?
Under what situations is partiality justified for the technology?
•What would stakeholders (including individuals affected by the
technology) consider to be equitable in relation to the technology's
outputs?•How has the potential impact of bias in the data been considered?
How are we minimizing bias in our data, algorithms and output?
•Can discriminatory bias, if any, be removed with data adjustments?
* Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To  see a more
comprehensive TAI question guide that can be used as an additional resource, please click here  for Deloitte Trustworthy AI Questions Guide.

## 第 46 頁

©  2024 Deloitte Asia Pacific Services Limited 46 AP Generative AI QRM Guidelines RESPONSIBLE
A public school organization has developed an app called “Education 2.0”. This app aims to provide hyper -personalized digital te achers to students, that can adapt to different learning
needs and curricula. With this app, students can receive a customized learning experience that is tailored to their strengths , weaknesses, and preferences.
The Education 2.0 App works by checking the student's work and comprehension and then adapting the lessons and learning strat egies accordingly. This means that students can receive
one-on-one attention from their personalized digital teacher to master new skills and knowledge. As a result, the human instruct or can focus on higher -level planning, interacting with
students, evaluation, and student support.EDUCATION 2.0
(Hyper -Personalized Education)
GOALS/  BENEFITS
AFFECTED STAKEHOLDERS“RESPONSIBLE" TAI QUESTIONS TO CONSIDER INCLUDE
MITIGATION STRATEGIES INCLUDE
TRUSTWORTHY AI CONSIDERATIONS
•Provide the students with personalized learning experiences,
optimizing their educational outcomes
•Supports teachers enabling them to focus on tasks that require their
expertise and personal interaction such as planning, interacting with
students, evaluation, and student support
•Students
•Teachers
•School Administrators
•Parents/Guardians
•Researchers or individuals with experience in the field of education
•Education Policy Makers•Monitor  the app's performance for alignment with responsible educational practices
•Develop training for students and teachers to use the app, including responsible use, privacy and ethical considerations, as well as the
knowledge teachers will need to guide the student in using the app responsibly
•Conduct consequence scanning to identify how the technology might have adverse or undesired impacts (including potential for misuse),
evaluate potential risks and apply mitigationsVALUE ADDING COMMON AND SOCIAL GOOD
•What difference will implementation of the technology make, and is
it enough to counter potential harms?
•Are there alternative or lower -risk ways to achieve the same aims?•Who is checking that the technology is behaving in a way that is positive
towards humanity on a routine basis?
•Are there potential adverse consequences of the technology and how
could these be mitigated?
•Could the technology be misused and how could this be mitigated?
SUSTAINABILITY FOCUSED HUMANE
•Does it align with our values?
•Could its purpose be misrepresented?
•Do you understand the environmental impact of the technology's
development, deployment and/or use? What have we considered to
help reduce that impact?•How can we take a responsible approach to integrating virtual
teachers/assistants/aids without losing the human element?
* Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To  see a more
comprehensive TAI question guide that can be used as an additional resource, please click here  for Deloitte Trustworthy AI Questions Guide.

## 第 47 頁

©  2024 Deloitte Asia Pacific Services Limited 47 AP Generative AI QRM Guidelines ACCOUNTABLE
A mining company has recently implemented a new tool called "A Helping Hand in the Field." This virtual field assistant provi des engineers with on -demand access to engineering
knowledge and support in problem -solving, improving efficiency, productivity, and decision -making capabilities.
On a day -to-day basis, this virtual field assistant serves as a reference tool, providing quick access to vast amounts of techni cal information. It also delivers relevant information and directs
engineers to appropriate resources. In addition to this, it helps with problem -solving by responding to questions about specific  engineering concepts, principles, or calculations.
When encountering concerns or challenges in the field, engineers can describe the problem to the virtual field assistant, and  it will return appropriate questions to identify the cause or
provide step -by-step guidance for resolution. This feature allows engineers to quickly troubleshoot concerns and minimize downti me.A HELPING HAND IN THE FIELD
(Virtual Field Assistant for Engineers)
GOALS / BENEFITS
AFFECTED STAKEHOLDERS"ACCOUNTABLE" TAI QUESTIONS TO CONSIDER INCLUDE
MITIGATION STRATEGIES INCLUDE
TRUSTWORTHY AI CONSIDERATIONS
•Empower  engineers with the knowledge and resources they need to
make accurate and effective decisions in the field
•Improve  efficiency, productivity, and problem -solving capabilities
•Engineers
•Supervisors
•Field Technicians
•Occupational Risk Prevention Team
•Government Agencies related to the mining industry•Establish clear guidelines and policies for the use of the virtual field assistant tool
•Provide comprehensive training to engineers on the responsible use of the virtual field assistant tool
•Implement access controls to help ensure that only authorized engineers can use the tool
•Help ensure processes are established to monitor for faults, remediate and learn
•Help ensure accountability is defined for relevant stakeholders throughout the lifecycle of the technologyOWNERSHIP ANSWERABLE
•Who is accountable for the technology throughout its lifecycle? Are
they equipped/enabled to fulfil their role for the technology?
•How is the system/use case monitored and how frequently throughout
its lifecycle?
•Who is accountable for protecting against model drift and ensuring
quality is maintained?•Who are the dedicated ‘first responders’ in the event of an emergency,
issue, or mistake originated by the technology? How are they equipped
to take on oversight responsibilities?
•Is there a dedicated team for users or other stakeholders to reach out to
with doubts or concerns about the technology?
RESOLVABLE
•Are there processes in place for when inconsistencies or concerns with
the technology are discovered?
* Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To  see a more
comprehensive TAI question guide that can be used as an additional resource, please click here  for Deloitte Trustworthy AI Questions Guide.

## 第 48 頁

©  2024 Deloitte Asia Pacific Services Limited 48 AP Generative AI QRM Guidelines ROBUST AND RELIABLE
A mid -size laboratory is set to implement a new technology system aimed at creating procedural templates, as well as recommendat ions on best practices (e.g., techniques, equipment,
etc.), to enhance laboratory processes. Leveraging historical data and scientific principles, this new system has the potenti al to suggest novel experimental designs, more efficient
processes, or alternate uses of equipment, thereby stimulating innovation in laboratory procedures. The system will analyze d ata from lab protocols, equipment specifications, previous
experimental designs, and techniques, providing a holistic understanding of laboratory procedures and principles. With this s ystem, the laboratory will be able to identify areas of
improvement in its processes and make data -driven decisions to optimize efficiency and productivity.OPTIMIZING LAB PROCEDURES
(Experimental Design)
GOALS / BENEFITS
AFFECTED STAKEHOLDERS"ROBUST AND RELIABLE" TAI QUESTIONS TO CONSIDER INCLUDE
MITIGATION STRATEGIES INCLUDE
TRUSTWORTHY AI CONSIDERATIONS
•Enhance laboratory processes
•Stimulate innovation in laboratory procedures
•Enable the laboratory to identify areas of improvement and make
data -driven decisions to optimize resource use
•Laboratory Staff
•Laboratory Management Team
•Patients
•Regulatory Bodies
•Quality Assurance and Accreditation Bodies •Perform human -in-the-loop controls to review the outputs, helping to ensure accuracy and quality
•Identify key business processes that will be impacted by the new technology system, and thoroughly assess the risks of harm t hat may occur
in the execution of those business processes
•Conduct testing and validation of the new technology system before its full implementation
•Establish a system for continuous monitoring and maintenance of the technology
•Realize recurrent meetings with laboratory staff to provide feedback on the system's performance and suggestions for improvem entCONSISTENT ADAPTABLE
•What are feedback loops looking to achieve and how will their impact be assessed and
monitored? What process will be in place when inconsistencies or concerns are
discovered?
•How stable is the model and how susceptible to model drift is it over time?•How frequently will models be validated or retrained?
•Will the model produce reliable results with new data
sets?
ACCURATE PREDICTABLE
•Are the outputs and recommendations stable and reliable over time, how has this been
assured in the design and testing and what ongoing quality controls may be required
through the operational life of the system?
•What is the consequence/impact of lower quality or inaccurate outputs?
•Are there situations where the technology should not be used, or where a factor of
safety should be applied to its recommendations?•What processes will you have to check the model for
incorrect, manipulated, or unauthorized output?
* Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To  see a more
comprehensive TAI question guide that can be used as an additional resource, please click here  for Deloitte Trustworthy AI Questions Guide.

## 第 49 頁

©  2024 Deloitte Asia Pacific Services Limited 49 AP Generative AI QRM Guidelines SAFE AND SECURE
A financial services company is in the process of adopting a new technology system to support corporate risk management activ ities, including those related to credit, investment, fraud,
and cybersecurity. This system enables real -time monitoring and verification for risk and fraud identification, which has a dire ct impact on operational efficiency and cost savings. The
ability to access relevant data and contextual information in real time supports compliance with regulations and industry sta ndards. By evaluating risks based on customer data, industry
data, and real -time updates, the organization can conduct better risk assessments with greater accuracy and impact.
The new system creates synthetic data that mirrors fraudulent transactions, which can help train models to better identify ri sky scenarios, predict fraudulent patterns, and ultimately
reduce the overall fraud rate.MITIGATING RISK AS IT ARISES
(Real -Time Risk Management)
GOALS / BENEFITS
AFFECTED STAKEHOLDERS"SAFE AND SECURE" TAI QUESTIONS TO CONSIDER INCLUDE
MITIGATION STRATEGIES INCLUDE
TRUSTWORTHY AI CONSIDERATIONS
•Enable real -time monitoring and verification for risk and fraud
identification
•Conduct more accurate risk assessments
•Reduce the overall fraud rate by enhancing the company's ability
to detect and prevent fraudulent activities
•Risk Management Team
•Clients
•Tech Team
•Regulatory bodies  •Implement strong data encryption measures to protect sensitive information within the system
•Implement multi -factor authentication for system access to enhance security
•Develop training for system users about safe and secure practices such as identifying phishing attempts, avoiding suspicious links or
attachments, and adhering to security protocols
•Regularly perform human controls to identify bias or potential harms and mitigate any discriminatory outcomes
•Include  consequence scanning to anticipate potential, physical or psychological harms that could arise due to the intended use or mis use of
the AI systemUSER FRIENDLY INVULNERABLE
•Is the technology intuitive for its intended users? •Is the technology aligned with the relevant global Technology
Operating Model (TOM) and cyber security standards and processes?
USER PROTECTION
•How are you ensuring that the AI solution is safe for its intended users
and society at large?
•Have you considered consequence scanning to anticipate potential,
physical or psychological harms that could arise due to the intended
use or misuse of the AI system?
* Please note that included in this deck are a few TAI questions to consider; however, these questions are not exhaustive. To  see a more
comprehensive TAI question guide that can be used as an additional resource, please click here  for Deloitte Trustworthy AI Questions Guide.

## 第 50 頁

©  2024 Deloitte Asia Pacific Services Limited 50 AP Generative AI QRM Guidelines
Contacts and References

## 第 51 頁

©  2024 Deloitte Asia Pacific Services Limited 51 AP Generative AI QRM Guidelines
©  2024 Deloitte Asia Pacific Services LimitedContacts and References
Contacts
•For any  questions or needs regarding Generative AI application, please reach out to local Generative AI Clearinghouse, AI
Institute or Market Incubator teams.
•For risk triage process and subsequent deep -dive assessment for market activation or service delivery transformation, each AP
PF and BU has identified the relevant BU QRM or risk point of contact as set out in the next slide to facilitate the process.
Resources and Additional Information
•Explore Deloitte Asia Pacific AI Institute: Asia Pacific AI Institute
•Explore Global Generative AI materials: Global Generative AI KX Page
•Explore Deloitte’s Trustworthy AI Framework:  Deloitte Trustworthy AI Framework Guidance and Questions Guide
•Explore Generative AI learning resources: Generative AI Fluency  and AP Gen AI Cura Group .
•Explore the globally available environments here: Generative AI Environments and Platforms Page
•Explore the Vendor Guardrails that have been put in place here: Generative AI
•Download the guide for Generative AI Independence Considerations here: Generative AI Independence

## 第 52 頁

©  2024 Deloitte Asia Pacific Services Limited 52 AP Generative AI QRM Guidelines Generative AI Risk Triage Process -BU QRM Point of Contact
Market Activation
PF A&A TL RA FA CON
AUAmanda Terry
Alicia Johnson (Audit)
Chloe Meyer Jacqui Hill Samantha Butler Aisling Ruane Jake Carnovale
CN Tony WongJenny Tsang
Cherie ChenYiyi Li David Tan Ma Jing
JPAkiko Kato Kawase Tetsuya
Ono NozomiTetsuya Ito Masanori KinamariNoriko Matsumori
Toshiya Hakoshima
KR Min Kyung Kim
NZ Mike Hawken Ian Fay Rhys Hermansson Scott McClay David Lovatt
SAChandrika Sridhar Samir Gandhi Murthi Kalaiselvi Krishna Chaturvedi Varun Rastogi
Sitaramprasad Sharma Raghavapudi
SEA None Wisesasari, Wisesasari (Seysi) Fazlin Hanoum Ilhan Christine Leong Lai Weng Yew
TW Vincent Cheng Gilbert Chiang Mike Chang Alan Wong Ann Lai
Service Delivery Transformation
PF A&A TL RA FA CON Enabling Areas
AUAmanda Terry
Alicia Johnson (Audit)
Chloe Meyer Jacqui Hill
Areeba WazahatSamantha Butler Aisling Ruane Jake Carnovale John Green
NinaYiannopoulos
CNTony Wong Jenny Tsang
Cherie ChenYiyi Li David Tan Ma JingJoanne Shuk Yee
Lee
JPAkiko Kato Kawase, Tetsuya
Ono, NozomiTetsuya Ito Masanori KinamariNoriko Matsumori
Toshiya HakoshimaTetsuya Ito
KR Kyung Jae Lee Shin Young Kang Sun Ah Choi Hyun Jeong Choi Seo Jeong Hong Jun Soo Kim
NZ Mike Hawken Ian Fay Rhys Hermansson Scott McClay David Lovatt Douglas Ah Poe
SAChandrika Sridhar Samir Gandhi Murthi Kalaiselvi Krishna ChaturvediVarun Rastogi
Kaushik DasguptaDebashish Banerjee
Sitaramprasad Sharma Raghavapudi
SEA None Wisesasari, Wisesasari (Seysi) Fazlin Hanoum Ilhan Christine Leong Lai Weng Yew None
TW Ricky Lin/Vita Yu
Note:  AP OGC contact persons for legal support (Enabling Area) - Constance Xin (AP OGC – China) and Hannah Szto (AP OGC – Australia)(as of May 2024, please refer to Generative AI under AP Risk Hub for the latest list

## 第 53 頁

©  2024 Deloitte Asia Pacific Services Limited 53 AP Generative AI QRM Guidelines
Deloitte refers to one or more of Deloitte Touche Tohmatsu Limited (“DTTL”), its global network of  member firms,
and their related entities (collectively, the “Deloitte organization”). DTTL (also referred to as “Deloitte Global”) and
each of its member firms and related entities are legally separate and independent entities, which cannot obligate or
bind each other in respect of third parties. DTTL and each DTTL member firm and related entity is liable only for its
own acts and omissions, and not those of each other. DTTL does not provide services to clients. Please see
www.deloitte.com/about  to learn more.
Deloitte Asia Pacific Limited is a company limited by guarantee and a member firm of DTTL. Members of Deloitte Asia
Pacific Limited and their related entities, each of which is a separate and independent legal entity, provide services
from more than 100 cities across the region, including Auckland, Bangkok, Beijing, Bengaluru, Hanoi, Hong Kong,
Jakarta, Kuala Lumpur, Manila, Melbourne, Mumbai, New Delhi, Osaka, Seoul, Shanghai, Singapore, Sydney, Taipei
and Tokyo.
This communication and any attachment to it is for internal distribution among personnel of the Deloitte organization.
It may contain confidential information and is intended solely for the use of the individual or entity to whom it is
addressed. If you are not the intended recipient, please notify us immediately by replying to this email and then
please delete this communication and all copies of it on your system. Please do not use this communication in any
way.
None of DTTL, its member firms, related entities, employees or agents shall be responsible for any loss or damage
whatsoever arising directly or indirectly in connection with any person relying on this communication.
©  2024 Deloitte Asia Pacific Services Limited

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增強版文件轉換工具依賴套件安裝程式
"""

import subprocess
import sys
import importlib
import platform

def install_package(package_name, import_name=None, description=""):
    """安裝 Python 套件"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安裝 {description}")
        return True
    except ImportError:
        print(f"📦 正在安裝 {package_name}... {description}")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} 安裝成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安裝失敗")
            return False

def check_system_requirements():
    """檢查系統需求"""
    print("🔍 檢查系統需求...")
    
    # 檢查Python版本
    python_version = sys.version_info
    if python_version < (3, 6):
        print(f"❌ Python版本過低: {python_version.major}.{python_version.minor}")
        print("   需要Python 3.6或更高版本")
        return False
    else:
        print(f"✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 檢查作業系統
    os_name = platform.system()
    print(f"✅ 作業系統: {os_name}")
    
    return True

def main():
    """主函數"""
    print("🔧 增強版文件轉換工具依賴套件安裝程式")
    print("=" * 60)
    
    # 檢查系統需求
    if not check_system_requirements():
        sys.exit(1)
    
    print("\n📦 安裝依賴套件...")
    
    # 需要安裝的套件列表
    packages = [
        ("python-docx", "docx", "- DOCX文件處理"),
        ("PyPDF2", "PyPDF2", "- PDF文件處理（基礎）"),
        ("pdfplumber", "pdfplumber", "- PDF文件處理（進階）"),
        ("python-pptx", "pptx", "- PPTX文件處理"),
        ("Pillow", "PIL", "- 圖片處理"),
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package_name, import_name, description in packages:
        if install_package(package_name, import_name, description):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"📊 安裝結果: {success_count}/{total_count} 套件安裝成功")
    
    if success_count == total_count:
        print("🎉 所有依賴套件安裝完成！")
        print("\n🚀 使用方法:")
        print("python document_converter_enhanced.py --help")
        print("\n✨ 主要功能:")
        print("- 智能表格處理")
        print("- 進階標題識別")
        print("- 頁頭頁尾自動移除")
        print("- 文字格式保留")
        print("- 更好的PDF處理")
        print("- 詳細轉換報告")
    else:
        print("⚠️  部分套件安裝失敗，請手動安裝:")
        for package_name, import_name, description in packages:
            try:
                importlib.import_module(import_name)
            except ImportError:
                print(f"pip install {package_name}")
        
        print("\n💡 提示:")
        print("- 確保網路連接正常")
        print("- 嘗試使用管理員權限運行")
        print("- 檢查pip版本: pip --version")
        print("- 升級pip: python -m pip install --upgrade pip")

if __name__ == "__main__":
    main()

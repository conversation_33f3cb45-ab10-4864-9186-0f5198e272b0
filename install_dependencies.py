#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安裝文件轉換工具所需的依賴套件
"""

import subprocess
import sys
import importlib

def install_package(package_name, import_name=None):
    """安裝 Python 套件"""
    if import_name is None:
        import_name = package_name
    
    try:
        importlib.import_module(import_name)
        print(f"✅ {package_name} 已安裝")
        return True
    except ImportError:
        print(f"📦 正在安裝 {package_name}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
            print(f"✅ {package_name} 安裝成功")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ {package_name} 安裝失敗")
            return False

def main():
    """主函數"""
    print("🔧 文件轉換工具依賴套件安裝程式")
    print("=" * 50)
    
    # 需要安裝的套件列表
    packages = [
        ("python-docx", "docx"),
        ("PyPDF2", "PyPDF2"),
        ("python-pptx", "pptx"),
    ]
    
    success_count = 0
    total_count = len(packages)
    
    for package_name, import_name in packages:
        if install_package(package_name, import_name):
            success_count += 1
    
    print("\n" + "=" * 50)
    print(f"📊 安裝結果: {success_count}/{total_count} 套件安裝成功")
    
    if success_count == total_count:
        print("🎉 所有依賴套件安裝完成！")
        print("\n使用方法:")
        print("python document_converter.py --help")
    else:
        print("⚠️  部分套件安裝失敗，請手動安裝:")
        for package_name, import_name in packages:
            try:
                importlib.import_module(import_name)
            except ImportError:
                print(f"pip install {package_name}")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件轉換工具
支援將 DOCX、PDF、PPTX 文件轉換為 Markdown 格式

功能特色：
- 支援批量轉換
- 增量處理（只處理新文件）
- 自動清理臨時文件
- 保留有用的文檔結構

依賴套件：
pip install python-docx PyPDF2 python-pptx pandoc-python-filters
"""

import os
import sys
import argparse
import hashlib
import json
from pathlib import Path
from datetime import datetime
import logging

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('converter.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DocumentConverter:
    """文件轉換器主類"""
    
    def __init__(self, input_dir="input", output_dir="output", cache_file="conversion_cache.json"):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.cache_file = Path(cache_file)
        self.cache = self.load_cache()
        
        # 創建目錄
        self.input_dir.mkdir(exist_ok=True)
        self.output_dir.mkdir(exist_ok=True)
        
        # 支援的文件類型
        self.supported_extensions = {'.docx', '.pdf', '.pptx'}
        
    def load_cache(self):
        """載入轉換快取"""
        if self.cache_file.exists():
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"無法載入快取文件: {e}")
        return {}
    
    def save_cache(self):
        """儲存轉換快取"""
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"無法儲存快取文件: {e}")
    
    def get_file_hash(self, file_path):
        """計算文件雜湊值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"無法計算文件雜湊值 {file_path}: {e}")
            return None
    
    def is_file_changed(self, file_path):
        """檢查文件是否已變更"""
        file_str = str(file_path)
        current_hash = self.get_file_hash(file_path)
        
        if file_str in self.cache:
            return self.cache[file_str].get('hash') != current_hash
        return True
    
    def convert_docx_to_md(self, docx_path, md_path):
        """轉換 DOCX 到 Markdown"""
        try:
            from docx import Document
            
            doc = Document(docx_path)
            markdown_content = []
            
            # 處理段落
            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    # 簡單的樣式處理
                    if paragraph.style.name.startswith('Heading'):
                        level = paragraph.style.name.replace('Heading ', '')
                        if level.isdigit():
                            markdown_content.append(f"{'#' * int(level)} {text}")
                        else:
                            markdown_content.append(f"## {text}")
                    else:
                        markdown_content.append(text)
                    markdown_content.append("")
            
            # 處理表格
            for table in doc.tables:
                markdown_content.append("")
                for i, row in enumerate(table.rows):
                    row_data = [cell.text.strip() for cell in row.cells]
                    markdown_content.append("| " + " | ".join(row_data) + " |")
                    if i == 0:  # 標題行
                        markdown_content.append("| " + " | ".join(["---"] * len(row_data)) + " |")
                markdown_content.append("")
            
            # 寫入文件
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(markdown_content))
            
            return True
            
        except ImportError:
            logger.error("請安裝 python-docx: pip install python-docx")
            return False
        except Exception as e:
            logger.error(f"轉換 DOCX 失敗 {docx_path}: {e}")
            return False
    
    def convert_pdf_to_md(self, pdf_path, md_path):
        """轉換 PDF 到 Markdown"""
        try:
            import PyPDF2
            
            markdown_content = []
            
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages, 1):
                    text = page.extract_text()
                    if text.strip():
                        markdown_content.append(f"## 第 {page_num} 頁")
                        markdown_content.append("")
                        
                        # 簡單的文字處理
                        lines = text.split('\n')
                        for line in lines:
                            line = line.strip()
                            if line:
                                markdown_content.append(line)
                        
                        markdown_content.append("")
            
            # 寫入文件
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(markdown_content))
            
            return True
            
        except ImportError:
            logger.error("請安裝 PyPDF2: pip install PyPDF2")
            return False
        except Exception as e:
            logger.error(f"轉換 PDF 失敗 {pdf_path}: {e}")
            return False
    
    def convert_pptx_to_md(self, pptx_path, md_path):
        """轉換 PPTX 到 Markdown"""
        try:
            from pptx import Presentation
            
            prs = Presentation(pptx_path)
            markdown_content = []
            
            for slide_num, slide in enumerate(prs.slides, 1):
                markdown_content.append(f"## 投影片 {slide_num}")
                markdown_content.append("")
                
                # 處理文字框
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        text = shape.text.strip()
                        # 簡單判斷是否為標題
                        if len(text) < 100 and '\n' not in text:
                            markdown_content.append(f"### {text}")
                        else:
                            for line in text.split('\n'):
                                if line.strip():
                                    markdown_content.append(f"- {line.strip()}")
                        markdown_content.append("")
                
                markdown_content.append("---")
                markdown_content.append("")
            
            # 寫入文件
            with open(md_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(markdown_content))
            
            return True
            
        except ImportError:
            logger.error("請安裝 python-pptx: pip install python-pptx")
            return False
        except Exception as e:
            logger.error(f"轉換 PPTX 失敗 {pptx_path}: {e}")
            return False
    
    def convert_file(self, file_path):
        """轉換單個文件"""
        file_path = Path(file_path)
        extension = file_path.suffix.lower()
        
        if extension not in self.supported_extensions:
            logger.warning(f"不支援的文件類型: {extension}")
            return False
        
        # 生成輸出文件名
        md_path = self.output_dir / f"{file_path.stem}.md"
        
        # 檢查是否需要轉換
        if not self.is_file_changed(file_path):
            logger.info(f"文件未變更，跳過: {file_path.name}")
            return True
        
        logger.info(f"開始轉換: {file_path.name}")
        
        # 根據文件類型選擇轉換方法
        success = False
        if extension == '.docx':
            success = self.convert_docx_to_md(file_path, md_path)
        elif extension == '.pdf':
            success = self.convert_pdf_to_md(file_path, md_path)
        elif extension == '.pptx':
            success = self.convert_pptx_to_md(file_path, md_path)
        
        if success:
            # 更新快取
            file_hash = self.get_file_hash(file_path)
            self.cache[str(file_path)] = {
                'hash': file_hash,
                'converted_at': datetime.now().isoformat(),
                'output_file': str(md_path)
            }
            logger.info(f"轉換成功: {file_path.name} -> {md_path.name}")
        else:
            logger.error(f"轉換失敗: {file_path.name}")
        
        return success
    
    def convert_all(self):
        """轉換所有支援的文件"""
        converted_count = 0
        failed_count = 0
        
        # 尋找所有支援的文件
        for extension in self.supported_extensions:
            for file_path in self.input_dir.glob(f"*{extension}"):
                if self.convert_file(file_path):
                    converted_count += 1
                else:
                    failed_count += 1
        
        # 儲存快取
        self.save_cache()
        
        logger.info(f"轉換完成: 成功 {converted_count} 個，失敗 {failed_count} 個")
        return converted_count, failed_count
    
    def cleanup_temp_files(self):
        """清理臨時文件"""
        temp_patterns = ['*.tmp', '~$*', '.~*']
        cleaned_count = 0
        
        for pattern in temp_patterns:
            for temp_file in self.input_dir.glob(pattern):
                try:
                    temp_file.unlink()
                    cleaned_count += 1
                    logger.info(f"清理臨時文件: {temp_file.name}")
                except Exception as e:
                    logger.warning(f"無法清理臨時文件 {temp_file}: {e}")
        
        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 個臨時文件")
        
        return cleaned_count


def main():
    """主函數"""
    parser = argparse.ArgumentParser(
        description='文件轉換工具 - 將 DOCX、PDF、PPTX 轉換為 Markdown',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用範例:
  python document_converter.py                    # 轉換 input 目錄中的所有文件
  python document_converter.py -i docs -o md     # 指定輸入和輸出目錄
  python document_converter.py -f file.docx      # 轉換單個文件
  python document_converter.py --cleanup         # 清理臨時文件
        """
    )
    
    parser.add_argument('-i', '--input', default='input', help='輸入目錄 (預設: input)')
    parser.add_argument('-o', '--output', default='output', help='輸出目錄 (預設: output)')
    parser.add_argument('-f', '--file', help='轉換單個文件')
    parser.add_argument('--cleanup', action='store_true', help='清理臨時文件')
    parser.add_argument('--force', action='store_true', help='強制重新轉換所有文件')
    
    args = parser.parse_args()
    
    # 創建轉換器
    converter = DocumentConverter(args.input, args.output)
    
    # 清理臨時文件
    if args.cleanup:
        converter.cleanup_temp_files()
    
    # 強制重新轉換
    if args.force:
        converter.cache = {}
    
    try:
        if args.file:
            # 轉換單個文件
            file_path = Path(args.file)
            if not file_path.exists():
                logger.error(f"文件不存在: {args.file}")
                sys.exit(1)
            
            success = converter.convert_file(file_path)
            converter.save_cache()
            
            if success:
                logger.info("文件轉換成功！")
            else:
                logger.error("文件轉換失敗！")
                sys.exit(1)
        else:
            # 轉換所有文件
            converted, failed = converter.convert_all()
            
            if failed > 0:
                logger.warning(f"有 {failed} 個文件轉換失敗")
                sys.exit(1)
            else:
                logger.info("所有文件轉換成功！")
    
    except KeyboardInterrupt:
        logger.info("轉換被使用者中斷")
        sys.exit(1)
    except Exception as e:
        logger.error(f"轉換過程發生錯誤: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

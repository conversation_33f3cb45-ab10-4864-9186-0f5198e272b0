@echo off
chcp 65001 >nul
echo ========================================
echo 文件轉換工具 v2.0 - PDF/DOCX/PPTX to Markdown
echo ========================================
echo.

REM 檢查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [錯誤] 找不到Python，請確認已安裝Python 3.6+
    pause
    exit /b 1
)

REM 檢查並安裝依賴套件
echo [資訊] 檢查依賴套件...
python install_dependencies_v2.py

if %errorlevel% neq 0 (
    echo [警告] 依賴套件安裝可能有問題
    echo.
)

REM 創建必要的目錄
if not exist "input" (
    mkdir input
    echo [資訊] 已創建 input 目錄
)

if not exist "output" (
    mkdir output
    echo [資訊] 已創建 output 目錄
)

if not exist "images" (
    mkdir images
    echo [資訊] 已創建 images 目錄
)

echo.
echo [資訊] v2.0 新功能特色:
echo ✅ 格式結構完整還原（標題、清單、表格、圖片）
echo ✅ 特殊內容處理（PDF分欄、頁眉頁腳，DOCX批註腳註）
echo ✅ UTF-8編碼與中文字元正確處理
echo ✅ 圖片另存並以Markdown語法引用
echo ✅ 表格轉為標準Markdown表格
echo ✅ 完整錯誤處理與日誌記錄
echo ✅ 支援批次處理與自動命名
echo.

REM 檢查input目錄是否有文件
set file_count=0
for %%f in (input\*.pdf input\*.docx input\*.pptx) do (
    set /a file_count+=1
)

if %file_count% equ 0 (
    echo [提示] input 目錄中沒有找到支援的文件
    echo 請將 PDF、DOCX 或 PPTX 文件放入 input 目錄中
    echo.
    echo 按任意鍵打開 input 目錄...
    pause >nul
    start input
    exit /b 0
)

echo [資訊] 找到 %file_count% 個文件待轉換
echo.

REM 詢問轉換選項
echo 轉換選項:
echo 1. 批次轉換所有文件
echo 2. 清理臨時文件後轉換
echo 3. 只清理臨時文件
echo 4. 詳細模式轉換 (顯示更多信息)
echo.
set /p choice="請選擇 (1-4): "

echo.
echo [資訊] 開始處理...

if "%choice%"=="1" (
    echo [模式] 批次轉換
    python document_converter_v2.py
) else if "%choice%"=="2" (
    echo [模式] 清理後轉換
    python document_converter_v2.py --cleanup
    if %errorlevel% equ 0 (
        python document_converter_v2.py
    )
) else if "%choice%"=="3" (
    echo [模式] 只清理臨時文件
    python document_converter_v2.py --cleanup
) else if "%choice%"=="4" (
    echo [模式] 詳細模式轉換
    python document_converter_v2.py --verbose
) else (
    echo [預設] 批次轉換
    python document_converter_v2.py
)

if %errorlevel% equ 0 (
    echo.
    echo [成功] 轉換完成！
    echo.
    echo 📁 轉換結果保存在 output 目錄中
    echo 🖼️  圖片文件保存在 images 目錄中
    echo 📋 詳細報告: output\conversion_summary.md
    echo 📊 轉換記錄: output\conversion_report.json
    echo 📝 日誌文件: document_converter_v2.log
    echo.
    echo v2.0 改進特色:
    echo ✅ 更好的文字分離和段落處理
    echo ✅ 智能表格格式化和結構保持
    echo ✅ 自動頁眉頁腳識別和移除
    echo ✅ 標題層級自動判斷
    echo ✅ 列表結構正確轉換
    echo ✅ 圖片提取和引用
    echo ✅ 完整的錯誤處理和報告
    echo.
    set /p open_output="是否要打開 output 目錄查看結果？(y/n): "
    if /i "%open_output%"=="y" (
        start output
    )
    
    set /p open_report="是否要查看轉換報告？(y/n): "
    if /i "%open_report%"=="y" (
        if exist "output\conversion_summary.md" (
            start output\conversion_summary.md
        )
    )
) else (
    echo.
    echo [錯誤] 轉換過程中發生錯誤
    echo 請檢查 document_converter_v2.log 文件查看詳細錯誤信息
    echo.
    echo 常見問題解決:
    echo 1. 確認文件沒有被其他程式開啟
    echo 2. 檢查文件是否損壞或密碼保護
    echo 3. 確認有足夠的磁碟空間
    echo 4. 重新安裝依賴套件: python install_dependencies_v2.py
    echo 5. 檢查文件編碼是否正確
    echo.
    set /p open_log="是否要查看錯誤日誌？(y/n): "
    if /i "%open_log%"=="y" (
        if exist "document_converter_v2.log" (
            start document_converter_v2.log
        )
    )
)

echo.
echo 按任意鍵退出...
pause >nul

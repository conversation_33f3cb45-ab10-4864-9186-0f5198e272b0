@echo off
chcp 65001 >nul
title 政策文件轉換工具 V2.0 - 增量處理版

echo.
echo ========================================
echo 🚀 政策文件轉換工具 V2.0
echo ========================================
echo.
echo ✨ V2.0 新功能:
echo   ⚡ 增量處理 - 只轉換新檔案
echo   📝 智能追蹤 - 自動記錄已處理檔案  
echo   🧹 自動清理 - 移除無用檔案
echo   📁 自動歸檔 - 新檔案自動歸檔
echo.
echo 📋 使用說明:
echo   - 將新的政策文件放在當前目錄或 "ref Policy" 資料夾
echo   - 系統會自動偵測並只處理新檔案
echo   - 大幅節省處理時間，提高效率
echo.

:menu
echo 請選擇執行模式:
echo [1] 增量處理 (推薦) - 只處理新檔案
echo [2] 強制處理 - 重新處理所有檔案
echo [3] 僅清理 - 只清理無用檔案
echo [4] 退出
echo.
set /p choice="請輸入選項 (1-4): "

if "%choice%"=="1" goto incremental
if "%choice%"=="2" goto force_all
if "%choice%"=="3" goto clean_only
if "%choice%"=="4" goto exit
echo 無效選項，請重新選擇
goto menu

:incremental
echo.
echo ⚡ 執行增量處理模式...
python setup_policy_conversion_v2.py
goto result

:force_all
echo.
echo ⚠️ 執行強制處理模式 (將重新處理所有檔案)...
python setup_policy_conversion_v2.py --force-all
goto result

:clean_only
echo.
echo 🧹 執行清理模式...
python setup_policy_conversion_v2.py --clean-only
goto result

:result
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo ✅ 處理完成！
    echo ========================================
    echo.
    echo 📁 結果位置:
    echo   - 原始文件: "ref Policy" 資料夾
    echo   - 轉換結果: "md Policy" 資料夾
    echo   - 摘要報告: conversion_summary.md
    echo   - 詳細日誌: policy_conversion_v2.log
    echo   - 處理記錄: processed_files.json
    echo.
    echo 💡 下次使用:
    echo   - 只需將新檔案放入當前目錄或 "ref Policy"
    echo   - 執行增量處理即可，系統會自動偵測新檔案
    echo.
) else (
    echo.
    echo ========================================
    echo ❌ 處理失敗
    echo ========================================
    echo.
    echo 請檢查以下項目:
    echo 1. 查看 policy_conversion_v2.log 了解錯誤詳情
    echo 2. 確認 Python 環境正常
    echo 3. 檢查檔案格式是否正確
    echo.
)

echo 按任意鍵返回選單...
pause >nul
goto menu

:exit
echo 再見！
exit /b 0
